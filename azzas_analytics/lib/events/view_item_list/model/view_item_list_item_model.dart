import 'package:firebase_analytics/firebase_analytics.dart';

class ViewItemListItemModel {
  final double? price;
  final int? quantity;
  final String? itemRef;
  final int? index;
  final String? itemBrand;
  final String? itemName;
  final String? itemCategory;
  final String? itemVariant;
  final String? itemCategory2;
  final String? itemId;
  final double? discount;
  final String? itemListName;

  ViewItemListItemModel({
    this.price,
    this.quantity,
    this.itemRef,
    this.index,
    this.itemBrand,
    this.itemName,
    this.itemCategory,
    this.itemVariant,
    this.itemCategory2,
    this.itemId,
    this.discount,
    this.itemListName,
  });

  Map<String, dynamic> toJson() {
    return {
      'price': price,
      'quantity': quantity,
      'item_ref': itemRef,
      'index': index,
      'item_brand': itemBrand,
      'item_name': itemName,
      'item_category': itemCategory,
      'item_variant': itemVariant,
      'item_category2': itemCategory2,
      'item_id': itemId,
      'discount': discount,
      'item_list_name': itemListName,
    };
  }

  AnalyticsEventItem toAnalyticsEventItem() {
    return AnalyticsEventItem(
      itemId: itemId,
      itemName: itemName,
      itemBrand: itemBrand,
      itemCategory: itemCategory,
      itemCategory2: itemCategory2,
      itemVariant: itemVariant,
      index: index,
      quantity: quantity,
      price: price,
      discount: discount,
      itemListName: itemListName,
      parameters: itemRef != null ? {'item_ref': itemRef!} : null,
    );
  }
}
