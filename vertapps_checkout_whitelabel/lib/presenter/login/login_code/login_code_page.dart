import 'dart:async';

import 'package:vertapps_checkout_whitelabel/components/components.dart';
import 'package:azzas_analytics/events/account/account_events.dart';
import 'package:azzas_analytics/events/navigation/navigation_events.dart';
import 'package:azzas_analytics/services/user_property_service.dart';
import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:flutter/material.dart';
import 'package:vertapps_checkout_whitelabel/components/templates/cds_base_template.dart';
import 'package:vertapps_checkout_whitelabel/components/templates/login/cds_login_code_template.dart';
import 'package:vertapps_checkout_whitelabel/presenter/login/login_biometric/widget/ask_biometric_bottom_sheet.dart';

class LoginCodePage extends CdsBaseStatefulTemplate {
  const LoginCodePage({
    required super.tokens,
    super.key,
    required this.email,
    required this.token,
    required this.newPassword,
    this.isRegister = false,
    this.overrideFinishFlow,
    this.fromCreatePassword,
  });

  final String email;
  final String token;
  final String? newPassword;
  final bool? isRegister;
  final Function()? overrideFinishFlow;
  final bool? fromCreatePassword;

  @override
  State<LoginCodePage> createState() => _LoginCodePageState();
}

class _LoginCodePageState extends State<LoginCodePage> {
  final _pinPutController = TextEditingController();
  final _pinPutFocusNode = FocusNode();
  final loginCodeCubit = Modular.get<LoginCodeCubit>();
  final authCubit = Modular.get<AuthCubit>();

  @override
  void initState() {
    NavigationEvents.logPageView(local: 'login_code');
    _pinPutFocusNode.requestFocus();

    loginCodeCubit.startTimer();

    _pinPutController.addListener(() {
      _validatePinForm();
    });

    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
    _pinPutController.dispose();
    _pinPutFocusNode.dispose();

    if (loginCodeCubit.state.timer != null) {
      loginCodeCubit.cancel();
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<LoginCodeCubit, LoginCodeState>(
      bloc: loginCodeCubit,
      builder: (context, state) {
        return Scaffold(
          body: SafeArea(
            child: SingleChildScrollView(
              child: CdsLoginCodeTemplate(
                tokens: widget.tokens,
                controller: _pinPutController,
                onBackButton: () {
                  Navigator.of(context).pop();
                },
                pinFocusNode: _pinPutFocusNode,
                enterAnotherEmailButton: () {
                  Navigator.of(context).pop();
                },
                onComplete: (code) async {
                  if (widget.newPassword != null &&
                      widget.newPassword!.isNotEmpty) {
                    await _resetPassword(code);
                    return;
                  }

                  await _validateCode(code);
                },
                resendCodeButton: _resendCode,
                emailUser: widget.email,
                secondsRemaining: state.secondsRemaining,
                loading: state.isLoading,
                hasError: state.hasError,
              ),
            ),
          ),
        );
      },
    );
  }

  Future<void> _resendCode() async {
    await loginCodeCubit.resendCode(widget.email);
    await AccountEvents.logEmailVerification(
        local: 'login_code', action: "codigo-reenviar");
    loginCodeCubit.setError(false);
    _pinPutController.text = "";
  }

  void _showSnackBar({required BuildContext context, required String message}) {
    CdsSnackBar.show(
      context: context,
      tokens: widget.tokens,
      text: message,
      type: SnackbarType.success,
      onPressButton: () {
        CdsSnackBar.hideSnackBar();
      },
    );
  }

  Future<void> _resetPassword(String code) async {
    final email = widget.email;
    final password = widget.newPassword;

    try {
      loginCodeCubit.setIsLoading(true);
      final response = await loginCodeCubit.setPassword(
        code: code,
        email: email,
        password: password!,
        token: widget.token,
      );
      if (response != null) {
        await authCubit.loginSuccess(
          tokenResponse: response,
          userEmail: email,
          loginType: LoginType.email,
        );
      }
      if (authCubit.state.isLoggedIn && mounted) {
        _showSnackBar(context: context, message: 'Senha alterada com sucesso');

        if (widget.fromCreatePassword == true) {
          await UserPropertyService.setUserEmailProperty(email);
          Modular.to.pushReplacementNamed(
            '/checkout/create_password_success',
            arguments: {
              "overrideFinishFlow": widget.overrideFinishFlow,
            },
          );
          return;
        }

        if (authCubit.state.hasBiometrics) {
          Modular.to.navigate('/');
          return;
        }

        _showBottomSheet('/');
      }
    } catch (e) {
      loginCodeCubit.setError(true);
      debugPrint("Error in resetPassword: $e");
    } finally {
      loginCodeCubit.setIsLoading(false);
    }
  }

  Future<void> _validateCode(String code) async {
    try {
      loginCodeCubit.setIsLoading(true);
      final response = await loginCodeCubit.validateCode(
        email: widget.email,
        code: code,
        token: widget.token,
      );
      if (response != null) {
        await authCubit.loginSuccess(
          tokenResponse: response,
          userEmail: widget.email,
          loginType: LoginType.email,
        );
      }
      if (authCubit.state.isLoggedIn && mounted) {
        if (widget.overrideFinishFlow != null) {
          widget.overrideFinishFlow!();

          return;
        }

        _showSnackBar(context: context, message: 'Login efetuado com sucesso');
        if (authCubit.state.localUserInfo?.personEmail.isNotNullOrEmpty ??
            false) {
          await UserPropertyService.setUserEmailProperty(
              authCubit.state.localUserInfo?.personEmail ?? '');
        }

        String route =
            widget.isRegister == true ? '/checkout/complete_user_data' : '/';

        if (widget.isRegister != true) {
          AccountEvents.logLogin(
              local: 'login_code', method: 'login_validate_code');
        } else {
          AccountEvents.logSignUpMethod(
              local: 'login_code', method: 'cadastro_validate_code');
        }

        if (authCubit.state.hasBiometrics) {
          Modular.to.navigate(route, arguments: {
            'isRegister': widget.isRegister ?? false,
            "overrideFinishFlow": widget.overrideFinishFlow,
          });
          return;
        }

        _showBottomSheet('/');

        return;
      }
      return loginCodeCubit.setError(true);
    } catch (e) {
      loginCodeCubit.setError(true);
      debugPrint("Error in validateCode: $e");
    } finally {
      loginCodeCubit.setIsLoading(false);
    }
  }

  void _validatePinForm() {
    final isPin = _pinPutController.text.isEmpty;

    if (isPin) {
      loginCodeCubit.setError(false);
    }
  }

  void _showBottomSheet(String? route) {
    showModalBottomSheet(
      context: context,
      isDismissible: true,
      enableDrag: false,
      builder: (BuildContext context) {
        return AskBiometricBottomSheet(
          tokens: widget.tokens,
          route: route,
          cubit: authCubit,
        );
      },
    );
  }
}
