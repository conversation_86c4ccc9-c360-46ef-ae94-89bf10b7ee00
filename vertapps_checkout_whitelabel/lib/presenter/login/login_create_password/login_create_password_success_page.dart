import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:flutter/material.dart';
import 'package:vertapps_checkout_whitelabel/presenter/feedback/controller/feedback_cubit.dart';
import 'package:vertapps_checkout_whitelabel/presenter/feedback/controller/feedback_state.dart';
import 'package:vertapps_checkout_whitelabel/presenter/login/widgets/cds_login_background.dart';
import 'package:vertapps_checkout_whitelabel/vertapps_checkout_whitelabel.dart';

class LoginCreatePasswordSuccessPage extends CdsBaseStatefulComponent {
  const LoginCreatePasswordSuccessPage({
    super.key,
    required super.tokens,
    this.type,
    this.overrideFinishFlow,
  });

  final LoginTemplateType? type;
  final Function()? overrideFinishFlow;

  @override
  State<LoginCreatePasswordSuccessPage> createState() => _FeedbackPageState();
}

class _FeedbackPageState extends State<LoginCreatePasswordSuccessPage> {
  final cubit = FeedbackCubit();

  @override
  void initState() {
    super.initState();
    Future.delayed(const Duration(seconds: 3), () {
      Modular.to.popAndPushNamed(
        '/checkout/login_email_password',
        arguments: {
          "overrideFinishFlow": widget.overrideFinishFlow,
        },
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<FeedbackCubit, FeedbackState>(
      bloc: cubit,
      builder: (context, state) {
        return Scaffold(
          body: CdsLoginBackground(
            type: widget.type ?? LoginTemplateType.fullImage,
            child: (inverse) {
              final color = inverse
                  ? widget.tokens.colors.content.inverse
                  : widget.tokens.colors.content.pure;

              return Column(
                children: [
                  CdsTopBar(
                    tokens: widget.tokens,
                    config: CdsTopBarConfig(
                      type: CdsTopBarType.small,
                      padding: EdgeInsets.zero,
                      backgroundColor: Colors.transparent,
                      inverse: inverse,
                      pinned: true,
                      onBackButton: () {
                        Navigator.of(context).pop();
                      },
                    ),
                  ),
                  SizedBox(
                    height: widget.tokens.layout.spacing.xxl,
                  ),
                  Icon(
                    widget.tokens.icons.checkCircleFilled,
                    size: 120,
                    color: color,
                  ),
                  Padding(
                    padding: EdgeInsets.symmetric(
                      horizontal: widget.tokens.layout.padding.xl,
                    ),
                    child: Text(
                      'Nova senha registrada com sucesso',
                      style: widget.tokens.typography.subtitleStyles.subtitle2
                          .copyWith(
                        color: color,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
              );
            },
          ),
        );
      },
    );
  }
}
