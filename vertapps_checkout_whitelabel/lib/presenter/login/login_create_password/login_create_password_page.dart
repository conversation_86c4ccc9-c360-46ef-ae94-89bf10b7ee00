import 'package:azzas_analytics/events/account/account_events.dart';
import 'package:azzas_analytics/events/navigation/navigation_events.dart';
import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:flutter/material.dart';
import 'package:vertapps_checkout_whitelabel/components/templates/cds_base_template.dart';
import 'package:vertapps_checkout_whitelabel/components/templates/login/cds_login_create_password.dart';

class LoginCreatePasswordPage extends CdsBaseStatefulTemplate {
  const LoginCreatePasswordPage({
    super.key,
    required super.tokens,
    this.email,
    this.overrideFinishFlow,
  });

  final String? email;
  final Function()? overrideFinishFlow;

  @override
  State<LoginCreatePasswordPage> createState() =>
      _LoginCreatePasswordPageState();
}

class _LoginCreatePasswordPageState extends State<LoginCreatePasswordPage> {
  final _loginCreatePasswordCubit = Modular.get<LoginCreatePasswordCubit>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();

  final _passwordFocusNode = FocusNode();
  final _confirmPasswordFocusNode = FocusNode();

  @override
  void initState() {
    NavigationEvents.logPageView(local: 'login_create_password');
    _emailController.text = widget.email ?? '';

    if (widget.email != null && widget.email!.isNotEmpty) {
      _loginCreatePasswordCubit.validateEmail(widget.email!);
    }

    super.initState();
  }

  @override
  void dispose() {
    super.dispose();

    _passwordFocusNode.dispose();
    _confirmPasswordFocusNode.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _loginCreatePasswordCubit.resetState();
  }

  void _validateForm() {
    _loginCreatePasswordCubit.validateForm(
      email: _emailController.text,
      password: _passwordController.text,
      confirmedPassword: _confirmPasswordController.text,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: SingleChildScrollView(
          child:
              BlocBuilder<LoginCreatePasswordCubit, LoginCreatePasswordState>(
            bloc: _loginCreatePasswordCubit,
            builder: (context, state) {
              return CdsLoginCreatePasswordTemplate(
                tokens: widget.tokens,
                emailController: _emailController,
                passwordController: _passwordController,
                confimedPasswordController: _confirmPasswordController,
                obscureText: state.passwordInputState.isObscure,
                onToggleObscure: () {
                  _loginCreatePasswordCubit.toggleObscurePassword(
                    isFromConfirmPassword: false,
                  );
                },
                confirmObscureText: state.confirmPasswordInputState.isObscure,
                onToggleConfirmObscure: () {
                  _loginCreatePasswordCubit.toggleObscurePassword(
                    isFromConfirmPassword: true,
                  );
                },
                onPressedButton: () async {
                  await _onTapCreatePassword();
                },
                onBackButton: () => Navigator.of(context).pop(),
                isDisabled: !state.isFormValid,
                hasError: state.hasPasswordError,
                onPasswordChanged: (password) {
                  _loginCreatePasswordCubit.validatePassword(password, false);
                  _validateForm();
                },
                onConfirmPasswordChanged: (password) {
                  _loginCreatePasswordCubit.validatePassword(password, true);
                  _validateForm();
                },
                onEmailChanged: (email) {
                  _loginCreatePasswordCubit.validateEmail(email);
                  _validateForm();
                },
                isLoading: state.isLoading,
              );
            },
          ),
        ),
      ),
    );
  }

  Future<void> _onTapCreatePassword() async {
    await AccountEvents.logSignUpStep(
        local: 'login_create_password', step: 'criar-senha');
    final token = await _loginCreatePasswordCubit.createPassword(
      email: _emailController.text,
      password: _passwordController.text,
      confirmedPassword: _confirmPasswordController.text,
    );

    if (token != null) {
      Modular.to.pushNamed(
        '/checkout/login_code',
        arguments: {
          'email': _emailController.text,
          'token': token,
          'newPassword': _passwordController.text,
          'fromCreatePassword': true,
          "overrideFinishFlow": widget.overrideFinishFlow,
        },
      );
    }
  }
}
