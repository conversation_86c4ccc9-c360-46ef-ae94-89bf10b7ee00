import 'package:azzas_analytics/events/navigation/navigation_events.dart';
import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:flutter/material.dart';
import 'package:vertapps_checkout_whitelabel/components/templates/cds_base_template.dart';
import 'package:vertapps_checkout_whitelabel/components/templates/login/cds_complete_user_data_template.dart';
import 'package:vertapps_checkout_whitelabel/components/components.dart';

class CompleteUserDataPage extends CdsBaseStatefulTemplate {
  const CompleteUserDataPage({
    this.isRegister = false,
    this.overrideFinishFlow,
    required super.tokens,
    required this.privacyPolicyLink,
    super.key,
  });

  final bool? isRegister;
  final Function()? overrideFinishFlow;
  final String privacyPolicyLink;

  @override
  State<CompleteUserDataPage> createState() => _CompleteUserDataPageState();
}

class _CompleteUserDataPageState extends State<CompleteUserDataPage> {
  final personalDataModel = PersonalDataModel();
  final authCubit = Modular.get<AuthCubit>();
  final completeUserDataCubit = Modular.get<CompleteUserDataCubit>();
  final _eventDispatcher = Modular.get<EventDispatcher>();

  bool isSaveButtonDisabled = true;
  bool receiveSMS = false;
  bool receiveEmail = false;
  late bool hasUserInfo;

  @override
  void initState() {
    NavigationEvents.logPageView(local: 'complete_user_data');
    super.initState();
    _onInit();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocConsumer<CompleteUserDataCubit, CompleteUserDataState>(
        bloc: completeUserDataCubit,
        listener: (context, state) {
          if (state.success) {
            if (widget.overrideFinishFlow != null) {
              widget.overrideFinishFlow!();
              return;
            }

            if (widget.isRegister == true) {
              // MainPage.goMyAccount();
              Modular.to.navigate("/");
            }

            CdsSnackBar.show(
              context: context,
              text: "Dados salvos com sucesso",
              type: SnackbarType.success,
              tokens: widget.tokens,
              onPressButton: () {
                CdsSnackBar.hideSnackBar();
              },
            );

            _updateUserInfo();

            return;
          }

          if (state.error) {
            CdsSnackBar.show(
              context: context,
              text: 'Desculpe, um erro inesperado aconteceu',
              type: SnackbarType.error,
              showIcon: false,
              showButton: false,
              tokens: widget.tokens,
            );
          }
        },
        builder: (context, state) {
          return SingleChildScrollView(
            child: SafeArea(
              child: BlocBuilder<AuthCubit, AuthState>(
                bloc: authCubit,
                builder: (context, authState) {
                  final email = authState.localUserInfo?.personEmail;

                  personalDataModel.emailController.text = email ?? '';

                  return CdsCompleteUserDataTemplate(
                    tokens: widget.tokens,
                    cpfController: personalDataModel.cpfController,
                    nameController: personalDataModel.nameController,
                    surnameController: personalDataModel.lastNameController,
                    phoneController: personalDataModel.phoneController,
                    birthDateController: personalDataModel.birthDateController,
                    onChanged: _validateFields,
                    onPressedButton: _completeUserData,
                    onBackButton: () {
                      Navigator.of(context).pop();
                    },
                    enterAnotherEmailButton: () {
                      Modular.to.pushNamed(
                        '/checkout/login_email',
                        arguments: {
                          "overrideFinishFlow": widget.overrideFinishFlow,
                        },
                      );
                    },
                    emailUser: email ?? '',
                    fistCheckbox: receiveEmail,
                    secondCheckbox: receiveSMS,
                    ontapFistCheckbox: (value) {
                      receiveEmail = value;
                      _validateFields();
                    },
                    ontapSecondCheckbox: (value) {
                      receiveSMS = value;
                      _validateFields();
                    },
                    isDisabled: isSaveButtonDisabled,
                    onTapLink: _openPrivacyPolicy,
                    isLoading: state.loading,
                  );
                },
              ),
            ),
          );
        },
      ),
    );
  }

  void _openPrivacyPolicy() {
    Modular.to.pushNamed(
      '/webview',
      arguments: WebViewParams(
        url: widget.privacyPolicyLink,
      ),
    );
  }

  void _onInit() {
    hasUserInfo = authCubit.state.localUserInfo?.personDocumentCpf != null;

    if (hasUserInfo) {
      personalDataModel.updateFields(authCubit.state.localUserInfo!);
    }

    _validateFields();
  }

  void _enableSaveButton() {
    setState(() {
      isSaveButtonDisabled = false;
    });
  }

  void _disableSaveButton() {
    setState(() {
      isSaveButtonDisabled = true;
    });
  }

  void _validateFields() {
    if (personalDataModel.validate()) {
      _enableSaveButton();
    } else {
      _disableSaveButton();
    }
  }

  Future<void> _completeUserData() async {
    await completeUserDataCubit.completeUserData(
      receiveEmail: receiveEmail,
      receiveSMS: receiveSMS,
      orderFormId: Modular.get<OrderFormCubit>().orderForm.orderFormId ?? '',
      userInfo: personalDataModel.getUserInfo(),
    );
    logAddPersonalInfo();
  }

  Future<void> _updateUserInfo() async {
    await authCubit.getUserInfo();
  }

  String? validateBirthDate(String? value) {
    if (value == null || value.isEmpty) {
      return "Data inválida";
    }

    try {
      List<String> parts = value.split('/');
      if (parts.length < 3) return "Data inválida";

      int day = int.parse(parts[0]);
      int month = int.parse(parts[1]);
      int year = int.parse(parts[2]);
      if (year < 100) {
        year += 2000;
      }

      DateTime birthDate = DateTime(year, month, day);
      DateTime today = DateTime.now();

      if (birthDate.isAfter(today)) {
        return "Data inválida";
      }
    } catch (e) {
      return "Data inválida";
    }

    return null;
  }

  void logAddPersonalInfo() {
    _eventDispatcher.logAddPersonalInfo(
        preFilled: hasUserInfo ? 'true' : 'false');
  }
}
