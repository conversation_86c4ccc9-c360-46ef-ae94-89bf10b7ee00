import 'package:azzas_analytics/events/account/account_events.dart';
import 'package:azzas_analytics/events/navigation/navigation_events.dart';
import 'package:azzas_analytics/services/user_property_service.dart';
import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:flutter/material.dart';
import 'package:vertapps_checkout_whitelabel/components/components.dart';
import 'package:vertapps_checkout_whitelabel/components/templates/cds_base_template.dart';
import 'package:vertapps_checkout_whitelabel/components/templates/login/cds_login_email_password_template.dart';
import 'package:vertapps_checkout_whitelabel/presenter/login/login_biometric/widget/ask_biometric_bottom_sheet.dart';
import 'package:vertapps_checkout_whitelabel/presenter/login/login_email_password/widgets/create_account_bottom_sheet.dart';

class LoginEmailPasswordPage extends CdsBaseStatefulTemplate {
  const LoginEmailPasswordPage({
    super.key,
    required super.tokens,
    this.overrideFinishFlow,
  });

  final Function()? overrideFinishFlow;
  @override
  State<LoginEmailPasswordPage> createState() => _LoginEmailPasswordPageState();
}

class _LoginEmailPasswordPageState extends State<LoginEmailPasswordPage> {
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _loginEmailPasswordCubit = Modular.get<LoginEmailPasswordCubit>();
  final _authCubit = Modular.get<AuthCubit>();
  final _passwordFocusNode = FocusNode();
  final _loginEmailCubit = Modular.get<LoginEmailCubit>();
  final authCubit = Modular.get<AuthCubit>();

  @override
  void initState() {
    NavigationEvents.logPageView(local: 'login_password');
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _passwordFocusNode.dispose();
  }

  Future<void> onTapLogin() async {
    if (_loginEmailPasswordCubit.state.isLoading ||
        !_loginEmailPasswordCubit.state.isFormValid) {
      return;
    }

    try {
      _loginEmailPasswordCubit.setIsLoading(true);
      final result = await _loginEmailPasswordCubit.loginWithPassword(
        email: _emailController.text,
        password: _passwordController.text,
      );
      if (result != null) {
        await _authCubit.loginSuccess(
            tokenResponse: result,
            userEmail: _emailController.text,
            loginType: LoginType.password);
        if (_authCubit.state.isLoggedIn && mounted) {
          if (_authCubit.state.localUserInfo?.personEmail.isNotNullOrEmpty ??
              false) {
            await UserPropertyService.setUserEmailProperty(
                _authCubit.state.localUserInfo?.personEmail ?? '');
          }
          if (widget.overrideFinishFlow != null) {
            widget.overrideFinishFlow?.call();
            return;
          }
          if (_authCubit.state.hasBiometrics) {
            Modular.to.navigate('/');
            return;
          }

          _showBottomSheet('/');
        }
      }
      return _loginEmailPasswordCubit.setError(true);
    } catch (e) {
      _loginEmailPasswordCubit.setError(true);
      debugPrint(e.toString());
    } finally {
      _loginEmailPasswordCubit.setIsLoading(false);
    }
  }

  void _showBottomSheet(String? route) {
    showModalBottomSheet(
      context: context,
      isDismissible: true,
      enableDrag: false,
      builder: (BuildContext context) {
        return AskBiometricBottomSheet(
          tokens: widget.tokens,
          route: route,
          cubit: authCubit,
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: SingleChildScrollView(
          child: BlocConsumer<LoginEmailPasswordCubit, LoginEmailPasswordState>(
            listener: (context, state) {
              if (state.userNotFoundError) {
                _showCreateAccountBottomSheet();
                return;
              }
            },
            bloc: _loginEmailPasswordCubit,
            builder: (context, state) {
              return CdsLoginEmaiPasswordlTemplate(
                obscureText: state.isObscurePassword,
                isLoading: state.isLoading,
                onToggleObscure: () {
                  _loginEmailPasswordCubit.toggleObscurePassword();
                },
                onEmailChanged: (email) {
                  _loginEmailPasswordCubit.validateEmail(email);
                },
                onPasswordChanged: (password) {
                  _loginEmailPasswordCubit.validatePassword(password);
                },
                tokens: widget.tokens,
                emailController: _emailController,
                passwordController: _passwordController,
                onBiometricSwitch: (value) {},
                isBiometricEnable: false,
                onBackButton: () {
                  Navigator.of(context).pop();
                },
                onPressedButton: () async {
                  await onTapLogin();
                },
                onTapLink: () async {
                  AccountEvents.logSignUpStep(
                      local: 'login_email', step: "e-mail");

                  Modular.to.pushNamed('/checkout/create_password', arguments: {
                    'email': _emailController.text,
                    "overrideFinishFlow": widget.overrideFinishFlow,
                  });
                },
                isDisabled: !state.isFormValid,
                hasError: state.hasError,
                supportingText: state.supportingText,
              );
            },
          ),
        ),
      ),
    );
  }

  void _showCreateAccountBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      useSafeArea: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return BlocBuilder<LoginEmailCubit, LoginEmailState>(
            bloc: _loginEmailCubit,
            builder: (context, state) {
              return CreateAccountBottomSheet(
                tokens: widget.tokens,
                config: CreateAccountBottomSheetConfig(
                  isLoading: state.isLoading,
                  onClose: () {
                    Navigator.of(context).pop();
                  },
                  onPressedButton: () async {
                    await _onTapCreateAccount();
                  },
                ),
              );
            });
      },
    );
  }

  Future<void> _onTapCreateAccount() async {
    if (_loginEmailCubit.state.isLoading) return;

    try {
      final result = await _loginEmailCubit.sendLogin(_emailController.text);
      if (result == null) return;

      _goToLoginCode(token: result);
    } catch (e) {
      if (!mounted) {
        return;
      }

      CdsSnackBar.show(
        context: context,
        text: "Erro ao enviar email",
        type: SnackbarType.error,
        tokens: widget.tokens,
      );
    }
  }

  void _goToLoginCode({
    required String token,
  }) {
    Modular.to.pushNamed(
      '/checkout/login_code',
      arguments: {
        'email': _emailController.text,
        'token': token,
        'isRegister': true,
        "overrideFinishFlow": widget.overrideFinishFlow,
      },
    );
  }
}
