import 'package:adyen_checkout/adyen_checkout.dart';
import 'package:flutter/material.dart';
import 'package:vertapps_checkout_whitelabel/components/composition_components/cds_product_item/utils/currency.dart';
import 'package:vertapps_checkout_whitelabel/components/templates/order_review/widgets/dialog_builder.dart';
import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:azzas_core/repositories/checkout/models/apple_pay/apple_pay_model.dart';

class ApplePayAdvancedComponent extends StatelessWidget {
  final ApplePayModel? applePay;
  final PaymentCubit paymentCubit;
  final void Function()? paymentResultCancel;
  final CdsPaymentArgs? args;

  const ApplePayAdvancedComponent({
    super.key,
    required this.applePay,
    required this.paymentCubit,
    this.paymentResultCancel,
    this.args,
  });

  @override
  Widget build(BuildContext context) {
    final appConfig = Modular.get<AppConfig>();

    final applePayComponentConfiguration = ApplePayComponentConfiguration(
      environment: Environment.europe,
      clientKey: applePay?.clientKey ?? '',
      countryCode: applePay?.countryCodeISO2 ?? '',
      amount: Amount(
        value: applePay?.amount?.value ?? 0,
        currency: applePay?.amount?.currency ?? "BRL",
      ),
      shopperLocale: 'pt-BR',
      applePayConfiguration: ApplePayConfiguration(
        merchantId: appConfig.applePayConfig?.merchantId ?? '',
        merchantName: appConfig.applePayConfig?.merchantName ?? '',
        merchantCapability: ApplePayMerchantCapability.credit,
        allowOnboarding: true,
        applePaySummaryItems: [
          ApplePaySummaryItem(
            label:
                "${appConfig.applePayConfig?.brandName ?? ''} em 1x de ${CurrencyUtils.format(
              amount: applePay?.totalCartValue ?? 0,
            )}",
            amount: Amount(
              value: applePay?.amount?.value ?? 0,
              currency: applePay?.amount?.currency ?? "BRL",
            ),
            type: ApplePaySummaryItemType.definite,
          ),
        ],
        requiredShippingContactFields: [
          ApplePayContactField.name,
        ],
      ),
    );

    final advancedCheckout = AdvancedCheckout(
      onSubmit: paymentCubit.onSubmit,
      onAdditionalDetails: paymentCubit.onAdditionalDetailsMock,
    );

    return AdyenApplePayComponent(
      configuration: applePayComponentConfiguration,
      paymentMethod: {
        "brands": applePay?.paymentMethods?.first.brands,
        "configuration": applePay?.paymentMethods?.first.configuration,
        "name": applePay?.paymentMethods?.first.name,
        "type": applePay?.paymentMethods?.first.type,
      },
      checkout: advancedCheckout,
      loadingIndicator: const AzzasSpinner(),
      width: 327,
      height: 50,
      onPaymentResult: (paymentResult) {
        if (paymentResult is PaymentAdvancedFinished) {
          if (paymentResult.resultCode.name == 'Authorised') {
            Modular.to.pushNamed(
              "/checkout/order-placed",
              arguments: CdsOrderPlacedArgs(
                onTapViewBag: () {
                  args?.goToBackPack.call(true);
                },
                onTapViewBalance: () {
                  args?.onTapViewBalance.call();
                },
                orderId:
                    'v${applePay?.orderId ?? ''}${appConfig.applePayConfig?.completeOderId}',
              ),
            );
          } else {
            paymentResultCancel?.call();
            paymentCubit.handleApplePayUserCancel(applePay?.paymentId ?? '');
          }
        } else {
          paymentResultCancel?.call();
          DialogBuilder.showPaymentResultDialog(paymentResult, context);
          paymentCubit.handleApplePayUserCancel(applePay?.paymentId ?? '');
        }
      },
    );
  }
}
