import 'dart:io';
import 'package:flutter/material.dart';
import 'package:vertapps_checkout_whitelabel/components/templates/templates.dart';
import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:vertapps_checkout_whitelabel/components/templates/payment/widgets/select_favorite_payment_bottom_sheet.dart';
import 'package:vertapps_checkout_whitelabel/components/templates/payment/widgets/show_checkout_bottom_sheet.dart';
import 'package:vertapps_checkout_whitelabel/components/templates/payment/widgets/wallet_bottom_sheet.dart';
import 'package:vertapps_checkout_whitelabel/presenter/common/widget/installmentes_bottom_sheet.dart';
import 'package:vertapps_checkout_whitelabel/presenter/payment/widgets/apple_pay_advanced_component.dart';
import 'package:vertapps_checkout_whitelabel/vertapps_checkout_whitelabel.dart';
import 'package:azzas_core/repositories/checkout/models/apple_pay/apple_pay_model.dart';

class CdsPaymentPage extends StatefulWidget {
  final BrandEnum brand;
  final CdsPaymentArgs? args;
  const CdsPaymentPage({
    super.key,
    required this.brand,
    required this.args,
  });

  @override
  State<CdsPaymentPage> createState() => _CdsPaymentPageState();
}

class _CdsPaymentPageState extends State<CdsPaymentPage>
    with TickerProviderStateMixin {
  final orderFormCubit = Modular.get<OrderFormCubit>();
  final paymentCubit = Modular.get<PaymentCubit>();
  final checkingAccountCubit = Modular.get<CheckingAccountCubit>();
  final eventDispatcher = Modular.get<EventDispatcher>();
  final appConfig = Modular.get<AppConfig>();
  final cvvController = TextEditingController();

  bool hasWarning = false;
  double totalCreditBalance = 0;
  double totalCashbackBalance = 0;
  double availableCreditBalance = 0;
  double availableCashbackValue = 0;
  double amountToUseCreditBalance = 0;
  double amountToUseCashbackBalance = 0;
  bool payOnlyCreditBalance = false;
  bool buttonIsDisabled = false;
  List<GiftCardsToExpire> giftCardsToExpire = [];
  bool showExpireWarning = false;
  ApplePayModel? applePay;
  String? favoritePayment;
  GiftCardCollection? checkingAccount;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _getFavoritePayment();
      getFinancialOverview();
      _getPaymentWarning();
      cvvController.addListener(updateCvv);
      updateCvv();
      updateCardSelected();
    });
  }

  PaymentType? get paymentSelected =>
      orderFormCubit.state.orderForm?.paymentType;

  CreditCardInfoData? get selectedCard => paymentCubit.getCardSelected();

  InstallmentOrderForm? get selectedInstallmentForCard =>
      paymentCubit.state.creditCardInfo.selectedInstallment;

  Future<void> _onFinishPayment() async {
    if (paymentSelected == null) {
      setState(() {
        buttonIsDisabled = true;
      });
    } else if (paymentSelected == PaymentType.applePay) {
      applePay = await _paymentApplePay();
    } else {
      Modular.to.pushNamed(
        '/checkout/order-review',
        arguments: CdsOrderReviewArgs(
          goToBackPack: (bool? showAddressCard) {
            widget.args?.goToBackPack.call(showAddressCard);
          },
          onTapViewBalance: () {
            widget.args?.onTapViewBalance.call();
          },
        ),
      );
    }
  }

  Future<void> _getPaymentWarning() async {
    await paymentCubit.getPaymentWarningComponent();
  }

  void getFinancialOverview() async {
    try {
      CdsBackdrop.show(context, widget.brand.tokens);
      final financialOverview =
          await checkingAccountCubit.getCheckingAccountFinancialOverviews();
      final expirationDetails =
          await checkingAccountCubit.getExpirationDetails();
      final checkingAccountResponse = checkingAccountCubit
          .getOrderFormCheckingAccount<GiftCardCollection>();

      setState(() {
        totalCreditBalance = financialOverview.amount.toDouble();
        availableCreditBalance = totalCreditBalance;
        totalCashbackBalance = 0;
        availableCashbackValue = totalCashbackBalance;
        giftCardsToExpire = expirationDetails.balances;
        checkingAccount = checkingAccountResponse;
        if (giftCardsToExpire.isNotEmpty) {
          showExpireWarning = true;
        }
      });
      await checkingAccountCubit.deleteCheckingAccountAmount(
          checkingAccount: checkingAccount!);
    } catch (e) {
      debugPrint(e.toString());
    } finally {
      if (mounted) {
        CdsBackdrop.hide();
      }
    }
  }

  Future<void> _getFavoritePayment() async {
    try {
      await orderFormCubit.getFavoritePaymentOption();
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  Future<void> _selectRecurrentCreditCardAsPayment(
      {Function()? onFinished, required CreditCardInfoData card}) async {
    try {
      CdsBackdrop.show(context, widget.brand.tokens);
      final paymentSytemId = card.selectedPaymentSystemId;

      if (paymentSytemId == null) {
        throw Exception(
            "Nenhum paymentSystemId foi identificado para obter as parcelas");
      }

      final availableInstallments =
          paymentCubit.getPaymentInstallmentsForCreditCard(
        paymentSytemId,
      );

      await paymentCubit.selectCreditCardPayment(
        creditCardInfo: card,
        installment: card.selectedInstallment ?? availableInstallments.first,
      );

      cvvController.text = '';

      await eventDispatcher.logAddPaymentInfo(
          paymentType: AzzasAnalyticsPaymentType.creditCard);
      onFinished?.call();
      return;
    } catch (e) {
      debugPrint(e.toString());
      if (mounted) {
        CdsSnackBar.show(
          context: context,
          tokens: widget.brand.tokens,
          text: 'Erro ao selecionar pagamento',
          type: SnackbarType.error,
        );
      }
    } finally {
      if (mounted) {
        CdsBackdrop.hide();
      }
    }
  }

  Future<void> _selectPixPayment() async {
    try {
      CdsBackdrop.show(context, widget.brand.tokens);
      paymentCubit.clearCreditCardError();
      await paymentCubit.selectPixPayment();
      await eventDispatcher.logAddPaymentInfo(
          paymentType: AzzasAnalyticsPaymentType.pix);
      setState(() {
        buttonIsDisabled = false;
      });
    } catch (e) {
      if (mounted) {
        CdsSnackBar.show(
          context: context,
          tokens: widget.brand.tokens,
          text: 'Erro ao selecionar pagamento',
          type: SnackbarType.error,
        );
      }
    } finally {
      if (mounted) {
        CdsBackdrop.hide();
      }
    }
  }

  Future<void> _selectApplePayPayment() async {
    try {
      CdsBackdrop.show(context, widget.brand.tokens);
      paymentCubit.clearCreditCardError();
      await paymentCubit.selectApplePay();
      await eventDispatcher.logAddPaymentInfo(
          paymentType: AzzasAnalyticsPaymentType.applePay);
      setState(() {
        buttonIsDisabled = false;
      });
    } catch (e) {
      if (mounted) {
        CdsSnackBar.show(
          context: context,
          tokens: widget.brand.tokens,
          text: 'Erro ao selecionar pagamento',
          type: SnackbarType.error,
        );
      }
    } finally {
      if (mounted) {
        CdsBackdrop.hide();
      }
    }
  }

  Future<void> _removeCreditValue() async {
    try {
      CdsBackdrop.show(context, widget.brand.tokens);
      await checkingAccountCubit.deleteCheckingAccountAmount(
        checkingAccount: checkingAccount!,
      );
      setState(() {
        payOnlyCreditBalance = false;
        amountToUseCreditBalance = 0;
        availableCreditBalance = totalCreditBalance;
        if (giftCardsToExpire.isNotEmpty) {
          showExpireWarning = true;
        }
      });
    } catch (e) {
      debugPrint(e.toString());
      if (mounted) {
        CdsSnackBar.show(
          context: context,
          tokens: widget.brand.tokens,
          text: 'Erro ao remover saldo',
          type: SnackbarType.error,
        );
      }
    } finally {
      if (mounted) {
        CdsBackdrop.hide();
      }
    }
  }

  void _showCreditBottomSheet() async {
    return showCheckoutBottomSheet(
      context: context,
      builder: (_) {
        return BlocBuilder<OrderFormCubit, OrderFormState>(
          bloc: orderFormCubit,
          builder: (context, state) {
            return WalletBottomSheet(
              tokens: widget.brand.tokens,
              creditBalanceValue: totalCreditBalance,
              cashbackBalanceValue: totalCashbackBalance,
              giftCardsToExpire: giftCardsToExpire,
              disableCashback: true,
              creditBalanceInUseValue: amountToUseCreditBalance,
              removeCreditValue: _removeCreditValue,
              totalPurchase: CurrencyHelper.currencyForDouble(
                    state.orderForm?.getTotalValueFormatted ?? 'R\$0,00',
                  ) /
                  100,
              useBalance: (creditValue, cashbackValue) async {
                try {
                  CdsBackdrop.show(context, widget.brand.tokens);
                  setState(() {
                    payOnlyCreditBalance = (creditValue / 100) ==
                        state.orderForm?.totalItemsWithShipping;
                    amountToUseCreditBalance = creditValue;
                    amountToUseCashbackBalance = cashbackValue;
                    availableCreditBalance = totalCreditBalance - creditValue;
                    availableCashbackValue =
                        totalCashbackBalance - cashbackValue;
                    showExpireWarning = false;
                  });
                  if (creditValue > 0) {
                    await checkingAccountCubit.useCheckingAccountAmount(
                      checkingAccount: checkingAccount!,
                      amount: creditValue.toInt(),
                    );
                  }
                  if (cashbackValue > 0) {
                    await checkingAccountCubit.useCheckingAccountAmount(
                      checkingAccount: GiftCard(name: 'Cashback'),
                      amount: cashbackValue.toInt(),
                    );
                  }
                  updateCardSelected();
                  Modular.to.pop();
                } catch (e) {
                  debugPrint(e.toString());
                  if (mounted) {
                    CdsSnackBar.show(
                      context: context,
                      tokens: widget.brand.tokens,
                      text: 'Erro ao usar saldo',
                      type: SnackbarType.error,
                    );
                  }
                } finally {
                  if (mounted) {
                    CdsBackdrop.hide();
                  }
                }
              },
            );
          },
        );
      },
      vsync: this,
    );
  }

  void updateCardSelected() async {
    final card = paymentCubit.getCardSelected();
    if (card != null) {
      await resetInstallmentsCardPayment();
    }
  }

  Future<void> resetInstallmentsCardPayment() async {
    final orderForm = orderFormCubit.orderForm;
    final purchaseTotal = (orderForm.value ?? 0) -
        orderForm.discountValue +
        orderForm.getGiftCardValues();
    if (paymentSelected == PaymentType.creditCard && selectedCard != null) {
      if (selectedCard?.selectedInstallment?.value != purchaseTotal) {
        final paymentSytemId = selectedCard?.selectedPaymentSystemId;

        if (paymentSytemId == null) {
          throw Exception(
              "Nenhum paymentSystemId foi identificado para obter as parcelas");
        }
        final installment = paymentCubit
            .getPaymentInstallmentsForCreditCard(
              paymentSytemId,
            )
            .first;

        await paymentCubit.selectCreditCardPayment(
          creditCardInfo: selectedCard!,
          installment: installment,
        );
      }
    }
  }

  void _showChooseFavoriteBottomSheet(
      List<CreditCardModel>? cards, String? favoriteCardNumber) async {
    return showCheckoutBottomSheet(
      context: context,
      builder: (_) {
        return BlocBuilder<OrderFormCubit, OrderFormState>(
          bloc: orderFormCubit,
          builder: (context, state) {
            return StatefulBuilder(builder: (context, setState) {
              return SelectFavoritePaymentBottomSheet(
                tokens: widget.brand.tokens,
                cards: cards,
                favoriteCardNumber: favoriteCardNumber,
                enablePixInstallment:
                    (orderFormCubit.state.orderForm?.acceptsPixInstallments ??
                        false),
                selectedFavorite: (paymentSelected, cardSelected) async {
                  Modular.to.pop();
                  await orderFormCubit
                      .updateFavoritePaymentOption(cardSelected == null
                          ? paymentSelected == PaymentType.applePay
                              ? PaymentSystemIds.applePay
                              : paymentSelected.value
                          : cardSelected.cardAccountId ?? '');
                  await _getFavoritePayment();
                },
              );
            });
          },
        );
      },
      vsync: this,
    );
  }

  void updateCvv() {
    if (selectedCard != null) {
      setState(() {
        buttonIsDisabled =
            cvvController.text.length < 3 || selectedInstallmentForCard == null;
      });
      paymentCubit.setCreditCardInfoCvv(cvvController.text);
    }
  }

  void _showInstallmentsBottomSheet() async {
    return showCheckoutBottomSheet(
      context: context,
      builder: (_) {
        return InstallmentesBottomSheet(
            tokens: widget.brand.tokens, onSuccessSelectInstallment: updateCvv);
      },
      vsync: this,
    );
  }

  Future<void> onCreateNewCard() async {
    Modular.to.pushNamed(
      '/checkout/credit-card-info',
      arguments: widget.args,
    );
  }

  void updatePaymentSelected(
    PaymentType? payment, {
    CreditCardInfoData? card,
  }) {
    if (payment == PaymentType.pix) {
      _selectPixPayment();
    } else if (payment == PaymentType.applePay) {
      _selectApplePayPayment();
    } else if (payment == PaymentType.creditCard && card != null) {
      setState(() {
        buttonIsDisabled = false;
      });
      _selectRecurrentCreditCardAsPayment(card: card);
    } else if (payment == PaymentType.newCard) {
      onCreateNewCard();
    }
  }

  Future<ApplePayModel?> _paymentApplePay() async {
    try {
      return await paymentCubit.applePayTransaction(
        "6LfvVcsqAAAAAA0cTMofemIoZ68ZvYT1jI36qabz",
        "6Leo08YqAAAAADvwYu9YpnaDBRFPHasHbelYezO-",
        context,
      );
    } catch (e) {
      final String message;
      if (e is PaymentErrorResponse && e.error is RecaptchaException) {
        final cause = e.error as RecaptchaException;
        message = cause.userFriendlyMessage;
      } else if ((e.toString().contains('CHK0328'))) {
        message = "Aguarde 2 minutos para tentar novamente";
      } else {
        message = 'ocorreu um erro inesperado tente novamente mais tarde';
      }
      CdsSnackBar.show(
        context: context,
        tokens: widget.brand.tokens,
        text: message,
        type: SnackbarType.error,
      );
      return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<OrderFormCubit, OrderFormState>(
        bloc: orderFormCubit,
        builder: (context, orderFormState) {
          return BlocBuilder<PaymentCubit, PaymentState>(
              bloc: paymentCubit,
              builder: (context, paymentState) {
                return Scaffold(
                  body: SafeArea(
                    child: CdsPaymentTemplate(
                      tokens: widget.brand.tokens,
                      onTapBack: () {
                        if (paymentSelected == PaymentType.applePay &&
                            applePay != null) {
                          paymentCubit.handleApplePayUserCancel(
                              applePay?.paymentId ?? '');
                          setState(() {
                            applePay = null;
                          });
                        }
                        Modular.to.pop();
                      },
                      goToOrderReview: () {
                        Modular.to.pushNamed(
                          '/checkout/order-review',
                          arguments: CdsOrderReviewArgs(
                            goToBackPack: (bool? showAddressCard) {
                              widget.args?.goToBackPack.call(showAddressCard);
                            },
                            onTapViewBalance: () {
                              widget.args?.onTapViewBalance.call();
                            },
                          ),
                        );
                      },
                      paymentWarning: paymentState.paymentWarning,
                      cvvController: cvvController,
                      selectedCard: selectedCard,
                      secondCardSelected: null,
                      selectedInstallmentForCard:
                          orderFormCubit.orderForm.getCurrentInstallment,
                      paymentSelected: paymentSelected,
                      hasWarning: hasWarning,
                      totalCreditBalance: totalCreditBalance,
                      totalCashbackBalance: totalCashbackBalance,
                      availableCreditBalance: availableCreditBalance,
                      availableCashbackValue: availableCashbackValue,
                      amountToUseCreditBalance: amountToUseCreditBalance,
                      amountToUseCashbackBalance: amountToUseCashbackBalance,
                      payOnlyCreditBalance: payOnlyCreditBalance,
                      buttonIsDisabled: !payOnlyCreditBalance &&
                          (buttonIsDisabled ||
                              ((cvvController.text.length < 3 ||
                                      paymentState.creditCardInfo
                                              .selectedInstallment ==
                                          null) &&
                                  paymentSelected == PaymentType.creditCard)),
                      giftCardsToExpire: giftCardsToExpire,
                      showExpireWarning: showExpireWarning,
                      showCreditBottomSheet: _showCreditBottomSheet,
                      favoriteOption: orderFormState.favoriteOption,
                      orderForm: orderFormState.orderForm,
                      isLoading:
                          orderFormState.isLoading || paymentState.isLoading,
                      showChooseFavoriteBottomSheet:
                          _showChooseFavoriteBottomSheet,
                      updatePaymentSelected: updatePaymentSelected,
                      showInstallmentsBottomSheet: _showInstallmentsBottomSheet,
                      onCreateNewCard: onCreateNewCard,
                      onFinishPayment: _onFinishPayment,
                      selectedPaymentType: paymentState.selectedPaymentType,
                      isIos: Platform.isIOS && appConfig.applePayConfig != null,
                      applePayButton: applePay != null
                          ? ApplePayAdvancedComponent(
                              paymentCubit: paymentCubit,
                              applePay: applePay,
                              args: widget.args,
                              paymentResultCancel: () =>
                                  setState(() => applePay = null),
                            )
                          : null,
                    ),
                  ),
                );
              });
        });
  }
}
