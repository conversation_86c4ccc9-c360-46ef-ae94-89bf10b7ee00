import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:vertapps_checkout_whitelabel/presenter/backpack/widget/register_addres_cep_bottom_sheet.dart';
import 'package:vertapps_checkout_whitelabel/presenter/backpack/widget/register_address_bottom_sheet.dart';
import 'package:vertapps_checkout_whitelabel/components/components.dart';
import 'package:vertapps_checkout_whitelabel/tokens/tokens.dart';

class CepBottomSheet extends StatefulWidget {
  final CheckoutDStokens tokens;
  const CepBottomSheet({
    super.key,
    required this.tokens,
  });

  @override
  State<CepBottomSheet> createState() => _CepBottomSheetState();
}

class _CepBottomSheetState extends State<CepBottomSheet>
    with TickerProviderStateMixin {
  final _bagPageCubit = Modular.get<BagPageCubit>();
  final _cepController = TextEditingController();
  final authCubit = Modular.get<AuthCubit>();
  final _eventDispatcher = Modular.get<EventDispatcher>();
  bool isCepValid = false;

  @override
  void initState() {
    super.initState();
    _cepController.clear();
    _cepController.addListener(() {
      _onTextChanged();
    });
  }

  void _onTextChanged() async {
    final isValid = TextHelper.isCEP(_cepController.text);

    if (isValid && mounted) {
      await _bagPageCubit.getAddressFromCep(_cepController.text);
      logSearchZipcode();
      setState(() {
        isCepValid = isValid;
        FocusManager.instance.primaryFocus?.unfocus();
      });
    } else {
      if (mounted) {
        setState(() {
          isCepValid = false;
        });
      }
    }
  }

  Future<void> _calculateDeliveriesByCep() async {
    try {
      Navigator.pop(context);
    } catch (e) {
      if (mounted) {
        CdsSnackBar.show(
          tokens: widget.tokens,
          context: context,
          text: 'Erro ao calcular frete',
          type: SnackbarType.error,
        );
      }
    }
  }

  void _showBottomSheetRegisterAddress() {
    SchedulerBinding.instance.addPostFrameCallback((_) {
      if (!mounted) return;

      showAzzasBottomSheet(
        vsync: this,
        context: context,
        builder: (_) {
          return RegisterAddressBottomSheet(
            templateType: TemplateType.bottomSheet,
            tokens: widget.tokens,
            onBack: () {
              Navigator.pop(context);
            },
            address: QueryCep(),
          );
        },
      );
    });
  }

  void logSearchZipcode(){
    _eventDispatcher.logSearchZipCode(
        zipCode: _bagPageCubit.state.currentAddress?.postalCode ?? _cepController.text,
        region: 'checkout',
        flagPickup: _bagPageCubit.hasPickupOptions(),
        shippings: _bagPageCubit.state.packages);
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<BagPageCubit, BagPageState>(
      bloc: _bagPageCubit,
      builder: (context, state) {
        return CdsCepBottomSheet(
            tokens: widget.tokens,
            cepEditingController: _cepController,
            onCEPChosen: (_) {
              _calculateDeliveriesByCep();
              _cepController.removeListener(_onTextChanged);
            },
            onCEPSearch: (_) {},
            onClose: () {
              Navigator.pop(context);
            },
            isLoading: state.isLoadingAddress,
            isCepValid: isCepValid,
            cityAndState: state.currentAddress?.cityAndState,
            isUserLoggedIn: authCubit.state.isLoggedIn,
            onRegisterAddress: () {
              Navigator.pop(context);
              _showBottomSheetRegisterAddress();
            });
      },
    );
  }
}
