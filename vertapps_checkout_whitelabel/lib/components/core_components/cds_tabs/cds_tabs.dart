import 'package:flutter/material.dart';
import 'package:vertapps_checkout_whitelabel/components/components.dart';

class CdsTabs extends CdsBaseStatelessComponent {
  const CdsTabs({
    super.key,
    required this.tabs,
    this.isScrollable = false,
    this.isContentScrollable = false,
    this.content = const [],
    this.tabController,
    required super.tokens,
  });

  final List<CdsTabItem> tabs;
  final bool isScrollable;
  final List<Widget> content;
  final TabController? tabController;
  final bool isContentScrollable;

  Widget _buildTabContent(Widget content) {
    if (isContentScrollable) {
      return ListView(
        children: [
          content,
        ],
      );
    }

    return content;
  }

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: tabs.length,
      child: Builder(
        builder: (context) {
          return Column(
            children: [
              Theme(
                data: ThemeData.fallback(useMaterial3: true).copyWith(
                  tabBarTheme: TabBarThemeData(
                    overlayColor: WidgetStateProperty.all(Colors.transparent),
                  ),
                ),
                child: Material(
                  color: Colors.transparent,
                  child: TabBar(
                    splashFactory: NoSplash.splashFactory,
                    indicator: const UnderlineTabIndicator(
                      borderSide: BorderSide(width: 2),
                    ),
                    indicatorColor: tokens.colors.content.pure,
                    isScrollable: isScrollable,
                    tabAlignment: isScrollable ? TabAlignment.start : null,
                    indicatorSize: TabBarIndicatorSize.tab,
                    indicatorPadding: isScrollable
                        ? EdgeInsets.zero
                        : EdgeInsets.symmetric(
                            horizontal: tokens.layout.spacing.lg,
                          ),
                    padding: isScrollable
                        ? EdgeInsets.symmetric(
                            horizontal: tokens.layout.spacing.lg,
                          )
                        : EdgeInsets.zero,
                    labelPadding: EdgeInsets.all(tokens.layout.spacing.md),
                    labelStyle:
                        tokens.typography.bodyStyles.descriptionBold.copyWith(
                      color: tokens.colors.content.pure,
                    ),
                    unselectedLabelStyle:
                        tokens.typography.bodyStyles.description.copyWith(
                      color: tokens.colors.content.content03,
                    ),
                    tabs: tabs,
                  ),
                ),
              ),
              if (content.isNotEmpty)
                Expanded(
                  child: TabBarView(
                    controller:
                        tabController ?? DefaultTabController.of(context),
                    children: content.map(_buildTabContent).toList(),
                  ),
                ),
            ],
          );
        },
      ),
    );
  }
}
