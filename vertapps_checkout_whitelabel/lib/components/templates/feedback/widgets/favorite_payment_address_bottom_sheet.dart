import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:flutter/material.dart';
import 'package:vertapps_checkout_whitelabel/components/components.dart';
import 'package:vertapps_checkout_whitelabel/components/composition_components/cds_bottom_sheet/sheet.dart';
import 'package:vertapps_checkout_whitelabel/tokens/checkout_ds_tokens.dart';

class FavoritePaymentAddressBottomSheet extends StatefulWidget {
  const FavoritePaymentAddressBottomSheet({
    required this.tokens,
    required this.orderForm,
    required this.selectedCard,
    this.closeIcon,
    this.titleStyle,
    this.bodyStyle,
    this.descriptionStyle,
    this.borderRadius,
    this.showDragBar = true,
    this.padding,
    this.onCloseTap,
    this.buttonStyle,
    this.onButtonPressed,
    this.isLoading = false,
    super.key,
  });

  final Function()? onButtonPressed;
  final IconData? closeIcon;
  final TextStyle? titleStyle;
  final TextStyle? buttonStyle;
  final TextStyle? bodyStyle;
  final TextStyle? descriptionStyle;
  final BorderRadius? borderRadius;
  final bool showDragBar;
  final EdgeInsets? padding;
  final Function()? onCloseTap;
  final bool isLoading;
  final CheckoutDStokens tokens;
  final OrderForm? orderForm;
  final Payment? selectedCard;

  @override
  State<FavoritePaymentAddressBottomSheet> createState() =>
      _FavoritePaymentAddressBottomSheetState();
}

class _FavoritePaymentAddressBottomSheetState
    extends State<FavoritePaymentAddressBottomSheet> {
  final orderFormCubit = Modular.get<OrderFormCubit>();
  bool checkbox = false;
  bool addressFavorited = false;
  bool paymentFavorited = false;
  bool isloadingAddress = false;
  bool isloadingPayment = false;
  PaymentType? paymentSelected;
  String? addressSelected;

  @override
  void initState() {
    super.initState();
    setState(() {
      paymentSelected = widget.orderForm?.paymentType;
    });
    _getFavoritePayment();
    _getFavoriteAddress();
  }

  Future<void> _getFavoritePayment() async {
    try {
      setState(() {
        isloadingPayment = true;
      });
      final favoriteOption = await orderFormCubit.getFavoritePaymentOption();
      setState(() {
        if (paymentSelected?.value == favoriteOption) {
          paymentFavorited = true;
        }
        isloadingPayment = false;
      });
    } catch (e) {
      setState(() {
        isloadingPayment = false;
      });
    }
  }

  Future<void> _getFavoriteAddress() async {
    final address = widget.orderForm?.shippingData?.address;
    try {
      setState(() {
        isloadingAddress = true;
      });
      final favoriteOption = await orderFormCubit
          .getIsAddressOptionFavorite(address?.addressId ?? '');
      setState(() {
        addressFavorited = favoriteOption;
        isloadingAddress = false;
      });
    } catch (e) {
      setState(() {
        isloadingAddress = false;
      });
    }
  }

  Future<void> _favoritePayment() async {
    if (paymentFavorited) {
      return;
    }
    try {
      setState(() {
        isloadingPayment = true;
      });
      if (paymentSelected != null) {
        await orderFormCubit
            .updateFavoritePaymentOption(paymentSelected!.value);
        setState(() {
          paymentFavorited = true;
        });
      }
    } catch (e) {
      debugPrint(e.toString());
    } finally {
      setState(() {
        isloadingPayment = false;
      });
    }
  }

  Future<void> _disableBottomSheet(value) async {
    setState(() {
      checkbox = value;
    });
    await orderFormCubit.disableFavoriteAddressPaymentBottomSheet(value);
  }

  Future<void> _favoriteAddress() async {
    if (addressFavorited) {
      return;
    }
    try {
      setState(() {
        isloadingAddress = true;
      });
      if (addressSelected != null) {
        await orderFormCubit.updateFavoriteAddressOption(
            widget.orderForm?.shippingData?.address?.addressId ?? '');
        setState(() {
          addressFavorited = true;
        });
      }
    } catch (e) {
      debugPrint(e.toString());
    } finally {
      setState(() {
        isloadingAddress = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final selectedCard = widget.selectedCard;
    final address = widget.orderForm?.shippingData?.address;
    return CdsSheet(
      tokens: widget.tokens,
      title: 'Nossa sugestão para você!',
      onClose: () {
        Navigator.of(context).pop();
      },
      type: CdsSheetType.bottomSheet,
      slotContent: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Flexible(
                child: Text(
                  'Favorite seus dados usados e facilite suas próximas compras!',
                  style:
                      widget.tokens.typography.bodyStyles.description.copyWith(
                    color: widget.tokens.colors.content.content03,
                  ),
                ),
              ),
              SizedBox(
                width: 24,
                height: 24,
                child: CdsTooltip(
                  tokens: widget.tokens,
                  message:
                      'Os favoritos virão pré-selecionados nas próximas compras. Não se preocupe, você pode sempre alterar endereço e pagamento utilizados antes de fechar a compra.',
                  child: Icon(
                    widget.tokens.icons.info,
                    size: 24,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _FavoriteOption(
            tokens: widget.tokens,
            config: FavoriteOptionConfig(
              contentSubtitle: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 8),
                  Text(
                    '${address?.street}',
                    style: widget.tokens.typography.bodyStyles.description
                        .copyWith(
                            color: widget.tokens.colors.content.content03),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${address?.postalCode} | ${address?.city} - ${address?.state}',
                    style: widget.tokens.typography.bodyStyles.description
                        .copyWith(
                            color: widget.tokens.colors.content.content03),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Destinatário: ${address?.receiverName}',
                    style: widget.tokens.typography.bodyStyles.description
                        .copyWith(
                            color: widget.tokens.colors.content.content03),
                  ),
                ],
              ),
              contentTitle: address?.complement ?? '',
              title: 'Endereço',
              isFavorite: addressFavorited,
              isLoading: isloadingAddress,
              favoriteOption: () {
                _favoriteAddress();
              },
            ),
          ),
          const SizedBox(height: 24),
          _FavoriteOption(
            tokens: widget.tokens,
            config: FavoriteOptionConfig(
              contentSubtitle: Text(
                selectedCard != null
                    ? '${AzzasCreditCardType.fromString(selectedCard.paymentSystemName ?? '')} ${selectedCard.lastDigits}'
                    : paymentSelected?.getFormattedName() ?? '',
                style: widget.tokens.typography.bodyStyles.description
                    .copyWith(color: widget.tokens.colors.content.content03),
              ),
              contentTitle: selectedCard != null
                  ? "${selectedCard.cardHolder} • Crédito"
                  : "",
              title: 'Pagamento',
              isFavorite: paymentFavorited,
              isLoading: isloadingPayment,
              favoriteOption: () {
                _favoritePayment();
              },
            ),
          ),
          const SizedBox(height: 24),
          Row(
            children: [
              CdsCheckbox(
                tokens: widget.tokens,
                value: checkbox,
                onChanged: _disableBottomSheet,
              ),
              const SizedBox(width: 8),
              Text(
                'Não perguntar novamente',
                style: widget.tokens.typography.bodyStyles.description
                    .copyWith(color: widget.tokens.colors.content.pure),
              ),
            ],
          ),
          const SizedBox(height: 24),
          Row(
            children: [
              Expanded(
                child: CdsButton.secondary(
                  tokens: widget.tokens,
                  onPressed: () {
                    if (!isloadingPayment && !isloadingAddress) {
                      Navigator.of(context).pop();
                    }
                  },
                  size: CdsButtonSize.large,
                  buttonText: 'Agora não',
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: CdsButton.primary(
                  tokens: widget.tokens,
                  onPressed: () async {
                    await _favoritePayment();
                    await _favoriteAddress();
                    Navigator.of(context).pop();
                  },
                  isLoading: isloadingPayment || isloadingAddress,
                  size: CdsButtonSize.large,
                  buttonText: 'Definir favorito',
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class FavoriteOptionConfig {
  final String title;
  final String contentTitle;
  final Widget contentSubtitle;
  final bool isFavorite;
  final VoidCallback favoriteOption;
  final bool isLoading;

  const FavoriteOptionConfig({
    required this.contentSubtitle,
    required this.favoriteOption,
    required this.contentTitle,
    required this.isFavorite,
    required this.isLoading,
    required this.title,
  });
}

class _FavoriteOption extends StatelessWidget {
  const _FavoriteOption({
    required this.tokens,
    required this.config,
  });
  final CheckoutDStokens tokens;
  final FavoriteOptionConfig config;

  @override
  Widget build(BuildContext context) {
    return Card(
      color: tokens.colors.surface.surfaceCard,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 24),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24),
            child: Row(
              children: [
                Text(
                  config.title,
                  style: tokens.typography.bodyStyles.descriptionBold
                      .copyWith(color: tokens.colors.content.pure),
                ),
                const Spacer(),
                Row(
                  children: [
                    config.isFavorite
                        ? Text(
                            '${config.title} Favorito',
                            style:
                                tokens.typography.bodyStyles.caption.copyWith(
                              color: tokens
                                  .colors.feedback.feedbackSuccess.content,
                            ),
                          )
                        : GestureDetector(
                            onTap: config.isLoading
                                ? () {}
                                : config.favoriteOption,
                            child: Text(
                              'Definir favorito',
                              style:
                                  tokens.typography.bodyStyles.caption.copyWith(
                                color: tokens.colors.brand.highlight,
                                decoration: TextDecoration.underline,
                              ),
                            ),
                          ),
                    if (config.isFavorite)
                      Icon(tokens.icons.heartFilled,
                          size: 16,
                          color:
                              tokens.colors.feedback.feedbackSuccess.content),
                  ],
                )
              ],
            ),
          ),
          const SizedBox(height: 24),
          CdsDivider(
            dividerSize: CdsDividerSize.small,
            tokens: tokens,
          ),
          const SizedBox(height: 24),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  config.contentTitle,
                  style: tokens.typography.bodyStyles.description
                      .copyWith(color: tokens.colors.content.pure),
                ),
                const SizedBox(height: 8),
                config.contentSubtitle,
              ],
            ),
          ),
          const SizedBox(height: 24),
        ],
      ),
    );
  }
}
