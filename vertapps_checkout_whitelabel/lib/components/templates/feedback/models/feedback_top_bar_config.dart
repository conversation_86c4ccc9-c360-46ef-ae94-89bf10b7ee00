import 'package:flutter/material.dart';

class FeedbackTopBarConfig {
  /// O título da [TopBar]
  ///
  /// Ex: `Seu pedido foi recebido!`
  final String title;

  /// O subtítulo da [TopBar]
  ///
  /// Ex: `Em breve você receberá a confirmação do pedido no e-mail:`
  final String subtitle;

  /// O email do usuário
  final String emailUser;

  /// A função que será chamada quando o botão de fechar for pressionado
  ///
  /// Ex: `onTapClose: () => Navigator.of(context).pop()`
  final VoidCallback onTapClose;

  /// Construtor do widget [FeedbackTopBar]
  ///
  /// Cria uma instância do widget [FeedbackTopBar] com os
  /// parâmetros [tokens], [title], [subtitle] e [onTapClose].

  const FeedbackTopBarConfig({
    required this.title,
    required this.subtitle,
    required this.emailUser,
    required this.onTapClose,
  });
}
