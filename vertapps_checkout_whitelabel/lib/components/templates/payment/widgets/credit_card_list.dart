import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:vertapps_checkout_whitelabel/components/templates/cds_base_template.dart';
import 'package:vertapps_checkout_whitelabel/components/templates/payment/widgets/list_item_default.dart';
import 'package:vertapps_checkout_whitelabel/components/templates/payment/widgets/list_item_radio.dart';
import 'package:vertapps_checkout_whitelabel/tokens/checkout_ds_tokens.dart';

import '../../../components.dart';

enum CreditCardFlow {
  selectCard,
  selectFavoriteCard,
}

class CreditCardList extends CdsBaseStatefulTemplate {
  const CreditCardList({
    super.key,
    required super.tokens,
    required this.items,
    required this.onSelectCard,
    required this.selectedCard,
    required this.onCreateCard,
    required this.cvvController,
    required this.hasPaymentAndCardSelected,
    this.selectedInstallmentForCard,
    this.onTapInstallments,
    this.isLoading = false,
    this.hasOnlyCards = false,
    this.favoriteCardNumber,
    required this.creditCardFlow,
  });
  final List<CreditCardModel> items;
  final Function(CreditCardInfoData) onSelectCard;
  final CreditCardInfoData? selectedCard;
  final bool hasOnlyCards;
  final Function onCreateCard;
  final String? favoriteCardNumber;
  final VoidCallback? onTapInstallments;
  final bool isLoading;
  final String? selectedInstallmentForCard;
  final TextEditingController cvvController;
  final bool hasPaymentAndCardSelected;
  final CreditCardFlow creditCardFlow;

  @override
  State<CreditCardList> createState() => _CreditCardListState();
}

class _CreditCardListState extends State<CreditCardList> {
  @override
  void initState() {
    super.initState();
    setState(() {
      visibleCards =
          widget.items.length > 3 ? widget.items.sublist(0, 3) : widget.items;
    });
  }

  List<CreditCardModel> visibleCards = [];
  bool isVisibleCardsWithCvvAndInstallments = false;

  CreditCardModel? getSelectedCard() {
    if (widget.creditCardFlow == CreditCardFlow.selectFavoriteCard) {
      return widget.items
          .firstWhereOrNull((c) => c.cardNumber == widget.favoriteCardNumber);
    }
    return widget.items.firstWhereOrNull(
        (e) => e.cardId == widget.selectedCard?.cardAccountId);
  }

  @override
  Widget build(BuildContext context) {
    CreditCardModel? selectedCard = getSelectedCard();

    return Column(
      children: [
        if (selectedCard != null &&
            widget.favoriteCardNumber != selectedCard.cardNumber &&
            isVisibleCardsWithCvvAndInstallments == true) ...[
          _CardWithCvvAndInstallments(
            tokens: widget.tokens,
            selectedCard: selectedCard,
            onSelectCard: widget.onSelectCard,
            favoriteCardNumber: widget.favoriteCardNumber,
            isLoading: widget.isLoading,
            cvvController: widget.cvvController,
            selectedInstallmentForCard: widget.selectedInstallmentForCard,
            onTapInstallments: widget.onTapInstallments,
            hasPaymentAndCardSelected: widget.hasPaymentAndCardSelected,
          ),
          SizedBox(height: widget.tokens.layout.spacing.lg),
          GestureDetector(
            onTap: () {
              if (widget.creditCardFlow == CreditCardFlow.selectCard) {
                setState(() {
                  isVisibleCardsWithCvvAndInstallments = false;
                });
              }

              setState(() {
                visibleCards = widget.items.length > 3
                    ? widget.items.sublist(0, 3)
                    : widget.items;
              });
            },
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  widget.tokens.icons.arrowLeft,
                  size: 16,
                ),
                const SizedBox(width: 4),
                Text(
                  'Ver lista de cartões salvos',
                  style: widget.tokens.typography.bodyStyles.caption,
                )
              ],
            ),
          ),
          SizedBox(height: widget.tokens.layout.spacing.lg),
        ],
        if (isVisibleCardsWithCvvAndInstallments == false) ...[
          _CardWithoutCvvAndInstallments(
            tokens: widget.tokens,
            selectedCard: selectedCard,
            onSelectCard: (cardInfo) {
              widget.onSelectCard(cardInfo);
              if (widget.creditCardFlow == CreditCardFlow.selectCard) {
                setState(() {
                  isVisibleCardsWithCvvAndInstallments = true;
                });
              }
            },
            favoriteCardNumber: widget.favoriteCardNumber,
            isLoading: widget.isLoading,
            hasPaymentAndCardSelected: widget.hasPaymentAndCardSelected,
            visibleCards: visibleCards,
            creditCardFlow: widget.creditCardFlow,
          ),
          if (visibleCards.length >= 3 &&
              visibleCards.length < widget.items.length) ...[
            ListItemDefault(
              tokens: widget.tokens,
              onTap: () {
                setState(() {
                  visibleCards = widget.items;
                });
              },
              title: 'Carregar mais cartões salvos',
              showMargin: false,
              showBorder: false,
              leftIcon: Icon(
                widget.tokens.icons.more,
              ),
            ),
          ],
          if (widget.creditCardFlow == CreditCardFlow.selectCard) ...[
            ListItemDefault(
              tokens: widget.tokens,
              onTap: () {
                widget.onCreateCard();
              },
              title: 'Adicionar novo cartão',
              showMargin: false,
              showBorder: false,
              leftIcon: Icon(
                widget.tokens.icons.add,
              ),
            ),
          ]
        ]
      ],
    );
  }
}

class _CardWithoutCvvAndInstallments extends StatelessWidget {
  const _CardWithoutCvvAndInstallments({
    required this.tokens,
    required this.selectedCard,
    required this.onSelectCard,
    required this.favoriteCardNumber,
    required this.isLoading,
    required this.hasPaymentAndCardSelected,
    required this.visibleCards,
    required this.creditCardFlow,
  });

  final CheckoutDStokens tokens;
  final CreditCardModel? selectedCard;
  final Function(CreditCardInfoData) onSelectCard;
  final String? favoriteCardNumber;
  final bool isLoading;
  final bool hasPaymentAndCardSelected;
  final List<CreditCardModel> visibleCards;
  final CreditCardFlow creditCardFlow;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        ...visibleCards.map((e) {
          return ListItemRadio(
              tokens: tokens,
              onTap: () {
                final cardInfo = e.toCreditCardInfoData();
                onSelectCard(cardInfo);
              },
              title: e.cardNickName,
              subtitle: e.cardInfo,
              hasFavoriteCardTag: favoriteCardNumber == e.cardNumber,
              isDisabled: isLoading,
              showMargin: false,
              showBorder: false,
              leftIcon: Image(
                image: e.brandIcon,
                height: 20,
                width: 20,
              ),
              isChecked: verifyIsChecked(
                e.cardNumber,
                hasPaymentAndCardSelected,
              ));
        }),
      ],
    );
  }

  bool verifyIsChecked(cardNumber, hasPaymentAndCardSelected) {
    if (creditCardFlow == CreditCardFlow.selectCard) {
      return selectedCard?.cardNumber == cardNumber &&
          hasPaymentAndCardSelected;
    }
    return selectedCard?.cardNumber == cardNumber;
  }
}

class _CardWithCvvAndInstallments extends StatelessWidget {
  const _CardWithCvvAndInstallments({
    required this.tokens,
    required this.selectedCard,
    required this.onSelectCard,
    required this.favoriteCardNumber,
    required this.isLoading,
    required this.cvvController,
    required this.selectedInstallmentForCard,
    required this.onTapInstallments,
    required this.hasPaymentAndCardSelected,
  });

  final CreditCardModel selectedCard;
  final Function(CreditCardInfoData) onSelectCard;
  final CheckoutDStokens tokens;
  final String? favoriteCardNumber;
  final bool isLoading;
  final TextEditingController cvvController;
  final String? selectedInstallmentForCard;
  final VoidCallback? onTapInstallments;
  final bool hasPaymentAndCardSelected;

  String get installmentSelectedText {
    if (selectedInstallmentForCard == null ||
        selectedInstallmentForCard == '0x de R\$ 0,00') {
      return 'Selecione uma opção';
    }
    return selectedInstallmentForCard!;
  }

  @override
  Widget build(BuildContext context) {
    return ListItemRadio(
      tokens: tokens,
      onTap: () {
        final cardInfo = selectedCard.toCreditCardInfoData();
        onSelectCard(cardInfo);
      },
      title: selectedCard.cardNickName,
      subtitle: selectedCard.cardInfo,
      hasFavoriteCardTag: favoriteCardNumber == selectedCard.cardNumber,
      isDisabled: isLoading,
      creditCardInputInfo: Column(
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: tokens.layout.spacing.md),
              Container(
                decoration: BoxDecoration(
                  color: tokens.colors.surface.surfaceBackground,
                  borderRadius: tokens.shapes.borderRadiusComponents.inputRadius,
                ),
                child: CdsTextField(
                  label: 'CVV',
                  controller: cvvController,
                  tokens: tokens,
                  keyboardType: TextInputType.number,
                  inputFormatter: [
                    LengthLimitingTextInputFormatter(4),
                  ],
                ),
              ),
              SizedBox(height: tokens.layout.spacing.md),
              Text(
                'Parcelamento:',
                style: tokens.typography.bodyStyles.description.copyWith(
                  color: tokens.colors.content.content01,
                ),
              ),
              SizedBox(height: tokens.layout.spacing.md),
              CdsSecondaryButton(
                tokens: tokens,
                buttonText: installmentSelectedText,
                size: CdsButtonSize.medium,
                onPressed: onTapInstallments,
                icon: tokens.icons.arrowDown,
                iconPosition: CdsButtonIconPosition.right,
                expanded: true,
              ),
            ],
          ),
        ],
      ),
      showMargin: false,
      showBorder: false,
      leftIcon: Image(
        image: selectedCard.brandIcon,
        height: 20,
        width: 20,
      ),
      isChecked: hasPaymentAndCardSelected,
    );
  }
}
