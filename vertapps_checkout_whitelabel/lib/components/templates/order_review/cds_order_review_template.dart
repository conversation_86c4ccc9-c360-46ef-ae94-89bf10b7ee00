import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:vertapps_checkout_whitelabel/components/components.dart';
import 'package:vertapps_checkout_whitelabel/components/composition_components/cds_pickup_bottom_sheet/model/cds_pickup_order_view.dart';
import 'package:vertapps_checkout_whitelabel/components/templates/feedback/models/order_resume_config.dart';
import 'package:vertapps_checkout_whitelabel/components/templates/common/widgets/order_resume.dart';
import 'package:vertapps_checkout_whitelabel/components/templates/feedback/models/payment_method_option.dart';
import 'package:vertapps_checkout_whitelabel/components/templates/order_review/config/delivery_info_config.dart';
import 'package:vertapps_checkout_whitelabel/components/templates/order_review/config/discount_coupon_config.dart';
import 'package:vertapps_checkout_whitelabel/components/templates/order_review/config/payment_info_config.dart';
import 'package:vertapps_checkout_whitelabel/components/templates/order_review/config/seller_coupon_config.dart';
import 'package:vertapps_checkout_whitelabel/components/templates/order_review/widgets/delivery_widget.dart';
import 'package:vertapps_checkout_whitelabel/components/templates/order_review/widgets/discount_coupon_widget.dart';
import 'package:vertapps_checkout_whitelabel/components/templates/order_review/widgets/payment_card_info_widget.dart';
import 'package:vertapps_checkout_whitelabel/components/templates/templates.dart';
import 'widgets/seller_coupon_widget.dart';

class CdsOrderReviewTemplate extends CdsBaseStatefulTemplate {
  const CdsOrderReviewTemplate({
    super.key,
    required super.tokens,
    required this.goToBackPack,
    required this.orderForm,
    required this.paymentSelected,
    required this.selectedInstallmentForCard,
    required this.showInstallmentsBottomSheet,
    required this.amountToUseCreditBalance,
    required this.showCreditBottomSheet,
    required this.getSalesPersonCodeApplied,
    required this.isLoading,
    required this.isButtonLoading,
    required this.isButtonDisabled,
    required this.finishPurchase,
    required this.removeSellerCode,
    required this.totalCreditBalance,
    required this.showSellerCodeBottomSheet,
    required this.removeCoupon,
    required this.showBottomSheetAddCoupon,
    required this.onTapViewBalance,
    required this.redirectToPayment,
    required this.cvvController,
    required this.onTapRedirectToPayment,
    required this.payOnlyCreditBalance,
    required this.packages,
    required this.paymentMethodOption,
    this.applePayButton,
    this.onTapBack,
    this.pickupPackage,
  });

  final Function({bool? showBottomSheet}) goToBackPack;
  final OrderForm? orderForm;
  final PaymentType? paymentSelected;
  final String? selectedInstallmentForCard;
  final VoidCallback showInstallmentsBottomSheet;
  final double totalCreditBalance;
  final double amountToUseCreditBalance;
  final VoidCallback showCreditBottomSheet;
  final String? getSalesPersonCodeApplied;
  final VoidCallback finishPurchase;
  final bool isLoading;
  final bool isButtonLoading;
  final bool isButtonDisabled;
  final VoidCallback removeSellerCode;
  final VoidCallback showSellerCodeBottomSheet;
  final VoidCallback removeCoupon;
  final VoidCallback showBottomSheetAddCoupon;
  final Widget? applePayButton;
  final bool redirectToPayment;
  final VoidCallback onTapViewBalance;
  final TextEditingController cvvController;
  final VoidCallback onTapRedirectToPayment;
  final void Function()? onTapBack;
  final bool payOnlyCreditBalance;
  final List<CdsPackModel> packages;
  final CdsPickupOrderView? pickupPackage;
  final PaymentMethodOption? paymentMethodOption;

  @override
  State<CdsOrderReviewTemplate> createState() => _CdsOrderReviewTemplateState();
}

class _CdsOrderReviewTemplateState extends State<CdsOrderReviewTemplate> {
  bool showProductDetails = false;
  Widget _buildSeparator() => Container(
        height: widget.tokens.layout.spacing.md,
        color: widget.tokens.colors.surface.surfaceCard,
      );

  CdsTertiaryButton _buildCta({
    required String buttonText,
    required VoidCallback? onPressed,
    IconData? icon,
    double? buttonHeight,
    double? iconSize,
  }) =>
      CdsButton.tertiary(
        tokens: widget.tokens,
        onPressed: onPressed,
        buttonText: buttonText,
        icon: icon ?? widget.tokens.icons.arrowRight,
        iconPosition: CdsButtonIconPosition.right,
        padding: EdgeInsets.zero,
        size: CdsButtonSize.small,
        height: buttonHeight ?? 16.0,
        iconSize: iconSize,
      );
  @override
  Widget build(BuildContext context) {
    final address = widget.orderForm?.shippingData?.address;
    final couponTitle = widget.orderForm?.getAppliedCoupon();
    final hasDiscountCoupon =
        couponTitle != null && couponTitle.isNotEmpty == true;
    final selectedApplePay = widget.paymentSelected == PaymentType.applePay;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: EdgeInsets.all(
            widget.tokens.layout.padding.lg,
          ),
          decoration: BoxDecoration(boxShadow: [
            BoxShadow(
              color: widget.tokens.colors.surface.surfaceBackground
                  .withOpacity(0.04),
              blurRadius: 8,
              offset: const Offset(0, 4),
            )
          ]),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  CdsButton.tertiary(
                    onPressed: widget.onTapBack ??
                        () {
                          Navigator.of(context).pop();
                        },
                    tokens: widget.tokens,
                    icon: widget.tokens.icons.arrowLeft,
                    size: CdsButtonSize.small,
                    type: CdsButtonType.icon,
                  ),
                  const Spacer(),
                  CdsButton.tertiary(
                    onPressed: widget.goToBackPack,
                    tokens: widget.tokens,
                    icon: widget.tokens.icons.close,
                    size: CdsButtonSize.small,
                    type: CdsButtonType.icon,
                  ),
                ],
              ),
              SizedBox(height: widget.tokens.layout.spacing.lg),
              Text(
                'Finalização do pedido',
                style: widget.tokens.typography.subtitleStyles.subtitle2,
              ),
            ],
          ),
        ),
        _buildSeparator(),
        Padding(
          padding: EdgeInsets.symmetric(
            horizontal: widget.tokens.layout.padding.lg,
            vertical: widget.tokens.layout.padding.md,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (widget.paymentMethodOption != null)
                PaymentCardInfoWidget(
                  tokens: widget.tokens,
                  config: PaymentInfoConfig(
                    changePayment: () {},
                    hasPixDiscount: false,
                    selectedPaymentMethod: widget.paymentMethodOption!,
                  ),
                  ctaButton: _buildCta(
                    buttonText: "Alterar",
                    onPressed: () {
                      if (widget.redirectToPayment) {
                        widget.onTapRedirectToPayment.call();
                      } else {
                        Navigator.of(context).pop();
                      }
                    },
                  ),
                ),
              if (widget.paymentSelected == PaymentType.creditCard) ...[
                if (widget.redirectToPayment) ...[
                  SizedBox(
                    height: widget.tokens.layout.spacing.lg,
                  ),
                  CdsTextField(
                    label: 'CVV',
                    controller: widget.cvvController,
                    tokens: widget.tokens,
                    keyboardType: TextInputType.number,
                    inputFormatter: [
                      LengthLimitingTextInputFormatter(4),
                    ],
                  ),
                ],
                SizedBox(
                  height: widget.tokens.layout.spacing.lg,
                ),
                Text(
                  'Parcelamento:',
                  style:
                      widget.tokens.typography.bodyStyles.description.copyWith(
                    color: widget.tokens.colors.content.content01,
                  ),
                ),
                SizedBox(
                  height: widget.tokens.layout.spacing.md,
                ),
                CdsSecondaryButton(
                  tokens: widget.tokens,
                  buttonText: widget.selectedInstallmentForCard != null
                      ? '${widget.selectedInstallmentForCard} sem juros'
                      : 'Selecione uma opção',
                  size: CdsButtonSize.medium,
                  onPressed: widget.showInstallmentsBottomSheet,
                  icon: widget.tokens.icons.arrowDown,
                  iconPosition: CdsButtonIconPosition.right,
                  expanded: true,
                ),
              ],
              if (widget.totalCreditBalance > 0) ...[
                SizedBox(
                  height: widget.tokens.layout.spacing.md,
                ),
                CdsFeedback(
                  tokens: widget.tokens,
                  config: CdsFeedbackConfig(
                    title: widget.amountToUseCreditBalance != 0
                        ? '${CurrencyHelper.format(amount: widget.amountToUseCreditBalance, dividedBy100: true)} de crédito aplicados'
                        : 'Você ainda tem saldo na carteira',
                    titleStyle:
                        widget.tokens.typography.bodyStyles.caption.copyWith(
                      color:
                          widget.tokens.colors.feedback.feedbackSuccess.content,
                    ),
                    type: FeedbackType.success,
                    hasBorderRadius: true,
                    hasLeftIcon: false,
                    rightIcon: _buildCta(
                      buttonText: "Aplicar",
                      onPressed: widget.showCreditBottomSheet,
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
        _buildSeparator(),
        Padding(
          padding: EdgeInsets.symmetric(
            horizontal: widget.tokens.layout.padding.lg,
            vertical: widget.tokens.layout.padding.md,
          ),
          child: Row(
            children: [
              Text(
                'Entrega',
                style: widget.tokens.typography.bodyStyles.paragraph,
              ),
              const Spacer(),
              _buildCta(
                  buttonText: 'Alterar',
                  onPressed: () {
                    widget.goToBackPack(showBottomSheet: false);
                  }),
            ],
          ),
        ),
        SizedBox(height: widget.tokens.layout.spacing.md),
        Visibility(
          visible: widget.pickupPackage != null,
          child: Padding(
            padding: EdgeInsets.symmetric(
              horizontal: widget.tokens.layout.padding.lg,
              vertical: widget.tokens.layout.padding.md,
            ),
            child: DeliveryWidget(
              tokens: widget.tokens,
              config: DeliveryInfoConfig(
                products: widget.pickupPackage?.products ?? [],
                termOverride: "Retire ",
                addressTitle: 'na ${widget.pickupPackage?.storeName}',
                deliveryTitle: "em até ${widget.pickupPackage?.estimateText}",
                deliveryAddress: '${widget.pickupPackage?.addressText}',
                onChangeDelivery: () {
                  widget.goToBackPack();
                },
              ),
            ),
          ),
        ),
        ...widget.packages.map(
          (package) => Padding(
            padding: EdgeInsets.symmetric(
              horizontal: widget.tokens.layout.padding.lg,
              vertical: widget.tokens.layout.padding.md,
            ),
            child: DeliveryWidget(
              tokens: widget.tokens,
              config: DeliveryInfoConfig(
                products: package.products,
                addressTitle: 'em ${address?.addressName ?? address?.street}',
                deliveryTitle:
                    package.selectedOption?.getShippingEstimatedFormatted ?? '',
                deliveryAddress: widget.orderForm?.currentFullAddress ?? '',
                onChangeDelivery: () {
                  widget.goToBackPack();
                },
              ),
            ),
          ),
        ),
        _buildSeparator(),
        Padding(
          padding:
              EdgeInsets.symmetric(horizontal: widget.tokens.layout.padding.lg),
          child: SellerCouponWidget(
            tokens: widget.tokens,
            config: SellerCouponConfig(
              onChangeSellerCoupon: () {},
              onRemoveSellerCoupon: widget.removeSellerCode,
              couponTitle: widget.getSalesPersonCodeApplied ?? '',
            ),
            ctaButton: _buildCta(
              buttonText: widget.getSalesPersonCodeApplied != null
                  ? 'Alterar'
                  : 'Adicionar',
              onPressed: widget.showSellerCodeBottomSheet,
              buttonHeight: 22.0,
              iconSize: 16.0,
            ),
          ),
        ),
        CdsDivider(
          dividerSize: CdsDividerSize.small,
          tokens: widget.tokens,
          isExpanded: true,
        ),
        Padding(
          padding:
              EdgeInsets.symmetric(horizontal: widget.tokens.layout.padding.lg),
          child: DiscountCouponWidget(
            tokens: widget.tokens,
            config: DiscountCouponConfig(
              onChangeCoupon: () {},
              onRemoveCoupon: widget.removeCoupon,
              couponTitle: couponTitle ?? '',
            ),
            ctaButton: _buildCta(
              buttonText: hasDiscountCoupon ? 'Alterar' : 'Adicionar',
              onPressed: widget.showBottomSheetAddCoupon,
              buttonHeight: 22.0,
              iconSize: 16.0,
            ),
          ),
        ),
        _buildSeparator(),
        Container(
          decoration: BoxDecoration(
            color: widget.tokens.colors.surface.surfaceCard,
          ),
          child: OrderResume(
            tokens: widget.tokens,
            orderResumeConfig: OrderResumeConfig(
              productsCount: widget.orderForm?.getAddedItems.length ?? 0,
              totalProductsValue:
                  widget.orderForm?.getTotalItemsFormatted ?? '',
              totalShippingValue: widget.orderForm?.shippingPrice ?? '',
              totalDiscountsValue: (widget.orderForm?.hasDiscount ?? false)
                  ? (widget.orderForm?.discountPrice ?? '')
                  : '',
              totalCreditsValue:
                  ((widget.orderForm?.paymentData?.giftCards?.isNotEmpty ??
                              false) &&
                          (widget.orderForm?.getGiftCardValues() != 0))
                      ? CurrencyHelper.format(
                          amount: widget.orderForm?.getGiftCardValues() ?? 0,
                          dividedBy100: true,
                        )
                      : null,
              totalOrderValue: widget.paymentSelected == PaymentType.creditCard
                  ? widget.orderForm?.getCurrentInstallment ?? ''
                  : widget.orderForm?.totalFormatted ?? '',
              backgroundColor: widget.tokens.colors.surface.surfaceCard,
            ),
          ),
        ),
        SizedBox(height: widget.tokens.layout.spacing.xxl),
      ],
    );
  }
}

//TODO mover extensão para um lugar mais apropriado
extension on List<OrderFormProduct> {
  /// Retorna uma lista de [CdsSmallProductModel], model utilizado no Checkout, que contém
  /// as informações dos produtos atrelados a um pacote no [OrderForm].
  List<CdsProductShortModel> toCdsProductModel() {
    return map(
      (product) => CdsProductShortModel(
        productId: product.id ?? '',
        price: CdsProductPriceModel(
          fullPrice: product.listPrice?.toDouble() ?? 0,
          salePrice: product.sellingPrice?.toDouble() ?? 0,
        ),
        title: product.name ?? '',
        imageUrl: product.imageUrl ?? '',
        size: product.size ?? '',
        quantity: product.quantity?.toString() ?? '0',
      ),
    ).toList();
  }
}
