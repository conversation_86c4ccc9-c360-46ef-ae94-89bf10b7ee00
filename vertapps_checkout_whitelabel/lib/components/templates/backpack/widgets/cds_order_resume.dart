import 'package:flutter/material.dart';
import 'package:vertapps_checkout_whitelabel/components/components.dart';

/// Configurações do componente de resumo da compra
/// Todas as strings devem ser enviadas prontas para exibição
class CdsOrderResumeConfig {
  /// Quantidade de produtos
  final int productLength;

  /// Valor total dos produtos
  final String totalProducts;

  /// Valor total final
  final String totalValue;

  /// Valor da entrega
  final String deliveryValue;

  /// Valor de desconto
  final String? discountValue;

  /// Valor de saldo utlizado
  final String? totalCreditsValue;

  /// Número máximo de parcelas
  final int? maxInstallments;

  CdsOrderResumeConfig({
    required this.maxInstallments,
    required this.productLength,
    required this.totalProducts,
    required this.totalValue,
    required this.deliveryValue,
    this.discountValue,
    this.totalCreditsValue,
  });
}

class CdsOrderResume extends CdsBaseStatelessComponent {
  const CdsOrderResume({
    super.key,
    required super.tokens,
    required this.config,
  });

  final CdsOrderResumeConfig config;

  @override
  Widget build(BuildContext context) {
    final TextStyle commonDescriptionStyle =
        tokens.typography.bodyStyles.caption.copyWith(
      color: tokens.colors.content.content03,
      fontWeight: FontWeight.w400,
    );
    final TextStyle commonStyle =
        tokens.typography.bodyStyles.paragraph.copyWith(
      color: tokens.colors.content.pure,
      fontWeight: FontWeight.w400,
    );
    final TextStyle valueStyle = tokens.typography.bodyStyles.caption.copyWith(
      color: tokens.colors.content.pure,
      fontWeight: FontWeight.w400,
    );

    return Container(
      color: tokens.colors.surface.surfaceCard,
      padding: EdgeInsets.symmetric(vertical: tokens.layout.padding.xl),
      child: Column(
        children: [
          Padding(
            padding: EdgeInsets.symmetric(horizontal: tokens.layout.padding.lg),
            child:
                Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
              Text(
                'Resumo do pedido',
                style: commonStyle,
              ),
              SizedBox(height: tokens.layout.spacing.lg),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '${config.productLength} ${config.productLength == 1 ? 'produto' : 'produtos'}',
                    style: commonDescriptionStyle,
                  ),
                  Text('${config.totalProducts}', style: valueStyle),
                ],
              ),
              SizedBox(height: tokens.layout.spacing.sm),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text('Entrega', style: commonDescriptionStyle),
                  Text(
                    '${config.deliveryValue.isEmpty ? "a calcular" : config.deliveryValue}',
                    style: valueStyle,
                  ),
                ],
              ),
              if (config.discountValue != null &&
                  config.discountValue!.isNotEmpty) ...[
                SizedBox(height: tokens.layout.spacing.sm),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text('Descontos', style: commonDescriptionStyle),
                    Text(
                      config.discountValue!,
                      style: valueStyle.copyWith(
                        color: tokens.colors.feedback.feedbackSuccess.content,
                      ),
                    ),
                  ],
                ),
              ],
              if (config.totalCreditsValue != null &&
                  config.totalCreditsValue!.isNotEmpty) ...[
                SizedBox(height: tokens.layout.spacing.sm),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text('Créditos usados', style: commonDescriptionStyle),
                    Text(
                      '- ${config.totalCreditsValue}',
                      style: valueStyle.copyWith(
                        color: tokens.colors.feedback.feedbackSuccess.content,
                      ),
                    ),
                  ],
                ),
              ],
              SizedBox(height: tokens.layout.spacing.md),
            ]),
          ),
          CdsDivider(
            dividerSize: CdsDividerSize.small,
            isExpanded: true,
            tokens: tokens,
          ),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: tokens.layout.padding.lg),
            child: Column(
              children: [
                SizedBox(height: tokens.layout.spacing.md),
                Row(
                  children: [
                    Text('Total',
                        style: tokens.typography.bodyStyles.description
                            .copyWith(color: tokens.colors.content.content01)),
                    SizedBox(width: tokens.layout.padding.xxs),
                    Text('em até ${config.maxInstallments}x',
                        style: commonDescriptionStyle),
                    Spacer(),
                    Text(
                      '${config.totalValue}',
                      style: commonStyle,
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
