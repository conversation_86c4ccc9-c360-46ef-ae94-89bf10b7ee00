import 'package:azzas_analytics/events/payment/order_completion_events.dart';
import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:flutter/material.dart';
import 'package:vertapps_checkout_whitelabel/components/templates/feedback/models/feedback_package_info.dart';
import 'package:vertapps_checkout_whitelabel/components/templates/feedback/models/feedback_top_bar_config.dart';
import 'package:vertapps_checkout_whitelabel/components/templates/feedback/models/order_resume_config.dart';
import 'package:vertapps_checkout_whitelabel/components/templates/feedback/models/payment_method_option.dart';
import 'package:vertapps_checkout_whitelabel/components/templates/feedback/models/selected_payment_config.dart';
import 'package:vertapps_checkout_whitelabel/components/templates/templates.dart';

class CdsOrderPlacedTemplate extends CdsBaseStatefulTemplate {
  const CdsOrderPlacedTemplate({
    super.key,
    required super.tokens,
    required this.orderId,
    required this.getCouponApplied,
    required this.selectedCard,
    required this.onTapViewBalance,
    required this.orderFormCopy,
    required this.userInfo,
    required this.isLoading,
    required this.backHome,
    required this.getPaymentMethod,
    required this.showFavoriteAddressPaymentBottomSheet,
    required this.getSalesPersonNumberCodeApplied,
    required this.activeOrders,
    this.availableCheckingAccountValue,
    required this.onTapViewBag,
  });

  final String orderId;
  final Function() onTapViewBalance;
  final Function() onTapViewBag;
  final OrderForm? orderFormCopy;
  final UserInfo? userInfo;
  final bool isLoading;
  final Function() backHome;
  final String? availableCheckingAccountValue;
  final String? getCouponApplied;
  final Payment? selectedCard;
  final String? getSalesPersonNumberCodeApplied;
  final PaymentMethodOption getPaymentMethod;
  final bool? showFavoriteAddressPaymentBottomSheet;
  final List<ListUserOrder> activeOrders;

  @override
  State<CdsOrderPlacedTemplate> createState() => _CdsOrderPlacedTemplateState();
}

class _CdsOrderPlacedTemplateState extends State<CdsOrderPlacedTemplate>
    with TickerProviderStateMixin {
  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) {
        if (didPop) return;
        widget.backHome();
      },
      child: Scaffold(
        body: SafeArea(
          child: Column(
            children: [
              widget.orderFormCopy == null
                  ? Expanded(
                      child: SingleChildScrollView(
                        child: widget.isLoading
                            ? const SizedBox(
                                height: 64,
                                child: Center(
                                  child: AzzasSpinner(),
                                ),
                              )
                            : Column(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Row(
                                    children: [
                                      IconButton(
                                        onPressed: () {
                                          widget.backHome();
                                        },
                                        icon: Icon(
                                          widget.tokens.icons.arrowLeft,
                                          size: 32.0,
                                        ),
                                      ),
                                    ],
                                  ),
                                  Center(
                                    child: Text(
                                      "Erro ao encontrar pedido",
                                      style: widget
                                          .tokens.typography.titleStyles.title4,
                                    ),
                                  ),
                                ],
                              ),
                      ),
                    )
                  : Expanded(
                      child: ListView(
                        scrollDirection: Axis.vertical,
                        children: [
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              CdsFeedbackTemplate(
                                config: FeedbackTemplateConfig(
                                  orderId: widget.orderId,
                                  topBarConfig: FeedbackTopBarConfig(
                                    title:
                                        'Seu pedido foi concluído com sucesso!',
                                    subtitle:
                                        'Em breve você receberá a confirmação do pedido no e-mail ',
                                    onTapClose: () async {
                                      await OrderCompletionEvents
                                          .logSelectContent(
                                              local: 'checkout_order_completed',
                                              contentType:
                                                  'finalizacao-compra');
                                      widget.backHome();
                                    },
                                    emailUser:
                                        widget.orderFormCopy?.clientProfileData?.email ?? '',
                                  ),
                                  packages: [
                                    ...getPackages(),
                                  ],
                                  cashbackText: '',
                                  orderResumeConfig: OrderResumeConfig(
                                    productsCount: widget.orderFormCopy
                                            ?.getAddedItems.length ??
                                        0,
                                    totalProductsValue: widget.orderFormCopy
                                            ?.getTotalItemsFormatted ??
                                        '',
                                    totalShippingValue: CurrencyHelper.format(
                                      amount:
                                          widget.orderFormCopy?.shippingValue,
                                    ),
                                    totalDiscountsValue:
                                        (widget.orderFormCopy?.hasDiscount ??
                                                false)
                                            ? (widget.orderFormCopy
                                                    ?.discountPrice ??
                                                '')
                                            : '',
                                    totalCreditsValue: ((widget
                                                    .orderFormCopy
                                                    ?.paymentData
                                                    ?.giftCards
                                                    ?.isNotEmpty ??
                                                false) &&
                                            (widget.orderFormCopy
                                                    ?.getGiftCardValues() !=
                                                0))
                                        ? CurrencyHelper.format(
                                            amount: widget.orderFormCopy
                                                    ?.getGiftCardValues() ??
                                                0,
                                            dividedBy100: true,
                                          )
                                        : null,
                                    totalOrderValue: (widget.orderFormCopy
                                            ?.getCurrentInstallment ??
                                        ''),
                                    packagesCount: widget
                                            .orderFormCopy
                                            ?.shippingData
                                            ?.logisticsInfo
                                            ?.length ??
                                        1,
                                  ),
                                  selectedPaymentConfig: SelectedPaymentConfig(
                                      totalDiscountsValue:
                                          widget.orderFormCopy?.discountPrice,
                                      orderForm: widget.orderFormCopy,
                                      selectedCard: widget.selectedCard,
                                      selectedPaymentMethod:
                                          widget.getPaymentMethod,
                                      creditVoucherValue:
                                          widget.availableCheckingAccountValue,
                                      couponName: widget.getCouponApplied,
                                      sellerCode: widget
                                              .getSalesPersonNumberCodeApplied ??
                                          '',
                                      showFavoriteAddressPaymentBottomSheet: widget
                                          .showFavoriteAddressPaymentBottomSheet,
                                      totalCreditsValue: ((widget
                                                      .orderFormCopy
                                                      ?.paymentData
                                                      ?.giftCards
                                                      ?.isNotEmpty ??
                                                  false) &&
                                              (widget.orderFormCopy
                                                      ?.getGiftCardValues() !=
                                                  0))
                                          ? CurrencyHelper.format(
                                              amount: widget.orderFormCopy
                                                      ?.getGiftCardValues() ??
                                                  0,
                                              dividedBy100: true,
                                            )
                                          : null),
                                  availableCashbackValue: "R\$0,00",
                                  availableCheckingAccountValue:
                                      widget.availableCheckingAccountValue ??
                                          "R\$0,00",
                                  onTapViewBalance: widget.onTapViewBalance,
                                ),
                                tokens: widget.tokens,
                              ),
                              Container(
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 24),
                                child: AzzasSecondaryButton(
                                  expanded: true,
                                  onPressed: () async {
                                    await OrderCompletionEvents
                                        .logSelectContent(
                                            local: 'checkout_order_completed',
                                            contentType: 'continuar-comprando');

                                    widget.onTapViewBag();
                                  },
                                  child: const Text('Continuar comprando'),
                                ),
                              ),
                              SizedBox(
                                height: 24,
                              ),
                            ],
                          ),
                        ],
                      ),
                    )
            ],
          ),
        ),
      ),
    );
  }

  List<FeedbackPackageInfo> getPackages() {
    if (widget.activeOrders.isEmpty) return [];

    List<FeedbackPackageInfo> items = [];

    for (var package in widget.activeOrders) {
      items.add(
        FeedbackPackageInfo(
          packageId: package.orderId ?? "",
          deliveryAddressText: package.deliveryType == 'retirar na loja'
              ? "Retirada na loja ${package.getStoreName()}"
              : "Receba em casa",
          deliveryEstimateDateText: package.deliveryType == 'retirar na loja'
              ? "Disponível para retirada entre ${package.getStorePickupTime()}"
              : package.formatBusinessDeliveryDate(),
          // deliveryEstimateDateText: 'tgdrtert',
          fullAddress: package.getFullAddress(),
          productImages: (package.items ?? []).map((item) {
            return item.getImageProductOrdered;
          }).toList(),
          totalValue: package.totalDescription,
        ),
      );
    }

    return items;
  }
}
