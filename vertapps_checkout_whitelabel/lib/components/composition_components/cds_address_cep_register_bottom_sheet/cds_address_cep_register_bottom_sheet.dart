import 'package:flutter/material.dart';
import 'package:vertapps_checkout_whitelabel/components/composition_components/cds_bottom_sheet/cds_sheet.dart';
import 'package:vertapps_checkout_whitelabel/components/composition_components/cds_bottom_sheet/enum/cds_sheet_type.dart';
import 'package:vertapps_checkout_whitelabel/components/components.dart';
import 'package:vertapps_checkout_whitelabel/components/core_components/cds_textfield/enum/cds_text_field_mask.dart';

class CdsAddressCepRegisterBottomSheet extends CdsBaseStatelessComponent {
  final TextEditingController cepTextEditingController;
  final VoidCallback onClose;
  final VoidCallback onBack;
  final bool isLoading;
  final bool isCepValid;

  CdsAddressCepRegisterBottomSheet({
    required super.tokens,
    required this.cepTextEditingController,
    required this.onClose,
    required this.onBack,
    required this.isLoading,
    required this.isCepValid,
  });
  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: SingleChildScrollView(
        child: CdsSheet(
          tokens: tokens,
          type: CdsSheetType.bottomSheet,
          onClose: onClose,
          onBack: onBack,
          title: 'Novo endereço',
          subtitle:
              'Insira os dados do endereço que você deseja receber seu pedido',
          buttonText: 'Digite um CEP válido',
          isButtonDisabled: !isCepValid && !isLoading,
          slotContent: Form(
            child:
                Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
              SizedBox(height: tokens.layout.spacing.lg),
              CdsTextField(
                label: 'Digite seu CEP',
                controller: cepTextEditingController,
                tokens: tokens,
                mask: CdsInputMask.cep,
                isEnabled: !isCepValid,
                keyboardType: TextInputType.number,
              ),
              SizedBox(height: tokens.layout.spacing.md),
              if (isLoading) ...[
                Center(
                    child: Padding(
                  padding:
                      EdgeInsets.symmetric(vertical: tokens.layout.padding.sm),
                  child: CdsSpinner(tokens: tokens),
                )),
              ],
            ]),
          ),
        ),
      ),
    );
  }
}
