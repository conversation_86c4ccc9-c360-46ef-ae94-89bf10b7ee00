import 'package:storybook/stories/ds_stories.dart';
import 'package:storybook_flutter/storybook_flutter.dart';
import 'package:vertapps_checkout_whitelabel/tokens/checkout_ds_tokens.dart';
import 'package:storybook/stories/templates/login/login_code_story.dart';
import 'package:storybook/stories/templates/login/login_complete_user_data_story.dart';
import 'package:storybook/stories/templates/login/login_create_password_story.dart';
import 'package:storybook/stories/templates/login/login_email_password_story.dart';
import 'package:storybook/stories/templates/login/login_email_story.dart';
import 'package:storybook/stories/templates/login/login_story.dart';

class LoginTemplatesStory extends DSstories {
  @override
  List<Story> call(CheckoutDStokens tokens) {
    return [
      ...LoginStory().call(tokens),
      ...LoginCodeStory().call(tokens),
      ...LoginCompleteUserDataStory().call(tokens),
      ...LoginCreatePasswordStory().call(tokens),
      ...LoginEmailPasswordStory().call(tokens),
      ...LoginEmailStory().call(tokens),
    ];
  }
}
