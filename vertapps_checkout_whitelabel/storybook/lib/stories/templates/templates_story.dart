import 'package:storybook/stories/ds_stories.dart';
import 'package:storybook/stories/templates/backpack/backpack_empty_template_story.dart';
import 'package:storybook/stories/templates/feedback_story.dart';
import 'package:storybook/stories/templates/login/login_story.dart';
import 'package:storybook/stories/templates/order_review_story.dart';
import 'package:storybook/stories/templates/Address/address_templates_story.dart';
import 'package:storybook/stories/templates/login/login_templates_story.dart';
import 'package:storybook/stories/templates/backpack/backpack_template_story.dart';
import 'package:storybook/stories/templates/payment_story.dart';
import 'package:storybook_flutter/storybook_flutter.dart';
import 'package:vertapps_checkout_whitelabel/tokens/checkout_ds_tokens.dart';

class TemplatesStory extends DSstories {
  @override
  List<Story> call(CheckoutDStokens tokens) {
    return [
      ...FeedbackStory().call(tokens),
      ...LoginStory().call(tokens),
      ...OrderReviewStory().call(tokens),
      ...LoginTemplatesStory().call(tokens),
      ...AddressTemplateStory().call(tokens),
      ...PaymentStory().call(tokens),
      ...BackpackTemplateStory().call(tokens),
      ...BackpackEmptyTemplateStory().call(tokens),
    ];
  }
}
