import 'package:flutter/material.dart';
import 'package:storybook/stories/components/composition_components/cds_product_item/utils/show_snackbar.dart';
import 'package:storybook_flutter/storybook_flutter.dart';
import 'package:vertapps_checkout_whitelabel/components/components.dart';
import 'package:vertapps_checkout_whitelabel/components/templates/backpack/backpack.dart';
import 'package:vertapps_checkout_whitelabel/tokens/checkout_ds_tokens.dart';

class BackpackEmptyTemplateStory {
  List<Story> call(CheckoutDStokens tokens) {
    return [
      Story(
        name: 'Templates / Backpack / Sem produtos',
        builder: (context) {
          final title = context.knobs.text(
            label: "<PERSON>ítu<PERSON>",
            initial: "Sua mochila está vazia",
          );

          final description = context.knobs.text(
            label: "Descrição",
            initial: "Confira os produtos salvos na sua lista de desejos ou explore nossas novidades!",
          );

          final buttonText = context.knobs.text(
            label: "Texto do botão",
            initial: "Explorar novidades",
          );

          final imageUrl = context.knobs.text(
            label: "Imagem",
            initial: "https://t3.ftcdn.net/jpg/07/05/22/98/360_F_705229898_6MV4F9FPWLFzz1pWVmr3BNnls9s8b1x4.jpg",
          );

          const int fullPrice = 320;
          const int salePrice = 300;
          const String quantity = "1";
          const String size = "100";
          const CdsProductStatus status = CdsProductStatus.available;
          const bool isGiftProduct = false;
          const bool isEnabled = false;
          const bool isSelected = false;

          final products = [
            CdsProductModel(
              isAvailable: true,
              productId: '1',
              price: CdsProductPriceModel(
                fullPrice: fullPrice.toDouble(),
                salePrice: salePrice.toDouble(),
              ),
              quantity: quantity,
              size: size,
              title: title,
              shippingDate: 3,
              status: status,
              imageUrl: imageUrl,
              isGiftProduct: isGiftProduct,
              checkbox: CdsProductCheckboxModel(
                isEnabled: isEnabled,
                value: isSelected,
                onChanged: (newValue) {},
              ),
              ctas: CdsProductCtasModel(
                onSave: ({required String itemId, required String productId}) async {
                  ShowSnackbar.show(context, "Produto $productId salvo com sucesso!", tokens);
                },
                onRemove: ({required String itemId}) async {
                  ShowSnackbar.show(context, "Produto $itemId removido.", tokens);
                },
                onWarning: ({required String productId}) async {
                  ShowSnackbar.show(context, "Aviso sobre o produto $productId.", tokens);
                },
                onProductInfo: ({
                  required String itemId,
                  required String productId,
                }) async {
                  ShowSnackbar.show(context, "Sobre o produto $productId.", tokens);
                },
              ),
              itemId: '1',
            ),
            CdsProductModel(
              isAvailable: true,
              productId: '2',
              price: CdsProductPriceModel(
                fullPrice: fullPrice.toDouble(),
                salePrice: salePrice.toDouble(),
              ),
              quantity: quantity,
              size: size,
              title: title,
              shippingDate: 3,
              status: status,
              imageUrl: imageUrl,
              isGiftProduct: isGiftProduct,
              checkbox: CdsProductCheckboxModel(
                isEnabled: isEnabled,
                value: isSelected,
                onChanged: (newValue) {},
              ),
              ctas: CdsProductCtasModel(
                onSave: ({required String itemId, required String productId}) async {
                  ShowSnackbar.show(context, "Produto $productId salvo com sucesso!", tokens);
                },
                onRemove: ({required String itemId}) async {
                  ShowSnackbar.show(context, "Produto $itemId removido.", tokens);
                },
                onWarning: ({required String productId}) async {
                  ShowSnackbar.show(context, "Aviso sobre o produto $productId.", tokens);
                },
                onProductInfo: ({
                  required String itemId,
                  required String productId,
                }) async {
                  ShowSnackbar.show(context, "Sobre o produto $productId.", tokens);
                },
              ),
              itemId: '2',
            ),
            CdsProductModel(
              isAvailable: true,
              productId: '3',
              price: CdsProductPriceModel(
                fullPrice: fullPrice.toDouble(),
                salePrice: salePrice.toDouble(),
              ),
              quantity: quantity,
              size: size,
              title: title,
              shippingDate: 3,
              status: status,
              imageUrl: imageUrl,
              isGiftProduct: isGiftProduct,
              checkbox: CdsProductCheckboxModel(
                isEnabled: isEnabled,
                value: isSelected,
                onChanged: (newValue) {},
              ),
              ctas: CdsProductCtasModel(
                onSave: ({required String itemId, required String productId}) async {
                  ShowSnackbar.show(context, "Produto $productId salvo com sucesso!", tokens);
                },
                onRemove: ({required String itemId}) async {
                  ShowSnackbar.show(context, "Produto $itemId removido.", tokens);
                },
                onWarning: ({required String productId}) async {
                  ShowSnackbar.show(context, "Aviso sobre o produto $productId.", tokens);
                },
                onProductInfo: ({
                  required String itemId,
                  required String productId,
                }) async {
                  ShowSnackbar.show(context, "Sobre o produto $productId.", tokens);
                },
              ),
              itemId: '3',
            ),
          ];

          CdsBrandInformationConfig brandInformationConfig = const CdsBrandInformationConfig(
            imageUrl: "https://upload.wikimedia.org/wikipedia/commons/a/a3/Image-not-found.png?20210521171500",
            title: "Título com até duas linhas",
            description: "Descrição com até duas linhas",
          );

          CdsProductSmallCtas wishlistCtas =
              CdsProductSmallCtas(
              onWarnMe: (_) async {},
              onAddToBag: (_) async {},
              onRemoveFromWishlist: (_) async {});
          CdsWishlistProductsConfig wishlistConfig = CdsWishlistProductsConfig(
            productListLength: 5,
            title: "Seus desejos salvos",
            description: "Leve agora o que você deixou para depois",
            productsList: [
              CdsProductSmallModel.fromCdsProductModel(products[0], wishlistCtas),
              CdsProductSmallModel.fromCdsProductModel(products[1], wishlistCtas),
              CdsProductSmallModel.fromCdsProductModel(products[2], wishlistCtas),
              CdsProductSmallModel.fromCdsProductModel(products[0], wishlistCtas),
              CdsProductSmallModel.fromCdsProductModel(products[1], wishlistCtas),
            ],
            onTapLoadMoreProducts: () {},
            onChangeCategories: (categoryName) {},
            categories: const [
              CategoriesOptions(
                selected: false,
                name: "Vestido",
                quantity: 9,
              ),
              CategoriesOptions(
                selected: false,
                name: "Calça",
                quantity: 1,
              ),
              CategoriesOptions(
                selected: false,
                name: "Macacão",
                quantity: 8,
              ),
              CategoriesOptions(
                selected: false,
                name: "Blusa",
                quantity: 2,
              ),
              CategoriesOptions(
                selected: false,
                name: "Saia",
                quantity: 1,
              ),
              CategoriesOptions(
                selected: false,
                name: "Calcinha",
                quantity: 4,
              ),
            ],
          );

          return ScaffoldMessenger(
            child: CdsBackpackEmptyTemplate(
              imageUrl: imageUrl,
              buttonText: buttonText,
              onPressedButton: () {},
              configs: [brandInformationConfig, brandInformationConfig],
              description: description,
              title: title,
              tokens: tokens,
              wishlistConfig: wishlistConfig,
            ),
          );
        },
      )
    ];
  }
}
