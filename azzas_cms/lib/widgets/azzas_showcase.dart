import 'package:azzas_app_commons/azzas_app_commons.dart'
    show A<PERSON>sButton, AzzasButtonStyle, AzzasController<PERSON>ar<PERSON>l, EventDispatcher, Modular, Product;
import 'package:azzas_cms/components/azzas_spot_product/azzas_spot_product.dart';
import 'package:azzas_cms/components/azzas_spot_product/azzas_spot_product_loading_skeleton.dart';
import 'package:flutter/material.dart';
import 'package:visibility_detector/visibility_detector.dart';

class AzzasShowcaseButton {
  final String text;
  final void Function() onTap;
  final Color backgroundColor;
  final Color textColor;
  final double? radius;
  final Widget? trailing;

  const AzzasShowcaseButton(
      {required this.text,
      required this.onTap,
      required this.backgroundColor,
      required this.textColor,
      this.radius,
      this.trailing});
}

class AzzasShowcase extends StatefulWidget {
  final List<Product> productsShowcase;
  final List<AzzasShowcaseButton>? showcaseButtonList;
  final bool isLoading;
  final bool noMargin;
  final String titleText;
  final String? subtitleText;
  final TextStyle? titleStyle;
  final TextStyle? subtitleStyle;
  final AzzasShowcaseButton? callToAction;
  final void Function(Product product, int index) onTapProduct;
  final SpotHeight spotHeight;
  final double? priceTopSpacing;
  final double? priceLeftSpacing;
  final TextStyle? priceTextStyle;
  final TextStyle? oldPriceTextStyle;
  final bool? hasFavoriteButton;
  final bool? buildDynamicSpotSize;
  final bool? hasPlusButton;

  const AzzasShowcase({
    super.key,
    this.showcaseButtonList,
    this.isLoading = false,
    this.noMargin = false,
    required this.titleText,
    this.titleStyle,
    this.subtitleText,
    this.subtitleStyle,
    this.callToAction,
    this.productsShowcase = const [],
    required this.onTapProduct,
    this.spotHeight = SpotHeight.large,
    this.priceTopSpacing,
    this.priceLeftSpacing,
    this.priceTextStyle,
    this.oldPriceTextStyle,
    this.hasFavoriteButton,
    this.hasPlusButton,
    this.buildDynamicSpotSize = false,
  });

  @override
  State<AzzasShowcase> createState() => _AzzasShowcaseState();
}

class _AzzasShowcaseState extends State<AzzasShowcase> {
  List<AzzasShowcaseButton> buttons = [];
  int activeItemIndex = 0;
  int filterButtonSelectedIndex = 0;
  double dynamicImageWidth = 0.0;

  ScrollController productScrollController = ScrollController();

  bool _hasDispatchedEvent = false;
  final _eventDispatcher = Modular.get<EventDispatcher>();

  @override
  void initState() {
    super.initState();
    buttons = _getValidButtons();
    WidgetsBinding.instance.addPostFrameCallback(
        // Carrega os produtos da primeira opção, caso exista
        (_) {
      buttons.firstOrNull?.onTap();
      productScrollController.addListener(() {
        int position = productScrollController.position.pixels ~/ (260.0);
        _updatePaginationPosition(position);
      });
    });
  }

  @override
  void didUpdateWidget(covariant AzzasShowcase oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.showcaseButtonList != oldWidget.showcaseButtonList) {
      buttons = _getValidButtons();
    }
  }

  @override
  void dispose() {
    productScrollController.dispose();
    super.dispose();
  }

  List<AzzasShowcaseButton> _getValidButtons() {
    return widget.showcaseButtonList
            ?.where((b) => b.text.trim().isNotEmpty)
            .toList() ??
        [];
  }

  _updatePaginationPosition(int page) {
    setState(() {
      activeItemIndex = page;
    });
  }

  @override
  Widget build(BuildContext context) {
    return VisibilityDetector(
        key: Key('showcase_${widget.hashCode}'),
        onVisibilityChanged: (visibilityInfo) {
          if (!mounted || _hasDispatchedEvent) return;
          if (visibilityInfo.visibleFraction > 0.9) {
            if (widget.productsShowcase.isNotEmpty) {
              _eventDispatcher.logViewItemList(
                itemListName: widget.titleText,
                products: widget.productsShowcase,
              );
              _hasDispatchedEvent = true;
            }
          }
        },
      child: Container(
        padding: EdgeInsets.only(bottom: widget.noMargin ? 0 : 24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24.0),
              child: Text(
                widget.titleText,
                style: widget.titleStyle,
              ),
            ),
            if (widget.subtitleText?.trim().isNotEmpty ?? false) ...[
              const SizedBox(height: 8.0),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 24.0),
                child: Text(
                  widget.subtitleText ?? '',
                  style: widget.subtitleStyle,
                ),
              ),
            ],
            if (buttons.isNotEmpty) _buildButtons(),
            if (widget.callToAction?.text.trim().isNotEmpty ?? false)
              _buildCallToAction(),
            _buildBody(),
            _buildIndexIndicator(),
          ],
        ),
      ),
    );
  }

  Widget _buildIndexIndicator() {
    if (widget.isLoading) {
      return const SizedBox.shrink();
    }
    return Padding(
      padding: const EdgeInsets.only(left: 24.0),
      child: AzzasControllerCarrousel(
        itemsCount: widget.productsShowcase.length,
        spaceBetweenDots: 8,
        activeItemIndex: activeItemIndex,
        activeItemColor: Colors.black,
        secondaryColor: Colors.grey,
      ),
    );
  }

  Widget _buildCallToAction() {
    return Padding(
      padding: const EdgeInsets.only(left: 24.0),
      child: AzzasButton.link(
        onPressed: () {
          // _intelligentSearchController.clearSearch();
          widget.callToAction!.onTap();
        },
        child: Text(widget.callToAction!.text,
            style: TextStyle(color: widget.callToAction!.textColor)),
        trailing: widget.callToAction?.trailing ?? Icon(Icons.arrow_forward),
      ),
    );
  }

  Widget _buildButtons() {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 16.0),
      height: 36.0,
      child: ListView.separated(
        padding: const EdgeInsets.symmetric(horizontal: 24.0),
        itemCount: buttons.length,
        scrollDirection: Axis.horizontal,
        itemBuilder: (BuildContext context, int index) {
          final filter = buttons[index];
          final isSelected = index == filterButtonSelectedIndex;
          return AzzasButton.primary(
            onPressed: () {
              filter.onTap();
              setState(() => filterButtonSelectedIndex = index);
            },
            style: AzzasButtonStyle(
                backgroundColor:
                    isSelected ? Colors.black : filter.backgroundColor,
                foregroundColor: isSelected ? Colors.black : filter.textColor,
                borderColor: isSelected ? Colors.transparent : Colors.grey,
                padding: const EdgeInsets.symmetric(
                  horizontal: 16.0,
                ),
                borderRadius:
                    BorderRadius.all(Radius.circular(filter.radius ?? 0))),
            child: Text(
              filter.text,
              style: TextStyle(
                  color: isSelected ? Colors.white : filter.textColor),
            ),
          );
        },
        separatorBuilder: (_, __) => const SizedBox(width: 4.0),
      ),
    );
  }

  Widget _buildBody() {
    const descriptionHeight = 46.0;
    const tabHeight = 48.0;

    if (widget.buildDynamicSpotSize == true) {
      return Expanded(
        child: LayoutBuilder(builder: (_, constraints) {
          final dynamicHeight =
              constraints.maxHeight - descriptionHeight - tabHeight;
          dynamicImageWidth = dynamicHeight;

          return Container(
            margin: const EdgeInsets.only(
              top: 16.0,
              bottom: 8.0,
            ),
            child: widget.isLoading
                ? _buildLoadingSkeleton(
                    widget.spotHeight,
                    271.0,
                  )
                : _buildProducts(
                    widget.spotHeight,
                    dynamicImageWidth,
                    dynamicHeight: 271.0,
                  ),
          );
        }),
      );
    }

    return Container(
      height: (spotHeightsMap[widget.spotHeight]! + 50),
      margin: const EdgeInsets.only(
        top: 16.0,
        bottom: 8.0,
      ),
      child: widget.isLoading
          ? _buildLoadingSkeleton(
              widget.spotHeight,
              271.0,
            )
          : _buildProducts(
              widget.spotHeight,
              271.0,
            ),
    );
  }

  Widget _buildProducts(
    SpotHeight imageHeight,
    double imageWidth, {
    double? dynamicHeight,
  }) {
    return ListView.separated(
      padding: const EdgeInsets.symmetric(horizontal: 24.0),
      controller: productScrollController,
      itemCount: widget.productsShowcase.length,
      scrollDirection: Axis.horizontal,
      itemBuilder: (BuildContext context, int index) {
        Product product = widget.productsShowcase[index];
        product.updateProductHero(AzzasShowcase);

        return AzzasCmsSpotProduct(
          index: index,
          width: imageWidth,
          spotHeight: imageHeight,
          imageHeight: dynamicHeight,
          heroTag: product.productHeroID,
          productImageUrl: product.coverImage,
          productTitle: product.productName!,
          currentPriceTextStyle: widget.priceTextStyle,
          oldPriceTextStyle: widget.oldPriceTextStyle,
          onTap: () => widget.onTapProduct(product, index),
          isOnSale: product.isOnSale,
          currentPriceTextColor: widget.priceTextStyle?.color,
          oldPriceTextColor: widget.oldPriceTextStyle?.color,
          textLeftSpacing: widget.priceLeftSpacing ?? 0.0,
          textTopSpacing: widget.priceTopSpacing,
          productFullPrice: product.productFormattedListPrice,
          productCurrentPrice: product.productFormattedPrice,
          product: product,
          hasFavoriteButton: widget.hasFavoriteButton ?? false,
          hasPlusButton: widget.hasPlusButton ?? false,
        );
      },
      separatorBuilder: (_, __) => const SizedBox(width: 4.0),
    );
  }

  Widget _buildLoadingSkeleton(SpotHeight imageHeight, double imageWidth) {
    return ListView.separated(
      itemCount: 5,
      scrollDirection: Axis.horizontal,
      itemBuilder: (_, __) => AzzasSpotProductLoadingSkeleton(
        width: imageWidth,
        spotHeight: imageHeight,
      ),
      separatorBuilder: (_, __) => const SizedBox(width: 4.0),
    );
  }
}
