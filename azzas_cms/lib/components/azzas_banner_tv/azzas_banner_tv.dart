import 'dart:async';
import 'package:azzas_app_commons/azzas_app_commons.dart'
    show
        A<PERSON><PERSON>Button,
        AzzasButtonStyle,
        AzzasControllerCarrousel,
        AzzasVideo,
        BannerNameGA4,
        Event<PERSON><PERSON>patcher,
        Modular,
        VideoType;
import 'package:azzas_cms/components/azzas_carousel_list/azzas_carousel_list_cms_widget.dart';
import 'package:azzas_cms/models/azzas_dimensions.dart';
import 'package:azzas_cms/components/azzas_cached_network_image/azzas_cached_image.dart';
import 'package:flutter/material.dart';

enum MediaType { video, image }

const Map mediaTypes = {
  MediaType.image: 'image',
  MediaType.video: 'video',
};

abstract class MediaItem {}

class Media extends MediaItem {
  final MediaType mediaType;
  final String mediaUrl;
  final String altText;
  final Function() onTap;
  final String? searchId;
  final String? filterCategoryOrCluster;
  final String? creativeSlot;
  final Dimensions? dimensions;
  final List<CarouselListButtonsLink>? buttonsLink;
  final BannerNameGA4? bannerNameGA4;

  Media({
    Key? key,
    required this.mediaType,
    required this.mediaUrl,
    required this.altText,
    required this.onTap,
    this.searchId,
    this.filterCategoryOrCluster,
    this.dimensions,
    this.creativeSlot,
    this.buttonsLink,
    this.bannerNameGA4,
  });
}

class DoubleMedia extends MediaItem {
  final List<Media> items;

  DoubleMedia({
    required this.items,
  });
}

class AzzasBannerTv extends StatefulWidget {
  final List<MediaItem> mediaList;
  final double height;
  final double width;
  final bool? autoplay;
  final bool? loop;
  final Duration? autoplayDelay;
  final Function(String navigateToLink)? onTapButton;
  final Function(BannerNameGA4? bannerNameGA4, int index)? onVisible;

  const AzzasBannerTv({
    super.key,
    required this.mediaList,
    this.height = 700.0,
    this.width = double.infinity,
    this.autoplay = false,
    this.loop = false,
    this.autoplayDelay,
    this.onTapButton,
    this.onVisible,
  });

  @override
  State<AzzasBannerTv> createState() => _AzzasBannerTvState();
}

class _AzzasBannerTvState extends State<AzzasBannerTv> {
  static const defaultAutoplayDelay = Duration(seconds: 5);
  static const autoplayAnimationDuration = Duration(milliseconds: 300);
  static const autoplayAnimationCurve = Curves.easeIn;
  final pageController = PageController(initialPage: 0, viewportFraction: 1.0);
  Timer? autoplayTimer;
  int activeIndex = 0;

  double? get currentPage =>
      pageController.positions.isNotEmpty ? pageController.page : 0;

  @override
  void initState() {
    super.initState();
    if (widget.autoplay == true) {
      initializeAutoplayTimer();
    }
  }

  @override
  void didUpdateWidget(covariant AzzasBannerTv oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.autoplay != oldWidget.autoplay ||
        widget.loop != oldWidget.loop ||
        widget.autoplayDelay != oldWidget.autoplayDelay) {
      autoplayTimer?.cancel();
      if (widget.autoplay == true) {
        initializeAutoplayTimer();
      }
    }
  }

  @override
  void dispose() {
    autoplayTimer?.cancel();
    pageController.dispose();
    super.dispose();
  }

  void initializeAutoplayTimer() {
    final effectiveAutoplayDelay = widget.autoplayDelay ?? defaultAutoplayDelay;
    final loop = widget.loop ?? false;
    autoplayTimer =
        Timer.periodic(effectiveAutoplayDelay + autoplayAnimationDuration, (_) {
      final isAnimating = currentPage != currentPage?.round();
      if (!isAnimating) {
        goToNextPage(loop: loop);
      }
    });
  }

  void goToNextPage({required bool loop}) {
    final isLast = currentPage == widget.mediaList.length - 1;
    if (isLast) {
      if (loop) {
        pageController.animateToPage(
          0,
          duration: autoplayAnimationDuration * 5,
          curve: autoplayAnimationCurve,
        );
      } else {
        autoplayTimer?.cancel();
      }
    } else if (pageController.page != null) {
      pageController.nextPage(
        duration: autoplayAnimationDuration,
        curve: autoplayAnimationCurve,
      );
    }
  }

  void dispatchViewPromotionEvent({
    required Media media,
    required int index,
  }) {
    if (widget.onVisible != null) widget.onVisible!(media.bannerNameGA4, index);
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: widget.height,
      width: widget.width,
      child: Stack(
        alignment: Alignment.center,
        children: [
          PageView.builder(
              itemCount: widget.mediaList.length,
              controller: pageController,
              onPageChanged: (index) {
                final media = widget.mediaList[index];
                if (media is Media) {
                  dispatchViewPromotionEvent(media: media, index: index);
                } else if (media is DoubleMedia) {
                  for (final mediaItem in media.items) {
                    dispatchViewPromotionEvent(media: mediaItem, index: index);
                  }
                }

                setState(() {
                  activeIndex = index;
                });
              },
              itemBuilder: (_, currentIndex) {
                final media = widget.mediaList[currentIndex];
                if (media is Media) {
                  dispatchViewPromotionEvent(media: media, index: currentIndex);
                } else if (media is DoubleMedia) {
                  for (final mediaItem in media.items) {
                    dispatchViewPromotionEvent(
                        media: mediaItem, index: currentIndex);
                  }
                }
                if (media is Media) {
                  return GestureDetector(
                    onTap: media.onTap,
                    child: (media.mediaType == MediaType.image
                        ? Stack(
                            children: [
                              AzzasCachedNetworkingImage(
                                height: widget.height,
                                width: widget.width,
                                imageUrl: media.mediaUrl,
                              ),
                              if ((media.buttonsLink?.isNotEmpty ?? false) &&
                                  true)
                                Padding(
                                  padding: const EdgeInsets.only(bottom: 100),
                                  child: LayoutBuilder(
                                    builder: (context, constraints) => Align(
                                      alignment: Alignment.bottomCenter,
                                      child: Wrap(
                                        alignment: WrapAlignment.center,
                                        spacing: 4,
                                        runSpacing: 4,
                                        children: _buildButtons(
                                          constraints,
                                          media.buttonsLink!,
                                          media,
                                          currentIndex,
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                            ],
                          )
                        : AzzasVideo(
                            videoType: VideoType.network,
                            mediaUrl: media.mediaUrl,
                            showButtonPlay: false,
                            startVideo: true,
                            fullScreen: true,
                            playPauseEnabled: false,
                          )),
                  );
                } else if (media is DoubleMedia) {
                  return _buildDoubleMedia(media);
                }

                return const SizedBox.shrink();
              }),
          if (widget.mediaList.length > 1)
            Positioned(
              bottom: 15,
              child: AzzasControllerCarrousel(
                activeItemIndex: activeIndex,
                itemsCount: widget.mediaList.length,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildDoubleMedia(DoubleMedia media) {
    final totalHeight = media.items.fold<double>(0, (previousValue, element) {
      return previousValue + (element.dimensions?.height ?? 0);
    });

    return Column(
      children: [
        ...media.items.map((item) =>
            _buildDoubleMediaItem(media: item, totalHeight: totalHeight))
      ],
    );
  }

  Widget _buildDoubleMediaItem({
    required Media media,
    required double totalHeight,
  }) {
    final mediaHeight = (media.dimensions?.height ?? 0) / totalHeight * 100;

    return Expanded(
      flex: !mediaHeight.isNaN && !mediaHeight.isInfinite
          ? mediaHeight.toInt()
          : 1,
      child: GestureDetector(
        onTap: media.onTap,
        child: AzzasCachedNetworkingImage(
          imageUrl: media.mediaUrl,
          width: widget.width,
          height: widget.height,
        ),
      ),
    );
  }

  List<Widget> _buildButtons(
    BoxConstraints constraints,
    List<CarouselListButtonsLink> items,
    Media media,
    int index,
  ) {
    List<Widget> widgets = [];
    // Agrupa botões para exibir 2 por linha
    for (int i = 0; i < items.length; i += 2) {
      if (i == items.length - 1) {
        // Centraliza último item em baixo dos 2 acima
        widgets.add(Padding(
          padding: const EdgeInsets.only(
            left: 4,
            right: 4,
            bottom: 4,
          ),
          child: _item(items[i], media, index),
        ));
      } else {
        widgets.add(
          Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 4),
                child: _item(items[i], media, index),
              ),
              const SizedBox(width: 4),
              if (i + 1 < items.length)
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 4),
                  child: _item(items[i + 1], media, index),
                ),
            ],
          ),
        );
      }
    }
    return widgets;
  }

  Widget _item(
    CarouselListButtonsLink btn,
    Media media,
    int index,
  ) {
    return AzzasButton.primary(
      child: Text(
        btn.title,
      ),
      onPressed: () {
        widget.onTapButton?.call(btn.navigateTo);
      },
      style: AzzasButtonStyle(
        backgroundColor: Colors.transparent,
        borderColor: btn.hasBorder ? Colors.white : null,
        padding: const EdgeInsets.all(8),
      ),
      trailing: Container(
        padding: const EdgeInsets.only(top: 4),
        child: const Icon(
          Icons.keyboard_arrow_right,
          weight: 500,
        ),
      ),
      expanded: false,
    );
  }
}
