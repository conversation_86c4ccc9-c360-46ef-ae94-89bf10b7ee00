import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:flutter/material.dart';

class CarouselListCmsWidget extends StatelessWidget {
  const CarouselListCmsWidget({
    super.key,
    required this.componentType,
    required this.items,
    this.autoplay,
    this.loop,
    this.autoplayDelay,
    this.onVisible,
    this.onTap,
  });

  factory CarouselListCmsWidget.fromComponent(
    CarouselListCmsComponent component, {
    Function(BannerNameGA4? bannerNameGA4, int index)? onVisible,
    Function(BannerNameGA4? bannerNameGA4, int index)? onTap,
  }
  ) {
    return CarouselListCmsWidget(
      componentType: component.componentType,
      items: component.items.map(CarouselListItem.fromComponentItem).toList(),
      autoplay: component.autoplay,
      loop: component.loop,
      autoplayDelay:
          component.delay != null ? Duration(seconds: component.delay!) : null,
      onTap: onTap,
      onVisible: onVisible,
    );
  }

  final String componentType;
  final List<CarouselListItem> items;
  final bool? autoplay;
  final bool? loop;
  final Duration? autoplayDelay;
  final Function(BannerNameGA4? bannerNameGA4, int index)? onVisible;
  final Function(BannerNameGA4? bannerNameGA4, int index)? onTap;


  @override
  Widget build(BuildContext context) {
    const shouldFilterByCluster = true;

    final mediaList = items
        .mapIndexed((index, item) {
          if (shouldFilterByCluster) {
            final result = _checkItemByCluster(context, item);
            if (!result) {
              return null;
            }
          }

          if (item.secondaryMedia == null) {
            return Media(
                bannerNameGA4: item.mainMedia.bannerNameGA4,
                altText: item.mainMedia.bannerNameGA4.value,
                mediaType: item.mainMedia.mediaType,
                mediaUrl: item.mainMedia.mediaUrl,
                creativeSlot: item.mainMedia.mediaName,
                onTap: () => _onTapMedia(context, index, item.mainMedia),
                buttonsLink: item.buttonsLink);
          }
          return DoubleMedia(items: [
            Media(
              bannerNameGA4: item.mainMedia.bannerNameGA4,
              altText: item.mainMedia.bannerNameGA4.value,
              mediaType: item.mainMedia.mediaType,
              mediaUrl: item.mainMedia.mediaUrl,
              creativeSlot: item.mainMedia.mediaName,
              onTap: () => _onTapMedia(context, index, item.mainMedia),
              buttonsLink: item.buttonsLink,
              dimensions: item.mainMedia.dimension,
            ),
            Media(
              bannerNameGA4: item.secondaryMedia?.bannerNameGA4,
              altText: item.secondaryMedia!.bannerNameGA4.value,
              mediaType: item.secondaryMedia!.mediaType,
              mediaUrl: item.secondaryMedia!.mediaUrl,
              creativeSlot: item.secondaryMedia!.mediaName,
              onTap: () => _onTapMedia(context, index, item.secondaryMedia!),
              buttonsLink: item.buttonsLink,
              dimensions: item.secondaryMedia!.dimension,
            )
          ]);
        })
        .whereType<MediaItem>()
        .toList();

    final screenSize = MediaQuery.sizeOf(context);

    const tabBarHeight = 90;
    if (mediaList.isEmpty) {
      return const SizedBox();
    }

    return AzzasBannerTv(
      height: screenSize.height - tabBarHeight,
      width: screenSize.width,
      mediaList: mediaList,
      autoplay: autoplay,
      loop: loop,
      autoplayDelay: autoplayDelay,
      onVisible: onVisible,
      onTapButton: (String navigateToLink) {
        if (navigateToLink.isNotEmpty) {
          NavigatorDynamic.call(navigateToLink);
        }
      },
    );
  }

   bool _checkItemByCluster(BuildContext context, CarouselListItem item) {
   
    final authCubit = Modular.get<AuthCubit>();
    
    final showToUnloggedUser =
        !authCubit.state.isLoggedIn && item.showToUnloggedUsers;

    if (item.clusters == null || (item.clusters?.isEmpty ?? true) ||
        showToUnloggedUser) {
      return true;
    }

    final userClusters = authCubit.state.userClusterData;

    if (!authCubit.state.isLoggedIn || userClusters == null) {
      return false;
    }

    final result = DynamicFilterByUserCluster.compareClusters(
        item.clusters ?? '', userClusters);

    return result;
  }

  void _onTapMedia(
    BuildContext context,
    int index,
    ListItemMedia item,
  ) async {
    if (onTap != null) {
      onTap!(item.bannerNameGA4, index);
    };
    final navigateTo = item.navigateTo?.trim();
    if (navigateTo != null && navigateTo.isNotEmpty) {
      NavigatorDynamic.call(item.navigateTo!);
    } else if (item.coupon?.isNotEmpty ?? false) {
      await _addCoupon(context, item);
    }
    if (context.mounted) {}
  }

  Future<void> _addCoupon(
    BuildContext context,
    ListItemMedia item,
  ) async {
    if (item.coupon == null || item.coupon == '') return;
    OrderCheckoutHandler orderCheckoutHandler = Modular.get<OrderCheckoutHandler>();
    orderCheckoutHandler.addCoupon(coupon: item.coupon!);
  }
}

class CarouselListItem {
  const CarouselListItem({
    required this.mainMedia,
    this.coupon,
    this.navigateTo,
    this.buttonsLink,
    this.clusters,
    this.secondaryMedia,
    this.showToUnloggedUsers = true,
  });

  factory CarouselListItem.fromComponentItem(
    CarouselListCmsComponentItem item,
  ) {
    return CarouselListItem(
      mainMedia: ListItemMedia.fromComponentItem(item),
      coupon: item.coupon,
      navigateTo: item.navigateTo,
      buttonsLink: item.buttonsLink
          .map(CarouselListButtonsLink.fromComponentItem)
          .toList(),
      clusters: item.clusters,
      secondaryMedia: item.secondaryMedia != null
          ? ListItemMedia.fromSecondaryMediaItem(item.secondaryMedia!)
          : null,
      showToUnloggedUsers: item.showToUnloggedUsers ?? true,
    );
  }

  final ListItemMedia mainMedia;
  final String? coupon;
  final String? navigateTo;
  final List<CarouselListButtonsLink>? buttonsLink;
  final String? clusters;
  final ListItemMedia? secondaryMedia;
  final bool showToUnloggedUsers;
}

class ListItemMedia {
  const ListItemMedia({
    required this.mediaType,
    required this.mediaUrl,
    required this.mediaName,
    required this.bannerNameGA4,
    required this.dimension,
    required this.navigateTo,
    required this.coupon,
  });

  final MediaType mediaType;
  final String mediaUrl;
  final String mediaName;
  final BannerNameGA4 bannerNameGA4;
  final Dimensions dimension;
  final String? navigateTo;
  final String? coupon;

  factory ListItemMedia.fromComponentItem(
    CarouselListCmsComponentItem item,
  ) {
    return ListItemMedia(
      mediaType: item.media.data.attributes.type.asMediaType(),
      mediaUrl: item.media.data.attributes.url,
      mediaName: item.media.data.attributes.name,
      bannerNameGA4: item.bannerNameGA4,
      dimension: Dimensions(
        height: item.media.data.attributes.height,
        width: item.media.data.attributes.width,
      ),
      navigateTo: item.navigateTo,
      coupon: item.coupon,
    );
  }

  factory ListItemMedia.fromSecondaryMediaItem(
    CarouselListCmsComponentSecondaryMediaItem secondaryMedia,
  ) {
    return ListItemMedia(
      mediaType: secondaryMedia.media.data.attributes.type.asMediaType(),
      mediaUrl: secondaryMedia.media.data.attributes.url,
      mediaName: secondaryMedia.media.data.attributes.name,
      bannerNameGA4: secondaryMedia.bannerNameGA4,
      dimension: Dimensions(
        height: secondaryMedia.media.data.attributes.height,
        width: secondaryMedia.media.data.attributes.width,
      ),
      navigateTo: secondaryMedia.navigateTo,
      coupon: secondaryMedia.coupon,
    );
  }
}

class CarouselListButtonsLink {
  const CarouselListButtonsLink({
    required this.title,
    required this.navigateTo,
    required this.hasBorder,
  });

  factory CarouselListButtonsLink.fromComponentItem(
    CarouselListCmsComponentButtonsLink item,
  ) {
    return CarouselListButtonsLink(
      title: item.title,
      navigateTo: item.navigateTo,
      hasBorder: item.hasBorder,
    );
  }

  final String title;
  final String navigateTo;
  final bool hasBorder;
}

extension on CmsMediaType {
  MediaType asMediaType() {
    return switch (this) {
      CmsMediaType.image => MediaType.image,
      CmsMediaType.video => MediaType.video,
    };
  }
}
