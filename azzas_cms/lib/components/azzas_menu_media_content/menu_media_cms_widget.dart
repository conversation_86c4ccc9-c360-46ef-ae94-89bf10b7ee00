import 'package:azzas_app_commons/azzas_app_commons.dart';

import 'package:flutter/material.dart';
import 'package:visibility_detector/visibility_detector.dart';

class MenuMediaCmsWidget extends StatelessWidget {
  const MenuMediaCmsWidget({
    this.media,
    this.videoUrl,
    this.navigateTo,
    this.imageUrl,
    this.index,
    this.bannerNameGA4,
    this.onVisible,
    this.onTap,
  });

  final CmsMedia? media;
  final String? imageUrl;
  final String? videoUrl;
  final String? navigateTo;
  final int? index;
  final BannerNameGA4? bannerNameGA4;
  final VoidCallback? onVisible;
  final VoidCallback? onTap;


  factory MenuMediaCmsWidget.fromComponent(
    MenuMediaCmsComponent component,
    int index,
    VoidCallback onTap,
    VoidCallback onVisible,
  ) {
    return MenuMediaCmsWidget(
      media: component.media,
      videoUrl: component.videoUrl,
      navigateTo: component.navigateTo,
      bannerNameGA4: component.bannerNameGA4,
      index: index,
      onTap: onTap,
      onVisible: onVisible,
    );
  }

  factory MenuMediaCmsWidget.fromAccordionItem(
      AccordionMenuEtcCmsComponentItem item) {
    return MenuMediaCmsWidget(
      imageUrl: item.imageUrl,
      videoUrl: item.videoUrl,
      navigateTo: item.navigateTo,
    );
  }

  @override
  Widget build(BuildContext context) {
    return VisibilityDetector(
      key: Key('menu_media_${bannerNameGA4}'),
      onVisibilityChanged: (info) {
        if (info.visibleFraction >= 0.8 && onVisible != null) {
          onVisible!();
        }
      },
      child: InkWell(
        onTap: navigateTo.isNotNullOrEmpty
            ? () {
                if (onTap != null) onTap!();
                NavigatorDynamic.call(navigateTo!);
              }
            : () {},
        child: Container(
          padding: EdgeInsets.only(top: 16, bottom: 16),
          constraints: BoxConstraints(maxHeight: 298),
          child: videoUrl.isNotNullOrEmpty
              ? AzzasVideo(
                  videoType: VideoType.network,
                  mediaUrl: videoUrl!,
                  looping: true,
                  startVideo: true,
                  showButtonPlay: false,
                  volume: 0,
                  fullScreen: true,
                  mixWithOthers: true,
                )
              : AzzasCachedNetworkingImage(
                  imageUrl: imageUrl ?? media?.data.attributes.url ?? '',
                ),
        ),
      ),
    );
  }
}
