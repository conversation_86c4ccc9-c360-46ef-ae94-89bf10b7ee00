import 'dart:async';

import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:flutter/material.dart';
import 'package:visibility_detector/visibility_detector.dart';

class FullLookCarouselCmsWidget extends StatefulWidget {
  final List<FullLookCarouselCmsComponentItem> items;
  final String title;
  final String? description;
  final String? callToAction;
  final Widget Function(List<String>)? bottomSheetBuilder;
  final void Function(int, BannerNameGA4)? onVisible;
  final void Function(int, BannerNameGA4)? onTap;

  const FullLookCarouselCmsWidget({
    super.key,
    required this.items,
    required this.title,
    this.description,
    this.callToAction,
    this.bottomSheetBuilder,
    this.onVisible,
    this.onTap,
  });

  factory FullLookCarouselCmsWidget.fromComponent(
    FullLookCarouselCmsComponent component, {
    Widget Function(List<String>)? bottomSheetBuilder,
    void Function(int, BannerNameGA4?)? onVisible,
    void Function(int, BannerNameGA4?)? onTap,
  }) {
    return FullLookCarouselCmsWidget(
        items: component.items,
        title: component.title,
        description: component.description,
        callToAction: component.callToAction,
        bottomSheetBuilder: bottomSheetBuilder,
        onVisible: onVisible,
        onTap: onTap);
  }

  @override
  State<FullLookCarouselCmsWidget> createState() =>
      _FullLookCarouselCmsWidgetState();
}

class _FullLookCarouselCmsWidgetState extends State<FullLookCarouselCmsWidget>
    with TickerProviderStateMixin {
  int activeItemIndex = 0;
  List<Product> productsFullLook = [];
  ScrollController productScrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    productScrollController.addListener(_onScroll);
  }

  void _onScroll() {
    final totalScroll = productScrollController.position.pixels;
    setState(() {
      activeItemIndex = totalScroll ~/ 220;
    });
  }

  @override
  void dispose() {
    productScrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = AzzasCmsThemeProvider.of(context).fullLookCarouselTheme;

    final height = spotHeights[SpotHeight.large];
    final width = spotWidths[SpotHeight.large];
    return widget.items.isEmpty
        ? const SizedBox.shrink()
        : AzzasCmsContentSpacer(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 24),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.title,
                        style: theme?.titleStyle,
                      ),
                      SizedBox(height: 16),
                      if (widget.description != null) ...[
                        Text(
                          widget.description!,
                          style: theme?.descriptionStyle,
                        ),
                        SizedBox(height: 8),
                      ],
                      if (widget.callToAction != null) ...[
                        InkWell(
                          onTap: () => NavigatorDynamic.call('full-look'),
                          child: Row(
                            children: [
                              Text(
                                widget.callToAction!,
                                style: theme?.callToActionStyle,
                              ),
                              SizedBox(width: 4),
                              Icon(theme?.callToActionIcon ?? Icons.arrow_right)
                            ],
                          ),
                        )
                      ]
                    ],
                  ),
                ),
                SizedBox(height: 52),
                SizedBox(
                  height: 500.0,
                  width: double.infinity,
                  child: ListView.separated(
                    cacheExtent: 100.0,
                    padding: EdgeInsets.symmetric(horizontal: 24),
                    scrollDirection: Axis.horizontal,
                    controller: productScrollController,
                    shrinkWrap: true,
                    itemCount: widget.items.length,
                    separatorBuilder: (_, __) => SizedBox(width: 4),
                    itemBuilder: (context, index) {
                      final fullLookItem = widget.items[index];

                      return VisibilityDetector(
                        key: Key('full_look${fullLookItem.bannerNameGA4}'),
                        onVisibilityChanged: (info) {
                          if (info.visibleFraction >= 0.8 &&
                              widget.onVisible != null) {
                            widget.onVisible!(
                                index, fullLookItem.bannerNameGA4);
                          }
                        },
                        child: GestureDetector(
                          onTap: () {
                            if (widget.onTap != null) {
                              widget.onTap!(index, fullLookItem.bannerNameGA4);
                            }
                            openFullLookProducts(fullLookItem, index);
                          },
                          child: AzzasCachedNetworkingImage(
                            imageUrl: fullLookItem.image.data.attributes.url,
                            height: height,
                            width: width,
                          ),
                        ),
                      );
                    },
                  ),
                ),
                SizedBox(height: 48),
                Padding(
                  padding: const EdgeInsets.only(left: 24.0),
                  child: AzzasControllerCarrousel(
                    itemsCount: widget.items.length,
                    activeItemIndex: activeItemIndex,
                  ),
                ),
                SizedBox(height: 64),
              ],
            ),
          );
  }

  void getProductList(List<String> productList) async {
    List<Product> listProducts = [];
    listProducts = await FullLookHandler.getProductsByIds(productList);
    if (mounted) {
      setState(() {
        productsFullLook = listProducts;
      });
    }
  }

  Future<void> openFullLookProducts(
    FullLookCarouselCmsComponentItem item,
    int position,
  ) async {
    if (widget.bottomSheetBuilder == null) return;
    final listProduct = item.productIdList;
    // final listProduct = await getProductList(item.productIdList);
    // final promotionId = 'complete-o-look-home:$position';
    // final bannerName = item.bannerNameGA4;

    // dispatchSelectPromotionEvent(
    //   screenClass: 'complete-o-look-home',
    //   promotionId: promotionId,
    //   promotionName: bannerName.promotionName,
    //   creativeName: bannerName.creativeName,
    // );

    showAzzasBottomSheet(
      context: context,
      builder: (context) {
        return widget.bottomSheetBuilder!(listProduct);
      },
      vsync: this,
    );
  }
}
