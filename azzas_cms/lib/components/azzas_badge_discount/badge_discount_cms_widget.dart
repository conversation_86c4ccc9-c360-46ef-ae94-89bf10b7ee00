import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class BadgeDiscountCmsWidget extends StatefulWidget {
  final String? coupon;
  final Color? backgroundColor;
  final CmsMedia media;
  
  final BannerNameGA4? bannerNameGA4;


  const BadgeDiscountCmsWidget({
    super.key,
    this.coupon,
    this.backgroundColor,
    required this.media,
    required this.bannerNameGA4,
  });

  factory BadgeDiscountCmsWidget.fromComponent(
      BadgeDiscountCmsComponent component) {
    return BadgeDiscountCmsWidget(
      media: component.media,
      backgroundColor: component.backgroundColor,
      coupon: component.coupon,
      bannerNameGA4: component.bannerNameGA4,
    );
  }

  @override
  State<BadgeDiscountCmsWidget> createState() => _BadgeDiscountCmsWidgetState();
}

class _BadgeDiscountCmsWidgetState extends State<BadgeDiscountCmsWidget> {
  @override
  Widget build(BuildContext context) {
    return Container(
      color: widget.backgroundColor,
      child: Center(
        child: GestureDetector(
          onTap: widget.coupon == null
              ? null
              : () {
                  Clipboard.setData(
                    ClipboardData(text: widget.coupon!),
                  );
    
                  AzzasSnackBar.show(
                      context: context,
                      message: 'Cupom copiado com sucesso!',
                      status: SnackBarStatus.success);
                },
          child: AzzasImage(
            fit: BoxFit.fitHeight,
            imageHeight: 500,
            imageWidth: double.infinity,
            image: NetworkImage(widget.media.data.attributes.url),
          ),
        ),
      ),
    );
  }
}
