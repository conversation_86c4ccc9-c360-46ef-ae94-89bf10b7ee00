import 'package:azzas_cms/azzas_cms.dart';
import 'package:azzas_cms/models/cms_component.dart';
import 'package:azzas_cms/models/cms_media.dart';
import 'package:azzas_cms/utils/color_utils.dart';
import 'package:flutter/material.dart';

class BadgeDiscountCmsComponent extends CmsComponent {
  BadgeDiscountCmsComponent({
    required super.id,
    required super.componentType,
    required this.media,
    this.coupon,
    this.backgroundColor,
    required this.bannerNameGA4,
  });

  factory BadgeDiscountCmsComponent.fromJson(Map<String, dynamic> json) {
    return BadgeDiscountCmsComponent(
      id: json['id'],
      componentType: json['__component'],
      coupon: json['coupon'],
      media: CmsMedia.fromJson(json['media']),
      backgroundColor:
          ColorUtils.parseStrapiColor(json['background']) ?? Colors.transparent,
      bannerNameGA4: json['banner_name_ga4'] is String
          ? BannerNameGA4.fromString(json['banner_name_ga4'])
          : null,
    );
  }

  final String? coupon;
  final Color? backgroundColor;
  final CmsMedia media;
  final BannerNameGA4? bannerNameGA4;
}
