import 'package:azzas_app_commons/azzas_app_commons.dart';

import 'package:flutter/material.dart';

class BannerContentTextCmsWidget extends StatefulWidget {
  final CmsMedia media;
  final String title;
  final String description;
  final BannerContentTextVariation variation;
  final Color? textColor;
  final Color overlayColor;
  final String ctaText;
  final String navigateTo;
  final BannerNameGA4? bannerNameGA4;

  const BannerContentTextCmsWidget({
    super.key,
    required this.media,
    required this.title,
    required this.description,
    required this.variation,
    required this.textColor,
    required this.overlayColor,
    required this.ctaText,
    required this.navigateTo,
    required this.bannerNameGA4,
  });

  factory BannerContentTextCmsWidget.fromComponent(
      BannerContentTextCmsComponent component) {
    return BannerContentTextCmsWidget(
      media: component.media,
      title: component.title,
      description: component.description,
      variation: component.variation,
      textColor: component.textColor,
      overlayColor: component.overlayColor,
      ctaText: component.ctaText,
      navigateTo: component.navigateTo,
        bannerNameGA4: component.bannerNameGA4
    );
  }

  @override
  State<BannerContentTextCmsWidget> createState() =>
      _BannerContentTextCmsWidgetState();
}

class _BannerContentTextCmsWidgetState
    extends State<BannerContentTextCmsWidget> {
  Widget _buildWidget(BuildContext context) {
    const tabBarHeight = 90.0;
    final screenHeight = MediaQuery.of(context).size.height - tabBarHeight;

    return switch (widget.variation) {
      BannerContentTextVariation.overlay => BannerContentOverlayWidget(
          height: screenHeight,
          media: widget.media,
          overlayColor: widget.overlayColor,
          ctaText: widget.ctaText,
          title: widget.title,
          description: widget.description,
          textColor: widget.textColor,
          navigateTo: widget.navigateTo,
        ),
      final v
          when v == BannerContentTextVariation.mediumPadding ||
              v == BannerContentTextVariation.largePadding =>
        BannerContentPaddingWidget(
          height: screenHeight,
          variation: widget.variation,
          media: widget.media,
          title: widget.title,
          description: widget.description,
          ctaText: widget.ctaText,
          navigateTo: widget.navigateTo,
          textColor: widget.textColor,
        ),
      _ => BannerContentOverlayWidget(
          height: screenHeight,
          media: widget.media,
          overlayColor: widget.overlayColor,
          ctaText: widget.ctaText,
          title: widget.title,
          description: widget.description,
          textColor: widget.textColor,
          navigateTo: widget.navigateTo,
        )
    };
  }

  @override
  Widget build(BuildContext context) {
    return AzzasCmsContentSpacer(
      child: _buildWidget(context),
    );
  }
}
