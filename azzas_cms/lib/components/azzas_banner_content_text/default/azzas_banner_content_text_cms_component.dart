import 'package:azzas_cms/azzas_cms.dart';
import 'package:azzas_cms/models/cms_component.dart';
import 'package:azzas_cms/models/cms_media.dart';
import 'package:azzas_cms/utils/color_utils.dart';
import 'package:flutter/material.dart';

class BannerContentTextCmsComponent extends CmsComponent {
  BannerContentTextCmsComponent({
    required super.id,
    required super.componentType,
    required this.media,
    required this.title,
    required this.description,
    required this.textColor,
    required this.variation,
    required this.overlayColor,
    required this.ctaText,
    required this.navigateTo,
    required this.bannerNameGA4,
  });

  factory BannerContentTextCmsComponent.fromJson(Map<String, dynamic> json) {
    return BannerContentTextCmsComponent(
      id: json['id'],
      componentType: json['__component'],
      media: CmsMedia.fromJson(json['media']),
      title: json['title'],
      description: json['description'],
      textColor: ColorUtils.parseStrapiColor(json['text_color']),
      variation: BannerContentTextVariation.fromCmsValue(json['variation']),
      overlayColor:
          ColorUtils.parseStrapiColor(json['overlay_color']) ?? Colors.white,
      ctaText: json['cta_text'],
      navigateTo: json['navigate_to'] ?? '',
      bannerNameGA4: json['banner_name_ga4'] is String
          ? BannerNameGA4.fromString(json['banner_name_ga4'])
          : null,
    );
  }

  final CmsMedia media;
  final String title;
  final String description;
  final BannerContentTextVariation variation;
  final Color? textColor;
  final Color overlayColor;
  final String ctaText;
  final String navigateTo;
  final BannerNameGA4? bannerNameGA4;
}

enum BannerContentTextVariation {
  overlay('overlay'),
  mediumPadding('medium padding'),
  largePadding('large padding');

  final String cmsValue;

  const BannerContentTextVariation(this.cmsValue);

  factory BannerContentTextVariation.fromCmsValue(String value) {
    return BannerContentTextVariation.values.firstWhere(
      (e) => e.cmsValue == value,
      orElse: () => BannerContentTextVariation.overlay,
    );
  }
}
