import 'package:azzas_cms/azzas_cms.dart';

/// Variação com dois itens de mídia do [AzzasBannerContentTextCmsComponent].
class BannerContentDoubleMediaCmsComponent extends CmsComponent {
  final String title;
  final String description;
  final String ctaText;
  final String navigateTo;
  final BannerContentDoubleMediaItemCmsComponent firstMediaItem;
  final BannerContentDoubleMediaItemCmsComponent secondMediaItem;

  const BannerContentDoubleMediaCmsComponent({
    required this.title,
    required this.description,
    required this.ctaText,
    required this.navigateTo,
    required this.firstMediaItem,
    required this.secondMediaItem,
    required super.id,
    required super.componentType,
  });

  factory BannerContentDoubleMediaCmsComponent.fromJson(
    Map<String, dynamic> json,
  ) {
    return BannerContentDoubleMediaCmsComponent(
      id: json['id'],
      componentType: json['__component'],
      title: json['title'],
      description: json['description'],
      ctaText: json['cta_text'],
      navigateTo: json['navigate_to'],
      firstMediaItem: BannerContentDoubleMediaItemCmsComponent.fromJson(
        json['first_media'],
      ),
      secondMediaItem: BannerContentDoubleMediaItemCmsComponent.fromJson(
        json['second_media'],
      ),
    );
  }
}

class BannerContentDoubleMediaItemCmsComponent {
  final CmsMedia media;
  final String? ctaText;
  final String? navigateTo;
  final BannerNameGA4? bannerNameGA4;

  const BannerContentDoubleMediaItemCmsComponent({
    required this.media,
    this.ctaText,
    this.navigateTo,
    required this.bannerNameGA4,
  });

  factory BannerContentDoubleMediaItemCmsComponent.fromJson(
    Map<String, dynamic> json,
  ) {
    return BannerContentDoubleMediaItemCmsComponent(
      media: CmsMedia.fromJson(json['media']),
      ctaText: json['cta_text'],
      navigateTo: json['navigate_to'],
      bannerNameGA4: json['banner_name_ga4'] is String
          ? BannerNameGA4.fromString(json['banner_name_ga4'])
          : null,
    );
  }

  bool get hasCta =>
      ctaText != null &&
      ctaText!.isNotEmpty &&
      navigateTo != null &&
      navigateTo!.isNotEmpty;
}
