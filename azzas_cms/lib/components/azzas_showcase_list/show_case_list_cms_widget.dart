import 'package:azzas_app_commons/azzas_app_commons.dart'
    show
        AzzasCmsThemeProvider,
        IntelligentSearchUseCase,
        Modular,
        NavigatorDynamic,
        Product,
        Search,
        SearchProductsUseCase;
import 'package:azzas_app_commons/modules/app_commons/app_commons.dart';
import 'package:azzas_cms/components/azzas_category_banner/azzas_category_banner_exp.dart';
import 'package:azzas_cms/components/azzas_showcase_list/show_case_list_cms_component.dart';
import 'package:azzas_cms/utils/azzas_cms_content_spacer.dart';
import 'package:azzas_cms/widgets/azzas_showcase.dart';
import 'package:flutter/material.dart';

class ShowCaseListCmsWidget extends StatelessWidget {
  const ShowCaseListCmsWidget({
    required this.title,
    required this.filterCategoryOrCluster,
    this.orderByIntelligentSearch,
    this.callToAction,
    this.subtitle,
    this.items,
    super.key,
    this.onVisible,
    this.onTap,
  });

  final String title;
  final String filterCategoryOrCluster;
  final String? orderByIntelligentSearch;
  final String? callToAction;
  final String? subtitle;
  final List? items;
  final Function(BannerNameGA4? bannerNameGA4, int index)? onVisible;
  final Function(BannerNameGA4? bannerNameGA4, int index)? onTap;

  factory ShowCaseListCmsWidget.fromComponent(
    ShowCaseListCmsComponentt component, {
    Function(BannerNameGA4? bannerNameGA4, int index)? onVisible,
    Function(BannerNameGA4? bannerNameGA4, int index)? onTap,
  }) {
    return ShowCaseListCmsWidget(
      title: component.title,
      subtitle: component.subtitle,
      filterCategoryOrCluster: component.filterCategoryOrCluster,
      orderByIntelligentSearch: component.orderByIntelligentSearch,
      callToAction: component.callToAction,
      items: component.items,
      onTap: onTap,
      onVisible: onVisible,
    );
  }

  @override
  Widget build(BuildContext context) {
    return AzzasCmsContentSpacer(
      child: ShowCaseListWidget(
        title: title,
        subtitle: subtitle,
        filterCategoryOrCluster: filterCategoryOrCluster,
        orderByIntelligentSearch: orderByIntelligentSearch,
        callToAction: callToAction,
        items: items,
      ),
    );
  }
}

class ShowCaseListWidget extends StatefulWidget {
  const ShowCaseListWidget({
    required this.title,
    this.items,
    required this.filterCategoryOrCluster,
    this.orderByIntelligentSearch,
    this.callToAction,
    this.subtitle,
    this.priceTextStyle,
    this.oldPriceTextStyle,
    super.key,
  });

  final List? items;
  final String filterCategoryOrCluster;
  final String? orderByIntelligentSearch;
  final String? callToAction;
  final String title;
  final String? subtitle;
  final TextStyle? priceTextStyle;
  final TextStyle? oldPriceTextStyle;

  @override
  State<ShowCaseListWidget> createState() => _ShowCaseListWidgetState();
}

class _ShowCaseListWidgetState extends State<ShowCaseListWidget> {
  List<Product> productsShowcase = [];
  bool isLoadingShowcase = false;
  Search? filter;
  final _eventDispatcher = Modular.get<EventDispatcher>();

  @override
  void initState() {
    super.initState();
    if (widget.items == null || widget.items!.isEmpty) {
      _searchProducts();
    }
  }

  Search get search {
    return Search(
      title: widget.title,
      filterCategoryOrCluster: widget.filterCategoryOrCluster,
    );
  }

  Future<void> _searchProducts() async {
    setState(() {
      isLoadingShowcase = true;
    });
    try {
      List<Product> productsResultSet = [];
      if (search.getFilter.isEmpty) {
        final searchProducts = Modular.get<SearchProductsUseCase>();
        productsResultSet = await searchProducts.call(search: search);
      } else {
        final _intelligentSearchUseCase =
            Modular.get<IntelligentSearchUseCase>();
        productsResultSet =
            (await _intelligentSearchUseCase(search: search)).products;
      }
      if (mounted) {
        setState(() {
          productsShowcase = productsResultSet;
          isLoadingShowcase = false;
        });
      }
    } catch (e) {
      debugPrint('Erro ao carregar produtos: $e');
      debugPrintStack();
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = AzzasCmsThemeProvider.of(context).showcaseListTheme;
    AzzasShowcaseButton showcaseButton = AzzasShowcaseButton(
        text: widget.callToAction ?? '',
        onTap: () async {
          if (filter != null) {
            NavigatorDynamic.call('plp${filter!.filterCategoryOrCluster}');
          }
        },
        trailing: theme.trailing,
        backgroundColor: Colors.white,
        textColor: Colors.black);
    List<AzzasShowcaseButton>? showcaseList = widget.items
        ?.map(
          (item) => AzzasShowcaseButton(
            text: item.title ?? '',
            onTap: () {
              setState(() {
                filter = Search(
                  title: item.title,
                  filterCategoryOrCluster: item.filterCategoryOrCluster,
                  orderBy: item.orderByIntelligentSearch != null
                      ? Search.getOrderByString(item.orderByIntelligentSearch!)
                      : null,
                );
              });
              _searchProducts();
            },
            backgroundColor: Colors.white,
            textColor: Colors.black,
            radius: theme.radius,
          ),
        )
        .toList();

    return Container(
      padding: const EdgeInsets.only(top: 20),
      child: AzzasShowcase(
        hasFavoriteButton: true,
        productsShowcase: productsShowcase,
        isLoading: isLoadingShowcase,
        callToAction: showcaseButton,
        showcaseButtonList: showcaseList,
        titleText: widget.title,
        titleStyle: theme.titleStyle,
        subtitleText: widget.subtitle,
        subtitleStyle: theme.subtitleStyle,
        priceTextStyle: widget.priceTextStyle ?? theme.priceTextStyle,
        oldPriceTextStyle: widget.oldPriceTextStyle,
        spotHeight: theme.spotHeight,
        onTapProduct: (Product product, int index) {
          _eventDispatcher.logSelectItem(
            product: product,
            itemListName: widget.title,
            index: index,
          );
          Modular.to.pushNamed('/pdp', arguments: product);
        },
      ),
    );
  }
}
