import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:flutter/material.dart';
import 'package:visibility_detector/visibility_detector.dart';

class StoriesCmsWidget extends StatefulWidget {
  const StoriesCmsWidget(this.component,
      {this.onVisible, this.onTap, super.key});

  final StoriesCmsComponent component;
  final Function(BannerNameGA4? bannerNameGA4, int index)? onVisible;
  final Function(BannerNameGA4? bannerNameGA4, int index)? onTap;

  factory StoriesCmsWidget.fromComponent(
    StoriesCmsComponent component, {
    Function(BannerNameGA4? bannerNameGA4, int index)? onVisible,
    Function(BannerNameGA4? bannerNameGA4, int index)? onTap,
  }) {
    return StoriesCmsWidget(
      component,
      onTap: onTap,
      onVisible: onVisible,
    );
  }
  @override
  State<StoriesCmsWidget> createState() => _StoriesCmsWidgetState();
}

class _StoriesCmsWidgetState extends State<StoriesCmsWidget> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return AzzasCmsContentSpacer(
      child: _SMStories(
        title: widget.component.title,
        items: widget.component.items,
        position: AzzasCmsContentMetadata.of(context)?.index ?? 0,
        onTap: widget.onTap,
        onVisible: widget.onVisible,
      ),
    );
  }
}

class _SMStories extends StatelessWidget {
  final List<StoryItemCmsComponent> items;
  final int position;
  final String? title;
  final Function(BannerNameGA4? bannerNameGA4, int index)? onVisible;
  final Function(BannerNameGA4? bannerNameGA4, int index)? onTap;

  const _SMStories({
    required this.items,
    required this.position,
    this.title,
    this.onVisible,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final storiesTheme = AzzasCmsThemeProvider.of(context).stories;
    return Align(
      alignment: Alignment.centerLeft,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 10.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (title.isNotNullOrEmpty)
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 24),
                child: Text(
                  title!,
                  style: storiesTheme.titleStyle,
                ),
              ),
            SizedBox(height: 48),
            SingleChildScrollView(
              padding: EdgeInsets.only(right: 24, left: 24),
              scrollDirection: Axis.horizontal,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: items
                    .mapIndexed<Widget>(
                      (index, story) => VisibilityDetector(
                        key: Key('stories_item_${story.title}'),
                        onVisibilityChanged: (info) {
                          if (info.visibleFraction >= 0.8 &&
                              onVisible != null) {
                            onVisible!(story.bannerNameGA4, index);
                          }
                        },
                        child: AzzasStoriesItem(
                          image:
                              StoryImage(url: story.media.data.attributes.url),
                          title: story.title,
                          onTap: () {
                            if (onTap != null)
                              onTap!(story.bannerNameGA4, index);
                            final String navigate = story.navigateTo
                                    .contains("?")
                                ? story.navigateTo
                                : "${story.navigateTo}?titulo=${story.title}";
                            NavigatorDynamic.call(navigate);
                          },
                        ),
                      ),
                    )
                    .toList(),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
