import 'package:azzas_cms/azzas_cms.dart';
import 'package:azzas_cms/models/cms_component.dart';
import 'package:azzas_cms/utils/color_utils.dart';
import 'package:flutter/material.dart';

class SpotProductClockCmsComponent extends CmsComponent {
  final String? title;
  final String? subtitle;
  final String? callToAction;
  final String? coupon;
  final String filterCategoryOrCluster;
  final String? orderBy;
  final DateTime startTime;
  final DateTime endTime;
  final Color? textColor;
  final Color? backgroundColor;
  final BannerNameGA4? bannerNameGA4;

  const SpotProductClockCmsComponent({
    required super.id,
    required super.componentType,
    required this.title,
    required this.subtitle,
    required this.callToAction,
    required this.coupon,
    required this.filterCategoryOrCluster,
    required this.orderBy,
    required this.startTime,
    required this.endTime,
    required this.textColor,
    required this.backgroundColor,
    required this.bannerNameGA4,
  });

  factory SpotProductClockCmsComponent.fromJson(Map<String, dynamic> json) {
    return SpotProductClockCmsComponent(
      id: json['id'],
      componentType: json['__component'],
      title: json['title'],
      subtitle: json['subtitle'],
      callToAction: json['call_to_action'],
      coupon: json['coupon'],
      filterCategoryOrCluster: json['filter_category_or_cluster'],
      orderBy: json['orderby'],
      startTime: DateTime.parse(json['start_time']),
      endTime: DateTime.parse(json['end_time']),
      textColor: ColorUtils.parsePrismicColor(json['text_color']),
      backgroundColor: ColorUtils.parsePrismicColor(json['background_color']),
      bannerNameGA4: json['banner_name_ga4'] is String
          ? BannerNameGA4.fromString(json['banner_name_ga4'])
          : null,
    );
  }
}
