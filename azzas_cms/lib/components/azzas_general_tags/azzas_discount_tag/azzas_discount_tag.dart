import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:flutter/material.dart';

class AzzasDiscountTag extends StatelessWidget {
  final Product product;

  const AzzasDiscountTag({
    super.key,
    required this.product,
  });

  @override
  Widget build(BuildContext context) {
    final hasDiscount = product.productDiscount > 0;

    if (!hasDiscount) return const SizedBox.shrink();

    return IntrinsicWidth(
      child: AzzasTag(
        label: '-${product.productDiscount}%',
        size: AzzasTagSize.small,
        type: AzzasTagType.success,
      ),
    );
  }
}
