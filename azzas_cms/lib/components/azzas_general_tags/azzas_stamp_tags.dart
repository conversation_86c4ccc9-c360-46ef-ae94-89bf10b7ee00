import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:flutter/material.dart';

/// Widget consolidado que combina stamps de ícones e tags de texto
/// Usa os widgets existentes que já estavam funcionando no sistema
class AzzasStampTags extends StatelessWidget {
  final Product product;

  const AzzasStampTags({
    super.key,
    required this.product,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        // Stamps de ícones (usando o widget existente)
        AzzasProductsStamps(product: product),

        // Tags de texto (usando o widget existente)
        AzzasProductsCmsStampTag(
          product: product,
          isPLP: false,
        ),
      ],
    );
  }
}
