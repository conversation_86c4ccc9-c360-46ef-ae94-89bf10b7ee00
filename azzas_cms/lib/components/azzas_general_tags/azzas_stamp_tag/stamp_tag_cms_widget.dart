import 'package:azzas_app_commons/azzas_app_commons.dart'
    show
        Product,
        BlocBuilder,
        TagState,
        TagCubit,
        AzzasProductsTagsModel,
        Search,
        Modular;
import 'package:azzas_cms/azzas_cms.dart';
import 'package:azzas_cms/widgets/azzas_tag.dart';

import 'package:flutter/material.dart';

class AzzasProductsCmsStampTag extends StatefulWidget {
  final Product product;
  final bool isPLP;
  final EdgeInsets? padding;

  const AzzasProductsCmsStampTag({
    super.key,
    required this.product,
    this.isPLP = false,
    this.padding,
  });

  @override
  State<AzzasProductsCmsStampTag> createState() =>
      _AzzasProductsCmsStampTagState();
}

class _AzzasProductsCmsStampTagState extends State<AzzasProductsCmsStampTag> {
  final _tagCubit = Modular.get<TagCubit>();

  @override
  void initState() {
    super.initState();
  }

  AzzasProductsTagsModel? getPriorityTag(Product product) {
    return _tagCubit.getPriorityStampTag(product);
  }

  void redirectToSearch(AzzasProductsTagsModel tag) {
    final search = Search(
      filterCategoryOrCluster: tag.filterCategoryOrCluster,
      orderBy: tag.orderByIntelligentSearch != null
          ? Search.getOrderByString(
              tag.orderByIntelligentSearch!,
            )
          : null,
      title: tag.title,
    );
    Modular.to.pushNamed('/searchResult', arguments: search);
  }

  @override
  Widget build(BuildContext context) {
    final productTagTheme = AzzasCmsThemeProvider.of(context).productTag;
    return BlocBuilder<TagCubit, TagState>(
      bloc: _tagCubit,
      builder: (context, state) {
        AzzasProductsTagsModel? tag = getPriorityTag(widget.product);
        if (tag == null) return SizedBox.shrink();
        return CmsAzzasTag(
            tagHeight: widget.isPLP ? CmsTagHeight.large : CmsTagHeight.medium,
            onTap: () => redirectToSearch(tag),
            text: tag.title,
            textColor: tag.titleColor,
            backgroundColor: tag.backgroundColor,
            textStyle: productTagTheme?.textStyle,
            borderColor: tag.borderColor,
            leftPadding: productTagTheme?.leftPadding,
            rightPadding: productTagTheme?.rightPadding,
            borderRadius: 16);
      },
    );
  }
}
