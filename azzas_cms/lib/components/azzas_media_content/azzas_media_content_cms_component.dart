import 'package:azzas_cms/azzas_cms.dart';

class MediaContentCmsComponent extends CmsComponent {
  final CmsMedia? media;
  final String? videoUrl;
  final String? navigateTo;
  final String? buttonText;
  final String? buttonNavigateTo;
  final BannerNameGA4? bannerNameGA4;
  final bool hasValidButton;

  MediaContentCmsComponent({
    required super.id,
    required super.componentType,
    this.media,
    this.videoUrl,
    this.navigateTo,
    this.buttonText,
    this.buttonNavigateTo,
    required this.bannerNameGA4,
    required this.hasValidButton,
  });

  factory MediaContentCmsComponent.fromJson(Map<String, dynamic> json) {
    return MediaContentCmsComponent(
      id: json['id'],
      componentType: json['__component'],
      media: json['media']['data'] == null
          ? null
          : CmsMedia.fromJson(json['media']),
      videoUrl: json['video_url'],
      navigateTo: json['navigate_to'],
      buttonText: json['button_text'],
      buttonNavigateTo: json['button_navigate_to'],
      bannerNameGA4: json['banner_name_ga4'] is String
          ? BannerNameGA4.fromString(json['banner_name_ga4'])
          : null,
      hasValidButton:
          json['button_text'] != null && json['button_navigate_to'] != null,
    );
  }
}
