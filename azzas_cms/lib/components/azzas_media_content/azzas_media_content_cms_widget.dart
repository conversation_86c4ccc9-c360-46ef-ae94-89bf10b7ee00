import 'package:azzas_app_commons/azzas_app_commons.dart';

import 'package:flutter/material.dart';

class MediaContentCmsWidget extends StatelessWidget {
  const MediaContentCmsWidget({
    this.media,
    this.videoUrl,
    this.navigateTo,
    this.buttonText,
    this.buttonNavigateTo,
    this.bannerNameGA4,
    required this.hasValidButton,
  });

  final CmsMedia? media;
  final String? videoUrl;
  final String? navigateTo;
  final String? buttonText;
  final String? buttonNavigateTo;
  final bool hasValidButton;
  final BannerNameGA4? bannerNameGA4;

  factory MediaContentCmsWidget.fromComponent(
      MediaContentCmsComponent component) {
    return MediaContentCmsWidget(
      media: component.media,
      videoUrl: component.videoUrl,
      navigateTo: component.navigateTo,
      buttonText: component.buttonText,
      buttonNavigateTo: component.buttonNavigateTo,
      hasValidButton: component.hasValidButton,
      bannerNameGA4: component.bannerNameGA4,
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = AzzasCmsThemeProvider.of(context).mediaContentTheme;

    return Stack(
      children: [
        InkWell(
          onTap: navigateTo.isNotNullOrEmpty
              ? () => NavigatorDynamic.call(navigateTo!)
              : () {},
          child: media == null && videoUrl.isNotNullOrEmpty
              ? AzzasVideo(
                  videoType: VideoType.network,
                  mediaUrl: videoUrl!,
                  looping: true,
                  startVideo: true,
                  showButtonPlay: false,
                  volume: 0,
                  fullScreen: true,
                  mixWithOthers: true,
                )
              : AzzasCachedNetworkingImage(
                  alignment: Alignment.topCenter,
                  imageUrl: media!.data.attributes.url,
                  width: double.infinity,
                  height: double.infinity,
                ),
        ),
        if (hasValidButton)
          Positioned(
            bottom: 16,
            left: 16,
            right: 16,
            child: Center(
              child: AzzasButton.secondary(
                size: ButtonSize.small,
                expanded: true,
                trailing: Icon(
                  theme.buttonIconTrailing,
                  size: 16,
                ),
                child: Text(
                  buttonText!,
                  style: theme.buttonTextStyle,
                ),
                onPressed: () => NavigatorDynamic.call(buttonNavigateTo!),
              ),
            ),
          ),
      ],
    );
  }
}
