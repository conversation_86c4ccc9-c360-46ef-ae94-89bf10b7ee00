import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class NotificationCenterHomeMessage extends StatefulWidget {
  const NotificationCenterHomeMessage({
    Key? key,
    required this.message,
    this.onHide,
  }) : super(key: key);

  final NotificationCenterMessage message;
  final VoidCallback? onHide;

  @override
  State<NotificationCenterHomeMessage> createState() =>
      _NotificationCenterHomeMessageState();
}

class _NotificationCenterHomeMessageState
    extends State<NotificationCenterHomeMessage> with TickerProviderStateMixin {
  // TODO: Event
  // @override
  // void initState() {
  //   super.initState();
  //   scheduleMicrotask(() {
  //     dispatchViewPromotionEvent(
  //       screenClass: 'NotificationCenterHomeMessage',
  //       context,
  //       promotionName: widget.message.title,
  //       creativeName: widget.message.message,
  //     );
  //   });
  // }
  final _eventDispatcher = Modular.get<EventDispatcher>();

  void logSelectedContent(String text) =>
      _eventDispatcher.logSelectedContent("home:notification:$text");

  @override
  Widget build(BuildContext context) {
    final notificationListItemTheme =
        AzzasCmsThemeProvider.of(context).notificationCenter.listItemTheme;
    final notificationHomeTheme = AzzasCmsThemeProvider.of(context)
        .notificationCenter
        .homeNotificationTheme;
    final notificationForegroundColor =
        notificationHomeTheme.notificationDefaultForeground;

    final leadingImageSize = notificationListItemTheme.leadingImageSize;
    final messageType = widget.message.messageType;

    final foregroundColor =
        widget.message.alertTextColor?.toColor() ?? notificationForegroundColor;

    return AzzasHomeNotification(
      titleStyle:
          notificationHomeTheme.titleStyle.copyWith(color: foregroundColor),
      messageStyle:
          notificationHomeTheme.messageStyle.copyWith(color: foregroundColor),
      title: Text(widget.message.title),
      message: Text(widget.message.message),
      backgroundColor: widget.message.alertBackgroundColor?.toColor() ??
          foregroundColor.withOpacity(0.9),
      textColor: widget.message.alertTextColor?.toColor(),
      leading: widget.message.iconProvider == null
          ? null
          : Image(
              image: widget.message.iconProvider!,
              color: notificationForegroundColor,
              width: leadingImageSize ?? 24,
              height: leadingImageSize ?? 24,
            ),
      trailing: Icon(
        messageType == NotificationMessageType.expandedMessageBanner
            ? notificationHomeTheme.expandedMesageIcon
            : notificationHomeTheme.notExpandedMesageIcon,
        color: widget.message.alertTextColor?.toColor() ??
            notificationForegroundColor,
      ),
      duration: const Duration(seconds: 5),
      onHide: widget.onHide,
      onTap: () {
        logSelectedContent(widget.message.title);
        _getActionOnTap();
        ;
      },
    );
  }

  _getActionOnTap() {
    if (widget.message.messageType ==
        NotificationMessageType.expandedMessageBanner) {
      return showAzzasBottomSheet(
          context: context,
          vsync: this,
          builder: (context) {
            return _ExpandedNotificationBottomSheet(
              callToAction: widget.message.callToAction,
              navigateTo: widget.message.navigateTo,
              title: widget.message.title,
              message: widget.message.message,
              textContent: widget.message.textContent,
              bannerImage: widget.message.bannerProvider,
              coupon: widget.message.coupon,
            );
          });
    }

    return widget.message.redirectType ==
            NotificationRedirectType.navigateToNotificationCenter
        ? Modular.to.pushNamed('/notification-center')
        : NavigatorDynamic.call(widget.message.navigateTo);
  }
}

class _ExpandedNotificationBottomSheet extends StatefulWidget {
  final String navigateTo;
  final String title;
  final String? callToAction;
  final String? message;
  final List<CmsTextContent>? textContent;
  final ImageProvider<Object>? bannerImage;
  final String? coupon;

  const _ExpandedNotificationBottomSheet({
    required this.navigateTo,
    required this.title,
    this.callToAction,
    this.message,
    this.textContent,
    this.bannerImage,
    this.coupon,
  });

  @override
  State<_ExpandedNotificationBottomSheet> createState() =>
      _ExpandedNotificationBottomSheetState();
}

class _ExpandedNotificationBottomSheetState
    extends State<_ExpandedNotificationBottomSheet> {
  @override
  Widget build(BuildContext context) {
    final OrderCheckoutHandler orderHandler =
        Modular.get<OrderCheckoutHandler>();
    bool isCouponApplied = (orderHandler.getAppliedCoupon() == widget.coupon);

    final theme = AzzasCmsThemeProvider.of(context)
        .notificationCenter
        .homeNotificationTheme;
    return AzzasBottomSheet(
      content: SizedBox(),
      scrollContent: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          AzzasMainContent(
            title: widget.title,
            subtitle: widget.message,
            titlePadding: EdgeInsets.only(bottom: 0),
            titleStyle: theme.bottomSheetTitleTextStyle,
            subtitleStyle: theme.bottomSheetDescriptionTextStyle,
          ),
          ConstrainedBox(
            constraints: BoxConstraints(
              minWidth: MediaQuery.of(context).size.width,
              maxHeight: MediaQuery.of(context).size.height * (double.infinity),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildTextContent(widget.textContent),
                if (widget.bannerImage != null) ...[
                  SizedBox(height: 12),
                  AzzasImage(image: widget.bannerImage!),
                ],
                SizedBox(height: 24),
                ..._buildActionButtons(
                    isCouponApplied: isCouponApplied,
                    orderHandler: orderHandler)
              ],
            ),
          ),
        ],
      ),
    );
  }

  List<Widget> _buildActionButtons({
    required bool isCouponApplied,
    required OrderCheckoutHandler orderHandler,
  }) {
    final hasCoupon = widget.coupon.isNotNullOrEmpty;
    final hasCallToAction = widget.callToAction.isNotNullOrEmpty &&
        widget.navigateTo.isNotNullOrEmpty;

    final List<Widget> buttons = [];
    final theme = AzzasCmsThemeProvider.of(context)
        .notificationCenter
        .homeNotificationTheme;

    if (hasCoupon) {
      buttons.addAll([
        Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: AzzasButton.secondary(
            expanded: true,
            onPressed: () async {
              await _handleOnTapSecondButton(
                isCouponApplied: isCouponApplied,
                orderHandler: orderHandler,
              );
            },
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(_getSecondButtonTitle(isCouponApplied)!,
                    textAlign: TextAlign.center),
                SizedBox(width: 4),
                if (isCouponApplied)
                  Icon(
                    theme.copyIcon,
                    size: 16.0,
                  )
              ],
            ),
          ),
        ),
      ]);
    }

    if (hasCallToAction) {
      buttons.add(
        hasCoupon
            ? AzzasButton.primary(
                expanded: true,
                onPressed: () => NavigatorDynamic.call(widget.navigateTo),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(widget.callToAction!, textAlign: TextAlign.center),
                  ],
                ),
              )
            : AzzasButton.secondary(
                expanded: true,
                onPressed: () => NavigatorDynamic.call(widget.navigateTo),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(widget.callToAction!, textAlign: TextAlign.center),
                  ],
                ),
              ),
      );
    }

    buttons.add(SizedBox(height: 24));
    return buttons;
  }

  String? _getSecondButtonTitle(bool isCouponApplied) {
    if (widget.coupon.isNullOrEmpty) {
      return null;
    }
    if (isCouponApplied) {
      return widget.coupon;
    }

    return 'Aplicar cupom na sacola';
  }

  Future<void> _handleOnTapSecondButton(
      {required bool isCouponApplied,
      required OrderCheckoutHandler orderHandler}) async {
    if (widget.coupon.isNullOrEmpty) return;

    if (isCouponApplied) {
      await Clipboard.setData(ClipboardData(text: widget.coupon!));
      _showSnackBar(context: context, message: "Cupom copiado");
      return;
    }

    orderHandler.addCoupon(coupon: widget.coupon!, context: context);
  }
}

void _showSnackBar({
  required BuildContext context,
  required String message,
}) {
  AzzasSnackBar.show(
    context: context,
    message: message,
  );
}

Widget _buildTextContent(textContent) {
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    mainAxisAlignment: MainAxisAlignment.start,
    children: [
      ...?textContent
          ?.map(
            (content) => _TextContent(
              textContent: content,
            ),
          )
          .toList(),
    ],
  );
}

class _TextContent extends StatelessWidget {
  const _TextContent({
    Key? key,
    required this.textContent,
  }) : super(key: key);

  final CmsTextContent textContent;

  @override
  Widget build(BuildContext context) {
    final homeNotificationTheme = AzzasCmsThemeProvider.of(context)
        .notificationCenter
        .homeNotificationTheme;

    if (textContent.title == null && textContent.description == null) {
      return SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (textContent.title.isNotNullOrEmpty) ...[
          Text(
              textAlign: TextAlign.start,
              textContent.title ?? "",
              style: homeNotificationTheme.bottomSheetTitleTextStyle),
          SizedBox(height: 8),
        ],
        if (textContent.description.isNotNullOrEmpty) ...[
          Text(textContent.description?.replaceAll("\\n", "\n") ?? '',
              style: homeNotificationTheme.bottomSheetDescriptionTextStyle),
          SizedBox(height: 24)
        ],
      ],
    );
  }
}
