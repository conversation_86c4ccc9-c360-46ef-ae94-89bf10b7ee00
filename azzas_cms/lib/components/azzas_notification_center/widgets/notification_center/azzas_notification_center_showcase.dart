import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:flutter/material.dart';

class NotificationCenterShowCaseWidget extends StatefulWidget {
  const NotificationCenterShowCaseWidget(this.component, {super.key});

  final ShowCaseListCmsComponentt component;

  @override
  State<NotificationCenterShowCaseWidget> createState() =>
      _ShowCaseListCmsWidgetState();
}

class _ShowCaseListCmsWidgetState
    extends State<NotificationCenterShowCaseWidget> {
  List<Product> productsShowcase = [];
  bool isLoadingShowcase = false;
  Search? filter;
  final _intelligentSearchUseCase = Modular.get<IntelligentSearchUseCase>();

  @override
  void initState() {
    super.initState();
    if (widget.component.items?.isEmpty ?? true) {
      loadProducts(
        filterCategoryOrCluster: widget.component.filterCategoryOrCluster,
        orderBy: widget.component.orderByIntelligentSearch,
      );
    }
  }

  Future<List<Product>> loadProducts(
      {String? filterCategoryOrCluster, String? orderBy}) async {
    if (mounted) {
      setState(() {
        isLoadingShowcase = true;
      });
    }

    List<Product> products = [];

    try {
      final search = Search(
        filterCategoryOrCluster: filterCategoryOrCluster,
        orderBy: orderBy != null ? Search.getOrderByString(orderBy) : null,
      );
      List<Product> searchResponse = [];
      await _intelligentSearchUseCase.call(
        search: search.buildIntelligentSearchWithFilters(),
      );
      products = searchResponse;
    } catch (e, st) {
      debugPrint('Erro ao carregar produtos do prismic showcase: $e');
      debugPrintStack(stackTrace: st);
    }

    if (mounted) {
      setState(() {
        productsShowcase = products;
        isLoadingShowcase = false;
      });
    }

    return products;
  }

  @override
  Widget build(BuildContext context) {
    AzzasShowcaseButton showcaseButton = AzzasShowcaseButton(
      text: widget.component.callToAction ?? '',
      onTap: () async {
        Modular.to.pushNamed(
          '/pdc',
          arguments: Search(
            filterCategories: widget.component.filterCategoryOrCluster,
          ),
        );
      },
      textColor: Colors.black,
      backgroundColor: Colors.white,
    );
    List<AzzasShowcaseButton> showcaseList = (widget.component.items ?? [])
        .map((item) => AzzasShowcaseButton(
            text: item.title ?? '',
            onTap: () {
              setState(() {
                filter = Search(
                  title: item.title,
                  filterCategoryOrCluster: item.filterCategoryOrCluster,
                  orderBy: item.orderByIntelligentSearch != null
                      ? Search.getOrderByString(item.orderByIntelligentSearch!)
                      : null,
                );
              });
              loadProducts(
                filterCategoryOrCluster: item.filterCategoryOrCluster,
                orderBy: item.orderByIntelligentSearch,
              );
            },
            backgroundColor: Colors.white,
            textColor: Colors.black))
        .toList();

    return AzzasCmsContentSpacer(
      child: AzzasShowcase(
        hasPlusButton: true,
        productsShowcase: productsShowcase,
        isLoading: isLoadingShowcase,
        callToAction: showcaseButton,
        showcaseButtonList: showcaseList,
        titleText: widget.component.title,
        subtitleText: widget.component.subtitle,
        hasFavoriteButton: false,
        onTapProduct: (Product product, int index) {
          Modular.to.pushNamed('/pdp', arguments: product);

          // TODO: Event
          // dispatchSelectItem(
          //     product: product,
          //     listName: widget.component.title,
          //     screenClass: 'Showcase');
          // analyticsController.logPrismicComponent(
          //   'vitrine',
          //   redirectTo: pdpRoute,
          //   position: SmCmsContentMetadata.of(context).index,
          //   childPosition: productsShowcase
          //       .indexWhere((p) => p.productId == product.productId),
          // );
        },
      ),
    );
  }
}
