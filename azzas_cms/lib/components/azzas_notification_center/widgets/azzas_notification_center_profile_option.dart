import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:flutter/material.dart' hide BadgeTheme;

class NotificationCenterProfileOption extends StatefulWidget {
  const NotificationCenterProfileOption({
    Key? key,
    required this.notificationsCubit,
  }) : super(key: key);

  final NotificationCenterCubit notificationsCubit;
  @override
  State<NotificationCenterProfileOption> createState() =>
      _NotificationCenterProfileOptionState();
}

class _NotificationCenterProfileOptionState
    extends State<NotificationCenterProfileOption> {
  final _eventDispatcher = Modular.get<EventDispatcher>();

  @override
  Widget build(BuildContext context) {
    int newNotificationsCount =
        widget.notificationsCubit.state.newNotificationsCount;
    final theme =
        AzzasCmsThemeProvider.of(context).notificationCenter.listItemTheme;

    return BlocBuilder<NotificationCenterCubit, NotificationCenterState>(
        bloc: widget.notificationsCubit,
        builder: (context, state) {
          return AzzasListItemIcon(
            onTap: () async {
              _eventDispatcher
                  .logSelectedContent("area-logada:perfil:minhas-notificacoes");
              Modular.to.pushNamed('/notification-center');
            },
            leftIcon: Icon(theme.emailIcon),
            title: 'Minhas notificações',
            divider: true,
            subtitle: newNotificationsCount > 0
                ? newNotificationsCount == 1
                    ? '1 mensagem não lida'
                    : '${newNotificationsCount} mensagens não lidas'
                : null ?? '',
            trailing: _buildTrailing(context),
          );
        });
  }

  Widget _buildTrailing(BuildContext context) {
    if (widget.notificationsCubit.state.isLoading) {
      return const Padding(
        padding: EdgeInsets.only(right: 8),
        child: AzzasSpinner(size: 24),
      );
    } else {
      return Row(
        children: [
          if (widget.notificationsCubit.state.newNotificationsCount > 0) ...[
            AzzasBadge(
              badgeTypeTheme: AzzasBadgeTypeTheme.dark,
              badgeCounter:
                  '${widget.notificationsCubit.state.newNotificationsCount}',
              widget: const SizedBox.shrink(),
            ),
            // const SizedBox(width: 16),
          ],
          // Icon(
          //   theme.trailingIcon,
          //   color: theme.trailingIconColor,
          //   size: 32,
          // ),
        ],
      );
    }
  }
}

class NotificationCenterListItemTheme
    extends ThemeExtension<NotificationCenterListItemTheme> {
  const NotificationCenterListItemTheme({
    this.subtitleStyle,
  });

  final TextStyle? subtitleStyle;

  @override
  ThemeExtension<NotificationCenterListItemTheme> copyWith({
    TextStyle? subtitleStyle,
  }) {
    return NotificationCenterListItemTheme(
      subtitleStyle: subtitleStyle ?? this.subtitleStyle,
    );
  }

  @override
  ThemeExtension<NotificationCenterListItemTheme> lerp(
    covariant NotificationCenterListItemTheme? other,
    double t,
  ) {
    return NotificationCenterListItemTheme(
      subtitleStyle: TextStyle.lerp(subtitleStyle, other?.subtitleStyle, t),
    );
  }
}
