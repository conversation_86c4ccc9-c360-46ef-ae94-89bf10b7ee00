import 'dart:async';
import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

class NotificationCenterPage extends StatefulWidget {
  const NotificationCenterPage({Key? key}) : super(key: key);

  @override
  State<NotificationCenterPage> createState() => _NotificationCenterState();
}

class _NotificationCenterState extends State<NotificationCenterPage> {
  final notificationCenterCubit = Modular.get<NotificationCenterCubit>();
  final authController = Modular.get<AuthCubit>();
  final _eventDispatcher = Modular.get<EventDispatcher>();
  List<NotificationCenterCmsComponent>? components = [];

  @override
  void dispose() {
    scheduleMicrotask(() => notificationCenterCubit.markAllMessagesAsViewed());
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
  }

  String composeLogSelectContent(String text) {
    return "area-logada:minhas-notificacoes:$text";
  }

  @override
  Widget build(BuildContext context) {
    final theme = AzzasCmsThemeProvider.of(context).notificationCenter;
    return Scaffold(
      body: BlocBuilder<NotificationCenterCubit, NotificationCenterState>(
          bloc: notificationCenterCubit,
          builder: (context, state) {
            return CustomScrollView(
              slivers: [
                AzzasSmallAppBar(
                  title: "Minhas notificações",
                  titleTextStyle: theme.navBarStyle?.titleTextStyle,
                  actions: theme.navBarStyle?.actions,
                  supportText: theme.navBarStyle?.showNotificationsCounter ==
                              true &&
                          notificationCenterCubit.state.messages.isNotEmpty
                      ? "${notificationCenterCubit.state.messages.length} Notificações"
                      : '',
                  onBackButton: () => Navigator.pop(context),
                ),
                ..._buildBody(context, notificationCenterCubit),
              ],
            );
          }),
    );
  }

  List<Widget> _buildBody(BuildContext context, NotificationCenterCubit cubit) {
    final theme = AzzasCmsThemeProvider.of(context).notificationCenter;

    if (cubit.state.isLoading) {
      return const [
        SliverFillRemaining(
          child: Center(child: AzzasSpinner()),
        ),
      ];
    }

    if (authController.state.isLoggedIn != true) {
      return [
        SliverFillRemaining(
          child: _EmptyOrUnloggedNotificationCenter(
            NotificationCenterScreenType.unlogged,
          ),
        ),
      ];
    }

    if (cubit.state.messages.isEmpty &&
        theme.showEmptyNotificationScreen == true) {
      return [
        SliverFillRemaining(
          child: _EmptyOrUnloggedNotificationCenter(
            NotificationCenterScreenType.empty,
          ),
        ),
      ];
    }

    return [
      SliverPadding(
        padding: const EdgeInsets.only(bottom: 16),
        sliver: SliverList(
          delegate: SliverChildBuilderDelegate(
            (context, index) {
              final message = cubit.state.messages[index];

              if (message.callToAction != '' &&
                  message.callToAction != null &&
                  message.bannerProvider == null) {
                return NotificationCenterItemCta.fromNotificationCenterMessage(
                  message,
                  onTap: () {
                    _eventDispatcher.logSelectedContent(composeLogSelectContent(
                        "${message.title}:${message.callToAction}"));
                    NavigatorDynamic.call(
                      cubit.state.messages[index].navigateTo,
                    );
                  },
                );
              } else {
                return NotificationCenterItem.fromNotificationCenterMessage(
                  message,
                  onTap: () {
                    _eventDispatcher.logSelectedContent(
                        composeLogSelectContent(message.title));

                    NavigatorDynamic.call(
                      cubit.state.messages[index].navigateTo,
                    );
                  },
                );
              }
            },
            childCount: cubit.state.messages.length,
          ),
        ),
      )
    ];
  }
}

class _EmptyOrUnloggedNotificationCenter extends StatefulWidget {
  const _EmptyOrUnloggedNotificationCenter(this.screenType);

  final NotificationCenterScreenType screenType;

  @override
  State<_EmptyOrUnloggedNotificationCenter> createState() =>
      __EmptyOrUnloggedNotificationCenterState();
}

class __EmptyOrUnloggedNotificationCenterState
    extends State<_EmptyOrUnloggedNotificationCenter> {
  List<NotificationCenterCmsComponent>? componentsEmptyOrUnlogged = [];
  bool isLoading = false;
  final notificationCenterCubit = Modular.get<NotificationCenterCubit>();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadNotificationCenterEmptyOrUnloggedComponents();
    });
  }

  Future<void> _loadNotificationCenterEmptyOrUnloggedComponents() async {
    final service = Modular.get<CmsService>();
    final notificationCenterComponents = await service
        .loadDynamicComponentsForDocument<NotificationCenterCmsComponent>(
            widget.screenType == NotificationCenterScreenType.unlogged
                ? "unlogged-notification-center"
                : "empty-notification-center",
            componentParser: NotificationCenterCmsComponent.fromJson);
    if (mounted) {
      setState(() {
        componentsEmptyOrUnlogged = notificationCenterComponents ?? [];
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!isLoading) {
      return AzzasCmsContent(
        components: componentsEmptyOrUnlogged!,
        renderDynamicCmsComponent: renderNotificationCenterCmsComponent,
      );
    }
    return const Center(
      child: AzzasSpinner(),
    );
  }
}

enum NotificationCenterScreenType {
  empty,
  unlogged,
}

Widget renderNotificationCenterCmsComponent(component) {
  return switch (component) {
    RedirectSectionCmsComponent() =>
      NotificationCenterRedirectSectionWidget.fromComponent(component),
    ShowCaseListCmsComponentt() => NotificationCenterShowCaseWidget(component),
    // Somente em NV
    // NotificationSignUpLoginCmsComponent() =>
    //   NotificationSignUpLoginCmsWidget(component),
    ParseErrorCmsComponent() when kDebugMode => ParseErrorWarning(component),
    _ when kDebugMode => UnknownComponentWarning(component),
    _ => const SizedBox.shrink(),
  };
}

class ParseErrorWarning extends StatelessWidget {
  const ParseErrorWarning(this.component, {super.key});
  final ParseErrorCmsComponent component;
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.red,
      alignment: Alignment.center,
      child: Text(
        'Error de parse: \n${component.details} \n ${component.error} \n ${component.stackTrace}',
      ),
    );
  }
}

class NotificationCenterParseErrorNotificationCenterCmsComponent
    extends NotificationCenterCmsComponent {
  const NotificationCenterParseErrorNotificationCenterCmsComponent({
    required this.error,
    required this.stackTrace,
    required this.details,
  }) : super(id: -1, componentType: 'internal-error');
  final Object error;
  final StackTrace stackTrace;
  final String details;
}
