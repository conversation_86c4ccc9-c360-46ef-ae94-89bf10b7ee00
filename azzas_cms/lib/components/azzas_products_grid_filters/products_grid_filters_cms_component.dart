import 'package:azzas_cms/azzas_cms.dart';
import 'package:flutter/material.dart';

class ProductsGridFiltersCmsComponent extends CmsComponent {
  final String title;
  final String? subtitle;
  final String? callToAction;
  final String filterCategoryOrCluster;
  final String? orderByIntelligentSearch;
  final List<ProductsGridFiltersCmsComponentItem>? items;
  final Color? foregroundColor;
  final Color? backgroundColor;
  final BannerNameGA4? bannerNameGA4;
  const ProductsGridFiltersCmsComponent({
    required super.id,
    required super.componentType,
    required this.title,
    required this.filterCategoryOrCluster,
    this.subtitle,
    this.callToAction,
    this.orderByIntelligentSearch,
    this.items,
    this.foregroundColor,
    this.backgroundColor,
    required this.bannerNameGA4,
  });

  factory ProductsGridFiltersCmsComponent.fromJson(Map<String, dynamic> json) {
    return ProductsGridFiltersCmsComponent(
      id: json['id'],
      componentType: json['__component'],
      title: json['title'],
      subtitle: json['subtitle'],
      callToAction: json['call_to_action'],
      filterCategoryOrCluster: json['filter_category_or_cluster'],
      orderByIntelligentSearch: json['order_by_intelligent_search'],
      items: (json['items'] as List)
          .map((j) => ProductsGridFiltersCmsComponentItem.fromJson(j))
          .toList(),
      backgroundColor: ColorUtils.parsePrismicColor(json['background_color']),
      foregroundColor: ColorUtils.parsePrismicColor(json['foreground_color']),
      bannerNameGA4: json['banner_name_ga4'] is String
          ? BannerNameGA4.fromString(json['banner_name_ga4'])
          : null,
    );
  }
}

class ProductsGridFiltersCmsComponentItem {
  const ProductsGridFiltersCmsComponentItem({
    required this.id,
    required this.title,
    required this.filterCategoryOrCluster,
    this.orderByIntelligentSearch,
  });

  factory ProductsGridFiltersCmsComponentItem.fromJson(
      Map<String, dynamic> json) {
    return ProductsGridFiltersCmsComponentItem(
      id: json['id'],
      title: json['title'],
      filterCategoryOrCluster: json['filter_category_or_cluster'],
      orderByIntelligentSearch: json['order_by_intelligent_search'],
    );
  }

  final int id;
  final String? title;
  final String? filterCategoryOrCluster;
  final String? orderByIntelligentSearch;
}
