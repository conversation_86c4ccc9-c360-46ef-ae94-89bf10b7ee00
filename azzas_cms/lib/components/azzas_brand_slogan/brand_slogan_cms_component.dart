import 'package:azzas_cms/azzas_cms.dart';
import 'package:azzas_cms/models/cms_component.dart';
import 'package:azzas_cms/models/cms_media.dart';
import 'package:azzas_cms/utils/color_utils.dart';
import 'package:flutter/material.dart';

class BrandSloganCmsComponent extends CmsComponent {
  BrandSloganCmsComponent({
    required super.id,
    required super.componentType,
    required this.media,
    this.title,
    this.subtitle,
    this.backgroundColor,
    this.textColor,
    required this.bannerNameGA4,
  });

  factory BrandSloganCmsComponent.fromJson(Map<String, dynamic> json) {
    return BrandSloganCmsComponent(
      id: json['id'],
      componentType: json['__component'],
      title: json['title'],
      subtitle: json['subtitle'],
      media: CmsMedia.fromJson(json['media']),
      backgroundColor:
          ColorUtils.parseStrapiColor(json['background']) ?? Colors.white,
      textColor:
          ColorUtils.parseStrapiColor(json['text_color']) ?? Colors.black,
      bannerNameGA4: json['banner_name_ga4'] is String
          ? BannerNameGA4.fromString(json['banner_name_ga4'])
          : null,
    );
  }

  final String? title;
  final String? subtitle;
  final CmsMedia media;
  final Color? backgroundColor;
  final Color? textColor;
  final BannerNameGA4? bannerNameGA4;
}
