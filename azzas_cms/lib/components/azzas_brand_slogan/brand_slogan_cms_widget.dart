import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:flutter/material.dart';

class BrandSloganCmsWidget extends StatefulWidget {
  final String? title;
  final String? subtitle;
  final Color? backgroundColor;
  final Color? textColor;
  final CmsMedia media;
  final BannerNameGA4? bannerNameGA4;

  const BrandSloganCmsWidget({
    super.key,
    this.title,
    this.subtitle,
    this.backgroundColor,
    required this.media,
    this.textColor,
    required this.bannerNameGA4,

  });

  factory BrandSloganCmsWidget.fromComponent(
      BrandSloganCmsComponent component) {
    return BrandSloganCmsWidget(
      media: component.media,
      title: component.title,
      subtitle: component.subtitle,
      backgroundColor: component.backgroundColor,
      textColor: component.textColor,
      bannerNameGA4: component.bannerNameGA4,
    );
  }

  @override
  State<BrandSloganCmsWidget> createState() => _BrandSloganCmsWidgetState();
}

class _BrandSloganCmsWidgetState extends State<BrandSloganCmsWidget> {
  @override
  Widget build(BuildContext context) {
    final theme = AzzasCmsThemeProvider.of(context).brandSloganTheme;
    return Container(
          color: widget.backgroundColor,
          child: Column(
    crossAxisAlignment: CrossAxisAlignment.center,
    children: [
      Padding(
        padding: EdgeInsets.symmetric(horizontal: 60),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            if (widget.title?.trim().isNotEmpty ?? false) ...[
              SizedBox(height: 24,),
              Text(
                widget.title!,
                style: theme.titleStyle.copyWith(
                    fontWeight: FontWeight.bold, color: widget.textColor),
              )
            ],
            SizedBox(
              height: 16,
            ),
            if (widget.subtitle?.trim().isNotEmpty ?? false) ...[
              Text(
                widget.subtitle!,
                textAlign: TextAlign.center,
                style:
                    theme.subtitleStyle.copyWith(color: widget.textColor),
              )
            ],
            SizedBox(
              height: 60,
            )
          ],
        ),
      ),
      Expanded(
        child: AzzasImage(
          imageWidth: double.infinity,
          image: NetworkImage(widget.media.data.attributes.url),
        ),
      ),
    ],
          ),
        );
  }
}
