import 'package:azzas_app_commons/azzas_app_commons.dart'
    show
        Azzas<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
        EventD<PERSON>patcher,
        Modular,
        NavigatorDynamic,
        OrderBy,
        Product,
        Search;
import 'package:azzas_cms/utils/azzas_cms_content_spacer.dart';
import 'package:azzas_cms/components/azzas_spot_product/azzas_spot_product_carousel.dart';
import 'package:azzas_cms/components/azzas_spot_product/spot_product_cms_component.dart';
import 'package:flutter/material.dart';

class SpotProductCmsWidget extends StatefulWidget {
  final String title;
  final String subTitle;
  final String? callToAction;
  final OrderBy? orderBy;
  final String filterCategoryOrCluster;
  final String? filterColor;
  final String? filterSize;
  final String? query;
  final String? searchTitle;
  final String componentType;
  final ValueChanged<Product>? onTap;

  const SpotProductCmsWidget({
    super.key,
    required this.title,
    required this.subTitle,
    this.callToAction,
    this.orderBy,
    required this.filterCategoryOrCluster,
    this.filterColor,
    this.filterSize,
    this.query,
    this.searchTitle,
    required this.componentType,
    this.onTap,
  });

  factory SpotProductCmsWidget.fromSpotProductCmsComponent(
    SpotProductCmsComponent component, {
    ValueChanged<Product>? onTap,
  }) {
    return SpotProductCmsWidget(
      title: component.title,
      subTitle: component.subTitle,
      callToAction: component.callToAction,
      orderBy: component.getOrderBy,
      filterCategoryOrCluster: component.filterCategoryOrCluster,
      filterColor: component.filterColor,
      filterSize: component.filterSize,
      query: component.query,
      searchTitle: component.searchTitle,
      componentType: component.componentType,
      onTap: onTap,
    );
  }

  static const screenClass = "CmsSpotProduct";

  @override
  State<SpotProductCmsWidget> createState() => _SpotProductCmsWidgetState();
}

class _SpotProductCmsWidgetState extends State<SpotProductCmsWidget> {
  final _eventDispatcher = Modular.get<EventDispatcher>();

  void logSelectedContent(String text) =>
      _eventDispatcher.logSelectedContent(text);

  void _onTapProduct(Product product, int childPosition) {
    product.updateProductHero(SpotProductCmsWidget);
    widget.onTap?.call(product);
  }

  Search get search {
    return Search(
      title: widget.searchTitle,
      filterCategoryOrCluster: widget.filterCategoryOrCluster,
      filterColor: widget.filterColor,
      filterSize: widget.filterSize,
      query: widget.query,
      orderBy: widget.orderBy,
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = AzzasCmsThemeProvider.of(context).spotProduct;
    return AzzasCmsContentSpacer(
      child: AzzasSpotProductCarousel(
        noMargin: true,
        onTapProduct: (Product product, int childPosition) {
          widget.onTap != null
              ? _onTapProduct(product, childPosition)
              : Modular.to.pushNamed('/pdp', arguments: product);
        },
        search: search,
        textPrimary: widget.title,
        textSecondary: widget.subTitle,
        callToAction: widget.callToAction,
        screenClass: SpotProductCmsWidget.screenClass,
        leftPaddingComponent: theme?.leftPaddingComponent ?? 24,
        hasFavoriteButton: true,
        textLeftSpacing: theme?.textLeftSpacing ?? 16,
        containerHeight: theme?.containerHeight,
        textTopSpacing: theme?.textTopSpacing ?? 24,
        textPrimaryStyle:
            theme?.textPrimaryStyle ?? const TextStyle(fontSize: 20),
        textSecondaryStyle:
            theme?.textSecondaryStyle ?? const TextStyle(fontSize: 16),
        topPadding: theme?.topCallToActionPadding,
        callToActionIcon: theme?.callToActionIcon,
        callToActionTextStyle: theme?.callToActionTextStyle,
        productTitleStyle: theme?.productTitleStyle,
        productTitleBottomSpacing: theme?.productTitleBottomSpacing,
        priceTextStyle: theme?.priceTextStyle,
        oldPriceTextStyle: theme?.oldPriceTextStyle,
        installmentsTextStyle: theme?.installmentsTextStyle,
        onTapCallToAction: () {
          logSelectedContent("${widget.title}:${widget.callToAction}");
          NavigatorDynamic.call("plp/${widget.filterCategoryOrCluster}");
        },
        hasInstallmentsText: theme?.hasInstallmentsText ?? false,
      ),
    );
  }
}
