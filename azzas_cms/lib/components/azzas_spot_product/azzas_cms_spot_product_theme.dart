import 'package:azzas_cms/components/azzas_spot_product/utils/azzas_price_builder.dart';
import 'package:flutter/material.dart';

class AzzasCmsSpotProductTheme {
  final TextStyle? textPrimaryStyle;
  final TextStyle? textSecondaryStyle;
  final double? leftPaddingComponent;
  final double? textLeftSpacing;
  final double? textTopSpacing;
  final double? topCallToActionPadding;
  final TextStyle? callToActionTextStyle;
  final Icon? callToActionIcon;
  final double? containerHeight;
  final TextStyle? productTitleStyle;
  final double? productTitleBottomSpacing;
  final TextStyle? priceTextStyle;
  final TextStyle? oldPriceTextStyle;
  final TextStyle? installmentsTextStyle;
  final bool? hasInstallmentsText;
  final AzzasSpotProductPriceBuilder? productFullPriceBuilder,
      productCurrentPriceBuilder;
  final BorderRadius? imageBorderRadius;

  const AzzasCmsSpotProductTheme({
    required this.textPrimaryStyle,
    required this.textSecondaryStyle,
    this.leftPaddingComponent,
    this.textLeftSpacing,
    this.textTopSpacing,
    this.topCallToActionPadding,
    this.callToActionTextStyle,
    this.callToActionIcon,
    this.containerHeight,
    this.productTitleStyle,
    this.productTitleBottomSpacing,
    this.priceTextStyle,
    this.oldPriceTextStyle,
    this.installmentsTextStyle,
    this.hasInstallmentsText,
    this.productFullPriceBuilder,
    this.productCurrentPriceBuilder,
    this.imageBorderRadius,
  });

  AzzasCmsSpotProductTheme copyWith({
    TextStyle? textPrimaryStyle,
    TextStyle? textSecondaryStyle,
    double? leftPaddingComponent,
    double? textLeftSpacing,
    double? textTopSpacing,
    double? topCallToActionPadding,
    TextStyle? callToActionTextStyle,
    Icon? callToActionIcon,
    double? containerHeight,
    TextStyle? productTitleStyle,
    double? productTitleBottomSpacing,
    TextStyle? priceTextStyle,
    TextStyle? oldPriceTextStyle,
    TextStyle? installmentsTextStyle,
    bool? hasInstallmentsText,
    AzzasSpotProductPriceBuilder? productFullPriceBuilder,
    AzzasSpotProductPriceBuilder? productCurrentPriceBuilder,
    BorderRadius? imageBorderRadius,
  }) {
    return AzzasCmsSpotProductTheme(
      textPrimaryStyle: textPrimaryStyle ?? this.textPrimaryStyle,
      textSecondaryStyle: textSecondaryStyle ?? this.textSecondaryStyle,
      leftPaddingComponent: leftPaddingComponent ?? this.leftPaddingComponent,
      textLeftSpacing: textLeftSpacing ?? this.textLeftSpacing,
      textTopSpacing: textTopSpacing ?? this.textTopSpacing,
      topCallToActionPadding:
          topCallToActionPadding ?? this.topCallToActionPadding,
      callToActionTextStyle:
          callToActionTextStyle ?? this.callToActionTextStyle,
      callToActionIcon: callToActionIcon ?? this.callToActionIcon,
      containerHeight: containerHeight ?? this.containerHeight,
      productTitleStyle: productTitleStyle ?? this.productTitleStyle,
      productTitleBottomSpacing:
          productTitleBottomSpacing ?? this.productTitleBottomSpacing,
      priceTextStyle: priceTextStyle ?? this.priceTextStyle,
      oldPriceTextStyle: oldPriceTextStyle ?? this.oldPriceTextStyle,
      installmentsTextStyle:
          installmentsTextStyle ?? this.installmentsTextStyle,
      hasInstallmentsText: hasInstallmentsText ?? this.hasInstallmentsText,
      productFullPriceBuilder:
          productFullPriceBuilder ?? this.productFullPriceBuilder,
      productCurrentPriceBuilder:
          productCurrentPriceBuilder ?? this.productCurrentPriceBuilder,
      imageBorderRadius: imageBorderRadius ?? this.imageBorderRadius,
    );
  }
}
