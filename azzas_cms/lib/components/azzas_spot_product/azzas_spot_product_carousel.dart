import 'dart:math';

import 'package:azzas_app_commons/azzas_app_commons.dart'
    show
        AnalyticsMetadataProvider,
        AzzasControllerCarrousel,
        EventDispatcher,
        Installment,
        IntelligentSearchUseCase,
        Modular,
        Product,
        Search,
        SearchProductsUseCase;
import 'package:azzas_cms/components/azzas_spot_product/azzas_spot_product.dart';
import 'package:azzas_cms/components/azzas_spot_product/azzas_spot_product_loading_skeleton.dart';
import 'package:azzas_cms/components/azzas_spot_product/utils/azzas_price_builder.dart';
import 'package:azzas_cms/widgets/azzas_sub_header.dart';
import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:visibility_detector/visibility_detector.dart';

class AzzasSpotProductCarousel extends StatefulWidget {
  final String? textPrimary, textSecondary, callToAction, textProductToBag;
  final String screenClass;

  final double? containerHeight,
      bottomPadding,
      topPadding,
      textLeftSpacing,
      textTopSpacing,
      imageWidth,
      imageHeight,
      leftPaddingComponent,
      bottomPaddingHeader,
      productTitleBottomSpacing,
      headerTopMargin,
      bottomSpacing;
  final double separatorWidth;

  final bool? noMargin,
      hasFavoriteButton,
      hasInstallmentsText,
      updateButton,
      linkToBag,
      isLoading,
      hasPlusButton,
      topPaddingHeader,
      showProductInformation,
      showControllerCarrousel;

  final bool isUpdatable,
      isPDPCarousel,
      ctaIsBottom,
      titleIsCenter,
      titleClickable,
      hideProductsIfEmpty,
      buildDynamicSpotSize,
      useImageWidth,
      ctaIsBelowHeader,
      useProductImageUrl,
      resizeImage,
      showDiscountTag,
      showProductsTag;

  final Color? textColor, backgroundColor;

  final TextStyle? textPrimaryStyle,
      textSecondaryStyle,
      priceTextStyle,
      oldPriceTextStyle,
      callToActionTextStyle,
      productTitleStyle,
      installmentsTextStyle,
      textToBagStyle;

  final Widget? clock;
  final Icon? callToActionIcon;

  final void Function(Product, int) onTapProduct;
  final void Function(Product, int)? onTapProductToBag;
  final void Function()? onTapClose, onTapTitle, onTapCallToAction;
  final void Function(Product)? onRemoveFromWishlist;

  final List<Product>? initialProducts;

  final Search search;
  final MainAxisAlignment? columnMainAxisAlignment;
  final SpotHeight? spotHeight;
  final EdgeInsets? paddingTextToBag;
  final IconData? addToBagIcon;
  final AzzasSpotProductPriceBuilder? productFullPriceBuilder,
      productCurrentPriceBuilder;
  final BorderRadius? imageBorderRadius;

  const AzzasSpotProductCarousel({
    super.key,
    required this.onTapProduct,
    required this.search,
    this.onTapProductToBag,
    this.noMargin = false,
    this.textPrimary,
    this.textSecondary,
    this.textPrimaryStyle,
    this.textSecondaryStyle,
    this.hasFavoriteButton,
    this.hasInstallmentsText,
    this.containerHeight,
    this.bottomPadding,
    this.topPadding,
    this.priceTextStyle,
    this.callToAction,
    this.updateButton = false,
    this.linkToBag = false,
    this.isUpdatable = false,
    this.ctaIsBottom = false,
    this.titleIsCenter = false,
    this.titleClickable = false,
    this.clock,
    this.textProductToBag,
    required this.screenClass,
    this.textColor,
    this.ctaIsBelowHeader = false,
    this.textLeftSpacing,
    this.textTopSpacing,
    this.isPDPCarousel = false,
    this.onTapClose,
    this.backgroundColor,
    this.imageWidth,
    this.imageHeight,
    this.onTapTitle,
    this.columnMainAxisAlignment,
    this.initialProducts,
    this.oldPriceTextStyle,
    this.isLoading,
    this.onRemoveFromWishlist,
    this.onTapCallToAction,
    this.hideProductsIfEmpty = true,
    this.buildDynamicSpotSize = false,
    this.hasPlusButton,
    this.useImageWidth = false,
    this.leftPaddingComponent,
    this.bottomPaddingHeader,
    this.topPaddingHeader,
    this.callToActionTextStyle,
    this.callToActionIcon,
    this.productTitleStyle,
    this.productTitleBottomSpacing,
    this.installmentsTextStyle,
    this.headerTopMargin,
    this.showProductInformation = true,
    this.showControllerCarrousel = true,
    this.bottomSpacing,
    this.spotHeight,
    this.paddingTextToBag,
    this.textToBagStyle,
    this.addToBagIcon,
    this.useProductImageUrl = false,
    this.productFullPriceBuilder,
    this.productCurrentPriceBuilder,
    this.imageBorderRadius,
    this.resizeImage = true,
    this.separatorWidth = 4.0,
    this.showDiscountTag = false,
    this.showProductsTag = false,
  });

  @override
  State<AzzasSpotProductCarousel> createState() =>
      _AzzasSpotProductCarouselState();
}

class _AzzasSpotProductCarouselState extends State<AzzasSpotProductCarousel> {
  bool isLoading = false;
  List<Product>? products;
  int activeItemIndex = 0;
  ScrollController productScrollController = ScrollController();
  final _eventDispatcher = Modular.get<EventDispatcher>();
  bool _hasDispatchedEvent = false;

  @override
  void initState() {
    super.initState();
    _searchProducts();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      productScrollController.addListener(() {
        int position = productScrollController.position.pixels ~/ (260.0);
        _updatePaginationPosition(position);
      });
    });
  }

  _updatePaginationPosition(int page) {
    setState(() {
      activeItemIndex = page;
    });
  }

  @override
  void didUpdateWidget(covariant AzzasSpotProductCarousel oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.search != widget.search ||
        oldWidget.initialProducts != widget.initialProducts) {
      _searchProducts();
    }
  }

  Future<void> _searchProductsReload() async {
    if (widget.search.filterCategoryOrCluster == null ||
        widget.search.filterCategoryOrCluster!.isEmpty) return;

    setState(() {
      isLoading = true;
    });
    try {
      List<Product> productsResultSet = [];

      late List<String> listCategoryOrCluster =
          widget.search.filterCategoryOrCluster!.split(',');

      final params = Search(
        filterCategoryOrCluster: listCategoryOrCluster[
            Random().nextInt(listCategoryOrCluster.length)],
        orderBy: widget.search.orderBy,
      );

      if (widget.search.getFilter.isEmpty) {
        final searchProducts = Modular.get<SearchProductsUseCase>();
        productsResultSet = await searchProducts.call(search: params);
      } else {
        final searchProducts = Modular.get<IntelligentSearchUseCase>();
        final searchResult = await searchProducts.call(search: params);
        productsResultSet = searchResult.products;
      }
      if (mounted) {
        setState(() {
          isLoading = false;
          products = productsResultSet;
        });
      }
    } catch (e) {
      debugPrint('Erro ao carregar produtos: $e');
      debugPrintStack();
    }
  }

  Future<void> _searchProducts() async {
    if (widget.initialProducts != null) {
      setState(() {
        products = widget.initialProducts;
      });

      return;
    }

    setState(() {
      isLoading = true;
    });
    try {
      List<Product> productsResultSet = [];
      if (widget.search.getFilter.isEmpty) {
        final searchProducts = Modular.get<SearchProductsUseCase>();
        productsResultSet = await searchProducts.call(search: widget.search);
      } else {
        final searchProducts = Modular.get<IntelligentSearchUseCase>();
        final searchResult = await searchProducts.call(search: widget.search);
        productsResultSet = searchResult.products;
      }
      if (mounted) {
        setState(() {
          isLoading = false;
          products = productsResultSet;
        });
      }
    } catch (e) {
      debugPrint('Erro ao carregar produtos: $e');
      debugPrintStack();
    }
  }

  @override
  Widget build(BuildContext context) {
    final analyticsData = AnalyticsMetadataProvider.of(context);

    if ((products?.isEmpty ?? false) && widget.hideProductsIfEmpty) {
      return const SizedBox.shrink();
    }

    return VisibilityDetector(
      key: Key('spot_product_carousel_${widget.hashCode}'),
      onVisibilityChanged: (visibilityInfo) {
        if (!mounted || _hasDispatchedEvent) return;
        if (visibilityInfo.visibleFraction > 0.9) {
          if (products?.isNotEmpty ?? false) {
            _eventDispatcher.logViewItemList(
              itemListName: widget.textPrimary ??
                  analyticsData['item_list_name'] as String? ??
                  widget.textSecondary,
              products: products ?? [],
            );
            _hasDispatchedEvent = true;
          }
        }
      },
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment:
            widget.columnMainAxisAlignment ?? MainAxisAlignment.start,
        children: [
          _buildHeader(),
          _buildBody(),
          if (widget.showControllerCarrousel ?? true)
            Padding(
              padding: EdgeInsets.only(
                top: widget.topPadding ?? 8,
                left: 24,
                bottom: 6,
              ),
              child: AzzasControllerCarrousel(
                activeItemIndex: activeItemIndex,
                itemsCount: products?.length ?? 0,
              ),
            ),
          if (widget.ctaIsBottom && widget.callToAction != null)
            Padding(
              padding: EdgeInsets.only(
                top: widget.topPadding ?? 4,
                left: 8,
                bottom: widget.bottomPadding ?? 6,
              ),
              child: _buildCallToAction(),
            ),
          SizedBox(height: widget.bottomSpacing ?? 24),
        ],
      ),
    );
  }

  Widget _buildBody() {
    if (widget.buildDynamicSpotSize == true) {
      const double descriptionHeight = 74.0;

      return Expanded(
        child: LayoutBuilder(
          builder: (_, constraints) {
            final dynamicHeight = constraints.maxHeight - descriptionHeight;
            final defaultAspectRatio = (widget.imageWidth ?? 271.0) /
                (widget.imageHeight ??
                    spotHeightsMap[SpotHeight.large] ??
                    spotHeightsMap[SpotHeight.medium]!);

            final dynamicWidth = dynamicHeight * defaultAspectRatio;

            return _buildBodyContainer(
              containerHeight: constraints.maxHeight,
              child: isLoading || widget.isLoading == true
                  ? _buildLoadingSkeleton(
                      imageWidth: dynamicWidth,
                      imageHeight: SpotHeight.large,
                      dynamicHeight: dynamicHeight,
                    )
                  : _buildProducts(
                      imageWidth: dynamicWidth,
                      imageHeight: dynamicHeight,
                    ),
            );
          },
        ),
      );
    }

    return _buildBodyContainer(
      containerHeight: widget.containerHeight ?? 495,
      child: isLoading || widget.isLoading == true
          ? _buildLoadingSkeleton(
              imageWidth: widget.imageWidth ?? 271.0,
              imageHeight: widget.spotHeight ?? SpotHeight.large,
              dynamicHeight: widget.imageHeight,
            )
          : _buildProducts(
              imageWidth: products!.length > 2 || widget.useImageWidth
                  ? (widget.imageWidth ?? 271.0)
                  : 271.0,
              imageHeight: widget.imageHeight,
              spotHeight: widget.spotHeight ?? SpotHeight.large,
            ),
    );
  }

  Widget _buildCallToAction() {
    return Padding(
      padding: EdgeInsets.only(
        top: widget.topPadding ?? 6,
        bottom: (widget.ctaIsBelowHeader ? 0 : 6),
      ),
      child: SizedBox(
        height: 40,
        child: GestureDetector(
          onTap: widget.onTapCallToAction ?? () {},
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Flexible(
                child: Text(
                  widget.callToAction!,
                  style: widget.callToActionTextStyle,
                ),
              ),
              const SizedBox(width: 4),
              widget.callToActionIcon ??
                  Icon(
                    Icons.arrow_forward_ios_rounded,
                    size: 10.0,
                    color: widget.textColor,
                  )
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      alignment: Alignment.topLeft,
      margin: EdgeInsets.only(
        top: widget.headerTopMargin ?? 24,
        bottom: widget.bottomPaddingHeader ?? widget.bottomPadding ?? 16,
        left: widget.leftPaddingComponent ?? 8,
      ),
      child: Column(
        crossAxisAlignment: widget.titleIsCenter
            ? CrossAxisAlignment.center
            : CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: GestureDetector(
                  onTap: () {
                    if (widget.titleClickable) {
                      widget.onTapTitle?.call();
                    }
                  },
                  child: AzzasSubHeader(
                    isCenter: widget.titleIsCenter,
                    onTap: widget.isUpdatable
                        ? () => _searchProductsReload()
                        : null,
                    textPrimary: widget.textPrimary,
                    styleTextPrimary: widget.textPrimaryStyle
                        ?.copyWith(color: widget.textColor),
                    textSecundary: widget.textSecondary,
                    styleTextSecond: widget.textSecondaryStyle
                        ?.copyWith(color: widget.textColor),
                    isTextPrimary: true,
                    isTextSecundary: true,
                    updateButton: widget.updateButton,
                  ),
                ),
              ),
              if (widget.isPDPCarousel)
                IconButton(
                  onPressed: widget.onTapClose,
                  icon: const Icon(
                    Icons.close,
                    size: 32.0,
                    color: Color(0xFF000000),
                  ),
                )
            ],
          ),
          if (widget.ctaIsBottom == false &&
              widget.ctaIsBelowHeader == true &&
              widget.callToAction != null)
            _buildCallToAction(),
          if (widget.clock != null) ...[
            GestureDetector(
              onTap: () {
                if (widget.titleClickable) {
                  widget.onTapTitle?.call();
                }
              },
              child: widget.clock,
            )
          ],
          if (widget.ctaIsBottom == false &&
              widget.ctaIsBelowHeader == false &&
              widget.callToAction != null)
            _buildCallToAction(),
        ],
      ),
    );
  }

  Widget _buildLoadingSkeleton({
    required double imageWidth,
    required SpotHeight imageHeight,
    double? dynamicHeight,
  }) {
    return ListView.separated(
      scrollDirection: Axis.horizontal,
      itemCount: 5,
      itemBuilder: (_, __) => AzzasSpotProductLoadingSkeleton(
        width: imageWidth,
        height: dynamicHeight,
        spotHeight: imageHeight,
      ),
      separatorBuilder: (context, index) => const SizedBox(width: 4),
    );
  }

  Widget _buildProducts({
    SpotHeight? spotHeight,
    double? imageHeight,
    required double imageWidth,
  }) {
    final analyticsData = AnalyticsMetadataProvider.of(context);
    return ListView.separated(
      cacheExtent: 100.0,
      padding:
          EdgeInsets.only(left: widget.leftPaddingComponent ?? 8, right: 24),
      itemCount: products!.length,
      scrollDirection: Axis.horizontal,
      controller: productScrollController,
      shrinkWrap: true,
      itemBuilder: (BuildContext context, int index) {
        Product product = products![index];
        product.updateProductHero(AzzasSpotProductCarousel);
        final uniqueHeroTag =
            '${product.productHeroID}_${widget.hashCode}_$index';

        String productImageUrl = '';
        if (widget.useProductImageUrl) {
          final image = product.items?.first.images
              ?.firstWhereOrNull((image) => image.imageLabel == "11");
          if (image != null && image.imageUrl?.isNotEmpty == true) {
            productImageUrl = image.imageUrl ??
                product.items?.first.images?.first.imageUrl ??
                '';
          }
        }

        return Container(
          color: widget.backgroundColor,
          child: AzzasCmsSpotProduct(
            showProductInformation: widget.showProductInformation ?? true,
            index: index,
            width: imageWidth,
            imageHeight: imageHeight,
            spotHeight: widget.spotHeight ?? SpotHeight.large,
            isPDPCarousel: widget.isPDPCarousel,
            heroTag: uniqueHeroTag,
            useProductImageUrl: widget.useProductImageUrl,
            productImageUrl: widget.useProductImageUrl
                ? productImageUrl
                : product.coverImage,
            productTitle: product.productName!,
            hasFavoriteButton: widget.hasFavoriteButton ?? false,
            onTap: () {
              _eventDispatcher.logSelectItem(
                itemListName: widget.textPrimary ??
                    widget.textSecondary ??
                    analyticsData['item_list_name'] as String? ??
                    '',
                product: product,
                index: index,
              );
              return widget.onTapProduct(product, index);
            },
            onTapProductToBag: widget.onTapProductToBag != null
                ? () => widget.onTapProductToBag!(product, index)
                : null,
            isOnSale: (product.isOnSale),
            productFullPrice: product.productFormattedListPrice,
            productCurrentPrice: product.productFormattedPrice,
            priceTextSize: (widget.priceTextStyle)?.fontSize,
            currentPriceTextColor: (widget.priceTextStyle)?.color,
            currentPriceTextStyle: widget.priceTextStyle,
            oldPriceTextStyle: widget.oldPriceTextStyle,
            oldPriceTextColor: (widget.oldPriceTextStyle)?.color,
            product: product,
            textLeftSpacing: widget.textLeftSpacing ?? 0,
            textTopSpacing: widget.textTopSpacing,
            textProductToBag: widget.textProductToBag,
            onRemoveFromWishlist: widget.onRemoveFromWishlist,
            hasPlusButton: widget.hasPlusButton ?? false,
            productTitleBottomSpacing: widget.productTitleBottomSpacing,
            productTitleStyle: widget.productTitleStyle,
            installmentsText: (widget.hasInstallmentsText ?? false)
                ? product.bestInstallment.asUserFriendlyString()
                : null,
            installmentsTextStyle: widget.installmentsTextStyle,
            paddingTextToBag: widget.paddingTextToBag,
            textToBagStyle: widget.textToBagStyle,
            addToBagIcon: widget.addToBagIcon,
            currentPriceBuilder: widget.productCurrentPriceBuilder,
            fullPriceBuilder: widget.productFullPriceBuilder,
            imageBorderRadius: widget.imageBorderRadius,
            resizeImage: widget.resizeImage,
            showDiscountTag: widget.showDiscountTag,
            showProductTags: widget.showProductsTag,
          ),
        );
      },
      separatorBuilder: (_, __) => SizedBox(width: widget.separatorWidth),
    );
  }

  Widget _buildBodyContainer({
    required Widget child,
    required double containerHeight,
  }) {
    return SizedBox(
      height: containerHeight,
      child: child,
    );
  }
}

extension on Installment {
  String asUserFriendlyString() {
    return 'em até ${numberOfInstallments}x';
  }
}
