import 'package:azzas_app_commons/azzas_app_commons.dart'
    show
        <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
        GetSimilarProductsUseCase,
        ImageHelper,
        Modular,
        Product,
        TextScaleHelper;
import 'package:azzas_app_commons/components/azzas_favorite_button.dart';
import 'package:azzas_cms/components/azzas_cached_network_image/azzas_cached_image.dart';
import 'package:azzas_cms/components/azzas_general_tags/azzas_product_tag/azzas_product_cms_tag.dart';
import 'package:azzas_cms/components/azzas_general_tags/azzas_stamp_tags.dart';
import 'package:azzas_cms/widgets/azzas_list_sku_pdc.dart';
import 'package:azzas_cms/widgets/azzas_tag.dart';
import 'package:flutter/material.dart';

const Map<String, int> vtexImageSize = {'width': 800, 'height': 1200};

enum SpotHeight { xsmall, small, medium, large, xlarge }

const Map<SpotHeight, double> spotHeightsMap = {
  SpotHeight.xsmall: 275.0,
  SpotHeight.small: 289.0,
  SpotHeight.medium: 331.0,
  SpotHeight.large: 418.0,
  SpotHeight.xlarge: 549.0,
};

const Map<SpotHeight, double> spotWidthsMap = {
  SpotHeight.xsmall: 178.0,
  SpotHeight.small: 187.0,
  SpotHeight.medium: 221.0,
  SpotHeight.large: 271.0,
  SpotHeight.xlarge: 375.0,
};

enum ButtonHeight { xLarge, large, medium, small, xSmall }

const Map buttonHeights = {
  ButtonHeight.xLarge: 72.0,
  ButtonHeight.large: 56.0,
  ButtonHeight.medium: 48.0,
  ButtonHeight.small: 40.0,
  ButtonHeight.xSmall: 32.0,
};

class AzzasCmsSpotProduct extends StatelessWidget {
  final int index;
  final SpotHeight spotHeight;
  final double? height,
      width,
      imageHeight,
      imageWidth,
      priceTextSize,
      textRightSpacing,
      productTitleBottomSpacing,
      textTopSpacing;
  final double textLeftSpacing;
  final String productTitle, heroTag, productImageUrl;
  final String? productFullPrice,
      productCurrentPrice,
      textProductToBag,
      favoriteAnimation,
      installmentsText;
  final Color? oldPriceTextColor,
      currentPriceTextColor,
      favoriteButtonBackgroundColor;
  final TextStyle? oldPriceTextStyle,
      currentPriceTextStyle,
      productTitleStyle,
      installmentsTextStyle,
      textToBagStyle;
  final EdgeInsets? paddingTextToBag;
  final IconData? addToBagIcon;
  final Product? product;
  final void Function() onTap;
  final void Function(Product)? onRemoveFromWishlist, onFavoriteCallback;
  final void Function()? onTapProductToBag;
  final Function(int)? onChangeImage;
  final bool isOnSale,
      isMiniature,
      hasFavoriteButton,
      isWishlistGridMode,
      isPDPCarousel,
      hasPlusButton,
      isSku,
      showDiscountTag,
      useProductImageUrl,
      resizeImage,
      haveCallToAction,
      hasFavoriteFeedbackText,
      showProductInformation,
      showProductTags;
  final ButtonHeight? favoriteButtonHeight;
  final BorderRadius? imageBorderRadius;
  final AzzasSpotProductPriceBuilder? fullPriceBuilder, currentPriceBuilder;

  AzzasCmsSpotProduct({
    super.key,
    required this.index,
    this.spotHeight = SpotHeight.medium,
    this.height,
    this.width,
    this.imageHeight,
    this.imageWidth,
    this.priceTextSize = 12.0,
    this.textLeftSpacing = 0,
    required this.heroTag,
    required this.productImageUrl,
    this.isOnSale = true,
    required this.productTitle,
    required this.onTap,
    this.productFullPrice,
    this.productCurrentPrice,
    this.oldPriceTextColor,
    this.currentPriceTextColor,
    this.oldPriceTextStyle,
    this.currentPriceTextStyle,
    this.textRightSpacing,
    this.textTopSpacing,
    this.haveCallToAction = false,
    this.product,
    this.favoriteAnimation,
    this.onRemoveFromWishlist,
    this.hasFavoriteFeedbackText = false,
    this.hasFavoriteButton = true,
    this.showProductInformation = true,
    this.isWishlistGridMode = false,
    this.installmentsText,
    this.isMiniature = false,
    this.onTapProductToBag,
    this.textProductToBag,
    this.isPDPCarousel = false,
    this.hasPlusButton = false,
    this.favoriteButtonBackgroundColor,
    this.favoriteButtonHeight,
    this.onChangeImage,
    this.isSku = false,
    this.onFavoriteCallback,
    this.productTitleStyle,
    this.productTitleBottomSpacing,
    this.installmentsTextStyle,
    this.paddingTextToBag,
    this.textToBagStyle,
    this.addToBagIcon,
    this.useProductImageUrl = false,
    this.fullPriceBuilder,
    this.currentPriceBuilder,
    this.imageBorderRadius,
    this.resizeImage = true,
    this.showDiscountTag = false,
    this.showProductTags = false,
  }) {
    assert(
      spotHeightsMap.containsKey(spotHeight) &&
          spotWidthsMap.containsKey(spotHeight),
      'A spotHeight informada não é suportada.',
    );
  }

  @override
  Widget build(BuildContext context) {
    final height = spotHeightsMap[spotHeight]!;
    final width = spotWidthsMap[spotHeight]!;

    final productImageWidth = imageWidth ?? this.width ?? width;
    final productImageHeight = imageHeight ?? height;
    final oldPriceTextColor = this.oldPriceTextColor;
    final currentPriceTextColor =
        isOnSale ? this.currentPriceTextColor : this.currentPriceTextColor;
    final textRightSpacing = this.textRightSpacing;

    return SizedBox(
      width: this.width ?? width,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Flexible(
            child: GestureDetector(
              onTap: onTap,
              child: Column(
                children: [
                  Stack(
                    children: <Widget>[
                      _ProductImage(
                        isPDPCarousel: isPDPCarousel,
                        heroTag: heroTag,
                        productImageHeight: productImageHeight,
                        productImageWidth: productImageWidth,
                        imageUrls: product?.images
                                .map((i) => i.imageUrl ?? "")
                                .toList() ??
                            [productImageUrl],
                        onChangeImage: onChangeImage,
                      ),
                      if (product != null) ...[
                        Positioned(
                          top: 8,
                          left: 0,
                          child: AzzasStampTags(
                            product: product!,
                          ),
                        ),
                        if (hasFavoriteButton)
                          Positioned(
                            top: 8,
                            right: 8,
                            child: AzzasFavoriteButton(
                              product: product!,
                              index: index,
                              onFavoriteCallback: onFavoriteCallback,
                            ),
                          ),
                      ],
                    ],
                  ),
                  if (product != null && (showProductInformation ?? true))
                    _ProductInformation(
                      isOnSale: isOnSale,
                      isWishlistGridMode: isWishlistGridMode,
                      productTitle: productTitle,
                      spotHeight: spotHeight,
                      textLeftSpacing: textLeftSpacing,
                      textRightSpacing: textRightSpacing ?? 4,
                      currentPriceTextColor: currentPriceTextColor,
                      currentPriceTextStyle: currentPriceTextStyle,
                      installmentsText: installmentsText,
                      oldPriceTextColor: oldPriceTextColor,
                      oldPriceTextStyle: oldPriceTextStyle,
                      priceTextSize: priceTextSize,
                      product: product!,
                      productCurrentPrice: productCurrentPrice,
                      productFullPrice: productFullPrice,
                      textTopSpacing: textTopSpacing,
                      productTitleStyle: productTitleStyle,
                      productTitleBottomSpacing: productTitleBottomSpacing,
                      installmentsTextStyle: installmentsTextStyle,
                      isSku: isSku,
                    ),
                ],
              ),
            ),
          ),
          if (onTapProductToBag != null && textProductToBag != null)
            Flexible(
              child: Padding(
                padding: paddingTextToBag ?? const EdgeInsets.only(top: 18.0),
                child: GestureDetector(
                  onTap: onTapProductToBag,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Flexible(
                        child: Text(
                          textProductToBag!,
                          style: textToBagStyle,
                        ),
                      ),
                      const SizedBox(width: 4),
                      Icon(
                        addToBagIcon ?? Icons.arrow_right,
                        size: 15.0,
                      )
                    ],
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}

class _ProductImage extends StatelessWidget {
  const _ProductImage({
    required this.isPDPCarousel,
    required this.heroTag,
    required this.productImageWidth,
    required this.productImageHeight,
    required this.imageUrls,
    this.onChangeImage,
    this.imageBorderRadius,
    this.resizeImage = true,
  });

  final bool isPDPCarousel, resizeImage;
  final String heroTag;
  final double productImageWidth, productImageHeight;
  final List<String> imageUrls;
  final Function(int)? onChangeImage;
  final BorderRadius? imageBorderRadius;

  Widget _buildImage(BuildContext context) {
    final gridInheritedWidget = GridDensityInheritedWidget.of(context);

    if (imageUrls.isEmpty) {
      return SizedBox(
        width: productImageWidth,
        height: productImageHeight,
      );
    }

    final imageUrl = resizeImageUrl(
      imageUrls.first,
      gridInheritedWidget?.gridDensity == 4,
      resizeImage,
    );

    if (isPDPCarousel) {
      return AzzasCachedNetworkingImage(
        imageUrl: imageUrl,
        width: productImageWidth,
        height: productImageHeight,
      );
    }
    if (gridInheritedWidget?.gridDensity == null) {
      return Hero(
        tag: heroTag,
        child: AzzasCachedNetworkingImage(
          imageUrl: imageUrl,
          width: productImageWidth,
          height: productImageHeight,
        ),
      );
    }

    return ImageSlider(
      showAnimation: false,
      imageUrls: imageUrls,
      heroTag: heroTag,
      productImageHeight: productImageHeight,
      productImageWidth: productImageWidth,
      onChangeImage: onChangeImage,
    );
  }

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: imageBorderRadius ?? BorderRadius.zero,
      child: _buildImage(context),
    );
  }
}

class _ProductInformation extends StatelessWidget {
  const _ProductInformation({
    required this.textLeftSpacing,
    required this.textRightSpacing,
    required this.product,
    required this.spotHeight,
    required this.isWishlistGridMode,
    this.textTopSpacing,
    required this.productTitle,
    required this.isOnSale,
    this.productFullPrice,
    this.oldPriceTextStyle,
    this.priceTextSize,
    this.oldPriceTextColor,
    this.productCurrentPrice,
    this.currentPriceTextStyle,
    this.currentPriceTextColor,
    this.installmentsText,
    this.productTitleStyle,
    this.productTitleBottomSpacing,
    this.installmentsTextStyle,
    required this.isSku,
    this.fullPriceBuilder,
    this.currentPriceBuilder,
    this.showDiscountTag = false,
    this.showProductTags = false,
  });

  final double textLeftSpacing;
  final double textRightSpacing;
  final Product product;
  final SpotHeight spotHeight;
  final bool isWishlistGridMode;
  final double? textTopSpacing;
  final String productTitle;
  final bool isOnSale;
  final String? productFullPrice;
  final TextStyle? oldPriceTextStyle;
  final double? priceTextSize;
  final Color? oldPriceTextColor;
  final String? productCurrentPrice;
  final TextStyle? currentPriceTextStyle;
  final Color? currentPriceTextColor;
  final String? installmentsText;
  final TextStyle? installmentsTextStyle;
  final bool isSku;
  final TextStyle? productTitleStyle;
  final double? productTitleBottomSpacing;
  final AzzasSpotProductPriceBuilder? fullPriceBuilder, currentPriceBuilder;
  final bool showDiscountTag, showProductTags;

  @override
  Widget build(BuildContext context) {
    final gridInheritedWidget = GridDensityInheritedWidget.of(context);
    final zeroPrice = CurrencyHelper.format(amount: 0);
    final hasFullPrice =
        productFullPrice != null && productFullPrice != zeroPrice;
    final hasCurrentPrice =
        productCurrentPrice != null && productCurrentPrice != zeroPrice;

    return Padding(
      // padding: const EdgeInsets.only(left: 12, right: 20),
      padding: EdgeInsets.only(left: textLeftSpacing, right: textRightSpacing),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.max,
        children: [
          if (isWishlistGridMode) ...[
            _ProductAvailableTag(product),
            _ProductLowStockTag(product),
          ],
          Padding(
            padding: isWishlistGridMode
                ? EdgeInsets.zero
                : EdgeInsets.only(
                    top: textTopSpacing ?? 4,
                    bottom: productTitleBottomSpacing ?? 0,
                  ),
            child: Align(
              alignment: Alignment.centerLeft,
              child: Text(
                productTitle,
                overflow: TextOverflow.ellipsis,
                textScaler: TextScaleHelper.clampTextScale(context,
                    maxScaleFactor: 1.50),
                softWrap: true,
                style: productTitleStyle ?? const TextStyle(fontSize: 12),
              ),
            ),
          ),
          Wrap(
            crossAxisAlignment: showDiscountTag
                ? WrapCrossAlignment.center
                : WrapCrossAlignment.start,
            spacing: 4.0,
            children: [
              if (isOnSale && hasFullPrice)
                _ProductFullPrice(
                  productFullPrice: productFullPrice,
                  oldPriceTextColor: oldPriceTextColor,
                  oldPriceTextStyle: oldPriceTextStyle,
                  priceTextSize: priceTextSize,
                  builder: fullPriceBuilder,
                ),
              if (hasCurrentPrice)
                _ProductCurrentPrice(
                  productCurrentPrice: productCurrentPrice,
                  currentPriceTextColor: currentPriceTextColor,
                  currentPriceTextStyle: currentPriceTextStyle,
                  priceTextSize: priceTextSize,
                  builder: currentPriceBuilder,
                ),
              if (showDiscountTag) ...[
                SizedBox(
                  width: 4.0,
                ),
                AzzasDiscountTag(
                  product: product,
                )
              ]
            ],
          ),
          Padding(
            padding: const EdgeInsets.only(top: 4, bottom: 8),
            child: AzzasProductsCmsTags(
              product: product,
              isPLP: true,
            ),
          ),
          if (installmentsText != null)
            Text(
              installmentsText!,
              style: installmentsTextStyle,
            ),
          if (isSku)
            ListSku(
              product: product,
              gridType: gridInheritedWidget?.gridDensity ?? 0,
            ),
        ],
      ),
    );
  }
}

class _ProductFullPrice extends StatelessWidget {
  const _ProductFullPrice({
    this.productFullPrice,
    this.oldPriceTextStyle,
    this.priceTextSize,
    this.oldPriceTextColor,
    this.builder,
  });

  final String? productFullPrice;
  final TextStyle? oldPriceTextStyle;
  final double? priceTextSize;
  final Color? oldPriceTextColor;
  final AzzasSpotProductPriceBuilder? builder;

  @override
  Widget build(BuildContext context) {
    return builder?.call(context, productFullPrice!) ??
        Text(
          productFullPrice!,
          style: TextStyle(
            fontSize: 10,
            height: oldPriceTextStyle?.height ?? 1.33,
            color: const Color(0xFF666666),
            decoration: TextDecoration.lineThrough,
          ),
        );
  }
}

class _ProductCurrentPrice extends StatelessWidget {
  const _ProductCurrentPrice({
    this.productCurrentPrice,
    this.currentPriceTextStyle,
    this.priceTextSize,
    this.currentPriceTextColor,
    this.builder,
  });

  final String? productCurrentPrice;
  final TextStyle? currentPriceTextStyle;
  final double? priceTextSize;
  final Color? currentPriceTextColor;
  final AzzasSpotProductPriceBuilder? builder;

  @override
  Widget build(BuildContext context) {
    return builder?.call(context, productCurrentPrice!) ??
        Text(
          productCurrentPrice!,
          style: currentPriceTextStyle ??
              TextStyle(
                fontSize: 10,
                height: currentPriceTextStyle?.height ?? 1.33,
                color: currentPriceTextColor ?? const Color(0xFF000000),
              ),
        );
  }
}

class _ProductAvailableTag extends StatelessWidget {
  const _ProductAvailableTag(this.product);

  final Product product;

  @override
  Widget build(BuildContext context) {
    final isProductAvailable = product.isAvailable;
    const lowStockTagTextColor = Colors.black;
    const lowStockTagColor = Colors.white;

    if (!isProductAvailable) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 4),
          CmsAzzasTag.stock(
            margin: EdgeInsets.zero,
            text: 'sem estoque',
            textColor: lowStockTagTextColor,
            backgroundColor: lowStockTagColor,
          ),
          const SizedBox(height: 4),
        ],
      );
    }
    return const SizedBox.shrink();
  }
}

class _ProductLowStockTag extends StatelessWidget {
  const _ProductLowStockTag(this.product);

  final Product product;

  @override
  Widget build(BuildContext context) {
    const lowStockTagTextColor = Colors.black;
    const lowStockTagColor = Colors.white;

    if (product.isLowStock) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 4),
          CmsAzzasTag.stock(
            margin: EdgeInsets.zero,
            text: "Low stock",
            textColor: lowStockTagTextColor,
            backgroundColor: lowStockTagColor,
          ),
        ],
      );
    }
    return const SizedBox(height: 4);
  }
}

class ImageSlider extends StatefulWidget {
  const ImageSlider({
    super.key,
    required this.imageUrls,
    required this.showAnimation,
    required this.heroTag,
    required this.productImageWidth,
    required this.productImageHeight,
    this.onChangeImage,
  });

  final String heroTag;
  final double productImageWidth;
  final double productImageHeight;
  final List<String> imageUrls;
  final bool showAnimation;
  final Function(int)? onChangeImage;

  @override
  State<ImageSlider> createState() => _ImageSliderState();
}

class _ImageSliderState extends State<ImageSlider>
    with TickerProviderStateMixin {
  PageController? _pageController;
  AnimationController? _animationController;

  @override
  void dispose() {
    _pageController?.dispose();
    _animationController?.dispose();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    _pageController = PageController();

    if (widget.showAnimation) {
      _animateScroll();
    }
  }

  @override
  void didUpdateWidget(covariant ImageSlider oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.showAnimation != oldWidget.showAnimation) {
      if (mounted && widget.showAnimation) {
        _animateScroll();
      }
    }
  }

  void _animateScroll() {
    if (!widget.showAnimation || !mounted || _pageController == null) return;

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _animationController = AnimationController(
        duration: const Duration(seconds: 1),
        vsync: this,
      );

      final double pageWidth = widget.productImageWidth;
      final double offset = pageWidth * 0.5;
      const Duration animationDuration = Duration(milliseconds: 500);

      _animationController!.forward().whenComplete(() {
        if (_pageController?.hasClients ?? false) {
          _pageController!.position
              .animateTo(
            _pageController!.offset + offset,
            duration: animationDuration,
            curve: Curves.easeInOut,
          )
              .whenComplete(() {
            if (_pageController?.hasClients ?? false) {
              _pageController!.position
                  .animateTo(
                _pageController!.offset - offset,
                duration: animationDuration,
                curve: Curves.easeInOut,
              )
                  .whenComplete(() {
                if (_pageController?.hasClients ?? false) {
                  _pageController!.jumpToPage(0);
                }
              });
            }
          });
        }
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    final gridInheritedWidget = GridDensityInheritedWidget.of(context);
    return SizedBox(
      width: widget.productImageWidth,
      height: widget.productImageHeight,
      child: PageView.builder(
        controller: _pageController,
        scrollDirection: Axis.horizontal,
        itemCount: widget.imageUrls.length,
        pageSnapping: true,
        onPageChanged: (index) => {
          if (widget.onChangeImage != null) {widget.onChangeImage!(index)}
        },
        itemBuilder: (context, index) {
          final imageUrl = resizeImageUrl(
            widget.imageUrls[index],
            gridInheritedWidget?.gridDensity == 4,
            true,
          );
          return Hero(
            tag: widget.heroTag,
            child: AzzasCachedNetworkingImage(
              imageUrl: imageUrl,
              width: widget.productImageWidth,
              height: widget.productImageHeight,
            ),
          );
        },
      ),
    );
  }
}

String resizeImageUrl(String image, bool isMiniature, bool resizeVtexImage) {
  if (isMiniature) {
    return ImageHelper.resizeVtexImage(
      imageUrl: image,
      vtexWidth: 0,
      vtexHeight: 600,
      position: 4,
    );
  }
  return resizeVtexImage
      ? ImageHelper.resizeVtexImage(
          imageUrl: image,
          vtexWidth: vtexImageSize['width']!,
          vtexHeight: vtexImageSize['height']!,
          position: 4,
        )
      : image;
}

class ListSku extends StatefulWidget {
  final Product product;
  final int gridType;

  const ListSku({
    super.key,
    required this.product,
    required this.gridType,
  });

  @override
  State<StatefulWidget> createState() => _ListSkus();
}

class _ListSkus extends State<ListSku> {
  List<Product> _similarProducts = [];
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    _fetchSimilarProducts();
  }

  @override
  void didUpdateWidget(covariant ListSku oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.gridType != widget.gridType && mounted) {
      _fetchSimilarProducts();
    }
  }

  Future<void> _fetchSimilarProducts() async {
    final getSimularProducts = Modular.get<GetSimilarProductsUseCase>();

    if (widget.product.productId == null) return;

    try {
      List<Product> similarProducts = await getSimularProducts.call(
          productId: int.parse(widget.product.productId!));
      similarProducts.insert(0, widget.product);
      setState(() {
        _similarProducts = similarProducts;
        isLoading = false;
      });
    } catch (e) {
      debugPrint('Erro ao buscar produtos similares: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return AzzasListSkuPdc(
      product: _similarProducts,
      gridType: widget.gridType,
    );
  }
}

class GridDensityInheritedWidget extends InheritedWidget {
  final int gridDensity;
  final bool? isOnTop;

  const GridDensityInheritedWidget({
    super.key,
    required this.gridDensity,
    this.isOnTop = false,
    required super.child,
  });

  static GridDensityInheritedWidget? of(BuildContext context) {
    return context
        .dependOnInheritedWidgetOfExactType<GridDensityInheritedWidget>();
  }

  @override
  bool updateShouldNotify(GridDensityInheritedWidget oldWidget) {
    return (oldWidget.gridDensity != gridDensity ||
        oldWidget.isOnTop != isOnTop);
  }
}
