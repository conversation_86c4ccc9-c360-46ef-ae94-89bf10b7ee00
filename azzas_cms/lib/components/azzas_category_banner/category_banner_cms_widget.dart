import 'package:azzas_app_commons/utils/navigator_dynamic.dart';
import 'package:azzas_cms/models/cms_media.dart';
import 'package:azzas_cms/components/azzas_accordion_menu/accordion_menu_cms_component.dart';
import 'package:azzas_cms/components/azzas_category_banner/category_banner_cms_component.dart';
import 'package:azzas_cms/components/azzas_banner_tv/azzas_banner_tv.dart';
import 'package:azzas_cms/components/azzas_category_banner/azzas_category_banner.dart';
import 'package:azzas_cms/utils/azzas_cms_content_metadata_provider.dart';
import 'package:azzas_cms/utils/azzas_cms_content_spacer.dart';
import 'package:flutter/material.dart';
import 'package:visibility_detector/visibility_detector.dart';

typedef CategoryBannerCallback = Future<void> Function(
    CategoryBannerCmsComponent component);

class CategoryBannerCmsWidget extends StatefulWidget {
  final CategoryBannerCmsComponent component;
  final CategoryBannerCallback? onItemSelected;
  final CategoryBannerCallback? onVisible;

  const CategoryBannerCmsWidget(
    this.component, {
    super.key,
    this.onItemSelected,
    this.onVisible,
  });


  factory CategoryBannerCmsWidget.fromComponent(
    CategoryBannerCmsComponent component, {
    Widget Function(List<String>)? bottomSheetBuilder,
    CategoryBannerCallback? onVisible,
    CategoryBannerCallback? onItemSelected,
  }) {
    return CategoryBannerCmsWidget(
      component,
      onItemSelected: onItemSelected,
      onVisible: onVisible,
    );
  }

  @override
  State<CategoryBannerCmsWidget> createState() =>
      _CategoryBannerCmsWidgetState();
}

class _CategoryBannerCmsWidgetState extends State<CategoryBannerCmsWidget> {
  @override
  Widget build(BuildContext context) {
    final cmsMediaType = widget.component.media.data.attributes.type;
    return AzzasCmsContentSpacer(
      shouldHaveSpacing: (context) {
        final metadata = AzzasCmsContentMetadata.of(context);
        if (metadata == null) return false;
        return metadata.previousComponent is! AccordionMenuCmsComponent &&
            metadata.previousComponent is! CategoryBannerCmsComponent;
      },
      child: VisibilityDetector(
        key: Key('category_banner${widget.component.bannerNameGA4}'),
        onVisibilityChanged: (info) {
          if (info.visibleFraction >= 0.8 && widget.onVisible != null) {
            widget.onVisible!(widget.component);
          }
        },
        child: AzzasCategoryBanner(
          bannerTitle: widget.component.bannerTitle,
          bannerSubtitle: widget.component.bannerSubtitle,
          titleColor: widget.component.titleColor,
          subtitleColor: widget.component.subtitleColor,
          linkText: widget.component.linkText,
          linkTextColor: widget.component.linkTextColor,
          media: Media(
            altText: widget.component.bannerNameGA4.value,
            mediaType: cmsMediaType.asMediaType(),
            mediaUrl: widget.component.media.data.attributes.url,
            onTap: () => _onTap(context),
          ),
          hasBottomSpacing: widget.component.hasBottomSpacing ?? false,
          linkOnTap: () => _onTap(context),
          description: widget.component.description,
          hasBorder: widget.component.hasBorder,
          videoUrl: widget.component.videoUrl,
        ),
      ),
    );
  }

  void _onTap(BuildContext context) async {
    debugPrint("CategoryBannerCmsWidget tapped");
    if (widget.onItemSelected != null) {
      debugPrint("Chamando onItemSelected callback");
      await widget.onItemSelected!(widget.component);
    }
    debugPrint(
        "Chamando NavigatorDynamic com linkOnTap: ${widget.component.linkOnTap}");
    NavigatorDynamic.call(widget.component.linkOnTap);
  }
}

extension on CmsMediaType {
  MediaType asMediaType() {
    return switch (this) {
      CmsMediaType.image => MediaType.image,
      CmsMediaType.video => MediaType.video,
    };
  }
}
