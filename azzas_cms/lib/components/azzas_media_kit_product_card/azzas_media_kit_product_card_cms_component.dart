import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:flutter/material.dart';

abstract class InterleavableComponent {
  int get getShowBeforeProductAt;
  int get getSize;
}

class MediaKiProductCardCmsComponent extends CmsComponent
    implements InterleavableComponent {
  @override
  int get getShowBeforeProductAt => showBeforeProductAt;

  @override
  int get getSize => size;

  const MediaKiProductCardCmsComponent({
    required super.id,
    required super.componentType,
    required this.showBeforeProductAt,
    required this.size,
    required this.media,
    required this.navigateTo,
    this.productIdList,
    this.cta,
    this.textColor,
    this.backgroundColor,
    this.componentBackgroundColor,
    this.bannerNameGA4,
  });

  final CmsMedia media;
  final String? navigateTo;
  final List<String>? productIdList;
  final int showBeforeProductAt;
  final int size;
  final String? cta;
  final Color? textColor;
  final Color? backgroundColor;
  final Color? componentBackgroundColor;
  final BannerNameGA4? bannerNameGA4;

  static List<String> getProductIdList(String? productIds) {
    if (productIds == null) return [];
    return productIds.split(";").map((e) => e.trim()).toList();
  }

  factory MediaKiProductCardCmsComponent.fromJson(Map<String, dynamic> json) {
    final productIds = getProductIdList(json['product_ids']);
    return MediaKiProductCardCmsComponent(
      id: json['id'],
      componentType: json['__component'],
      productIdList: productIds,
      showBeforeProductAt: json['show_before_product_at'],
      size: json['size'],
      media: CmsMedia.fromJson(json['media'] ?? json['image']),
      navigateTo: json['navigate_to'],
      cta: json['cta_text'],
      textColor:
          ColorUtils.parseStrapiColor(json['text_color']) ?? Colors.white,
      backgroundColor:
          ColorUtils.parseStrapiColor(json['background_color']) ?? Colors.black,
      componentBackgroundColor:
          ColorUtils.parseStrapiColor(json['component_background_color']),
      bannerNameGA4: json['banner_name_ga4'] is String
          ? BannerNameGA4.fromString(json['banner_name_ga4'])
          : null,
    );
  }
}
