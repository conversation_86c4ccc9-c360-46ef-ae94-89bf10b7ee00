import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:flutter/material.dart';
import 'package:visibility_detector/visibility_detector.dart';

class AzzasMediaKitProductCardCmsWidget extends StatefulWidget {
  final String imageUrl;
  final String? navigateTo;
  final int? position;
  final String? cta;
  final List<String>? productIdList;
  final Color? textColor;
  final Color? backgroundColor;
  final BannerNameGA4? bannerNameGA4;

  /// Sobrescreve método executado ao clicar no botão
  final VoidCallback? onButtonPressed;

  /// Disparado ao clicar na imagem (já contem redirecionamento)
  final VoidCallback? onTap;

  /// Método executado quando o componente está visivel
  final VoidCallback? onVisibility;

  const AzzasMediaKitProductCardCmsWidget({
    super.key,
    required this.imageUrl,
    this.navigateTo,
    this.position,
    this.cta,
    this.productIdList,
    this.textColor,
    this.backgroundColor,
    this.onButtonPressed,
    this.onTap,
    this.bannerNameGA4,
    this.onVisibility,
  });

  AzzasMediaKitProductCardCmsWidget.fromCmsComponent(
      MediaKiProductCardCmsComponent component, int? index,
      {Key? key,
      VoidCallback? onPressed,
      VoidCallback? onVisibility,
      VoidCallback? onTap})
      : this(
          key: key,
          imageUrl: component.media.data.attributes.url,
          navigateTo: component.navigateTo,
          position: index,
          cta: component.cta,
          productIdList: component.productIdList,
          textColor: component.textColor,
          backgroundColor: component.backgroundColor,
          onButtonPressed: onPressed,
          onTap: onTap,
          bannerNameGA4: component.bannerNameGA4,
          onVisibility: onVisibility,
        );

  @override
  State<AzzasMediaKitProductCardCmsWidget> createState() =>
      _AzzasMediaKitProductCardCmsWidgetState();
}

class _AzzasMediaKitProductCardCmsWidgetState
    extends State<AzzasMediaKitProductCardCmsWidget> {
  bool isLoading = false;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        return VisibilityDetector(
          key: Key('media_kit_${widget.imageUrl}'),
          onVisibilityChanged: (info) {
            if (info.visibleFraction >= 0.8 && widget.onVisibility != null) {
              widget.onVisibility!();
            }
          },
          child: Stack(
            children: [
              AzzasCachedNetworkingImage(
                imageUrl: widget.imageUrl,
                width: double.infinity,
                height: constraints.hasBoundedHeight ? double.infinity : null,
              ),
              isLoading ? _buildLoadingOverlay() : _buildInkWell(),
              if (widget.cta != null &&
                  (widget.productIdList?.isNotEmpty ?? false)) ...[
                Positioned(
                  bottom: 24,
                  left: 0,
                  right: 0,
                  child: Align(
                    alignment: Alignment.bottomCenter,
                    child: AzzasPrimaryButton(
                      style: AzzasButtonStyle(
                        backgroundColor: widget.backgroundColor,
                        borderColor: widget.backgroundColor,
                      ),
                      size: ButtonSize.small,
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            widget.cta!,
                            style: TextStyle(
                              color: widget.textColor,
                            ),
                          ),
                          SizedBox(
                            width: 8.0,
                          ),
                          Icon(
                            Icons.arrow_forward_ios,
                            size: 12.0,
                            color: widget.textColor,
                          )
                        ],
                      ),
                      onPressed: () {
                        if (widget.onButtonPressed != null) {
                          widget.onButtonPressed!();
                        } else {
                          Modular.to.pushNamed('/buy_look_page',
                              arguments: widget.productIdList);
                        }
                      },
                    ),
                  ),
                )
              ]
            ],
          ),
        );
      },
    );
  }

  Widget _buildLoadingOverlay() {
    return Positioned.fill(
      child: ColoredBox(
        color: Colors.black.withOpacity(0.5),
        child: const Center(
          child: AzzasSpinner(),
        ),
      ),
    );
  }

  Widget _buildInkWell() {
    return Positioned.fill(
      child: Material(
        color: Colors.transparent,
        child: widget.navigateTo.isNotNullOrEmpty
            ? InkWell(
                onTap: () {
                  if (widget.onTap != null) widget.onTap!();
                  NavigatorDynamic.call(widget.navigateTo!);
                },
              )
            : const SizedBox.shrink(),
      ),
    );
  }
}
