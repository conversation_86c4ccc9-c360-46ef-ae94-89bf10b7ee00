import 'package:azzas_app_commons/azzas_app_commons.dart';

import 'package:flutter/material.dart';
import 'package:visibility_detector/visibility_detector.dart';

/// *SimpleBodyClockCmsWidget* é um reloginho que deverá ser exibido principalmente
/// no corpo da Home. Caso procure pelo reloginho exibido no topo da tela,
/// seja na Home, PDP ou PDC, procure por [AzzasHeaderClock].
class SimpleBodyClockCmsWidget extends StatefulWidget {
  final bool isActive;
  final String? title;
  final String? subtitle;
  final String? rulesButtonText;
  final String? rulesDescription;
  final String? buttonText;
  final String? coupon;
  final Color backgroundColor;
  final Color textColor;
  final VoidCallback? onTapButton;
  final DateTime startTime;
  final DateTime endTime;
  final AzzasClockButtonPosition buttonPosition;
  final BannerNameGA4? bannerNameGA4;
  final Function(BannerNameGA4? component)? onVisible;
  final Function(BannerNameGA4? component)? onTap;

  const SimpleBodyClockCmsWidget({
    super.key,
    required this.isActive,
    required this.title,
    required this.subtitle,
    this.rulesButtonText,
    this.rulesDescription,
    required this.buttonText,
    required this.coupon,
    required this.backgroundColor,
    required this.textColor,
    this.onTapButton,
    required this.startTime,
    required this.endTime,
    required this.onVisible,
    required this.onTap,
    required this.bannerNameGA4,
    this.buttonPosition = AzzasClockButtonPosition.afterCoupon,
  });

  @override
  State<StatefulWidget> createState() => _SimpleClockCmsWidgetState();

  factory SimpleBodyClockCmsWidget.fromComponent(
    SimpleBodyClockCmsComponent component, {
    Function(BannerNameGA4? bannerNameGA4)? onVisible,
    Function(BannerNameGA4? bannerNameGA4)? onTap,
  }) {
    return SimpleBodyClockCmsWidget(
      isActive: component.isActive,
      title: component.title,
      subtitle: component.subtitle,
      rulesButtonText: component.rulesButtonText,
      rulesDescription: component.rulesDescription,
      buttonText: component.buttonText,
      coupon: component.coupon,
      backgroundColor: component.backgroundColor,
      textColor: component.textColor,
      startTime: component.startTime,
      endTime: component.endTime,
      bannerNameGA4: component.bannerNameGA4,
      onVisible: onVisible,
      onTap: onTap,
      buttonPosition:
          AzzasClockButtonPosition.fromString(component.buttonPosition),
    );
  }
}

class _SimpleClockCmsWidgetState extends State<SimpleBodyClockCmsWidget> {
  final checkoutHandler = Modular.get<OrderCheckoutHandler>();
  bool _isCouponActive = false;

  @override
  void initState() {
    super.initState();
    _verifyIsCouponActive();
  }

  void _verifyIsCouponActive() {
    final appliedCoupon = checkoutHandler.getAppliedCoupon();
    setState(() {
      _isCouponActive = (appliedCoupon == widget.coupon);
    });
  }

  Future<void> _onTapCoupon() async {
    if (widget.coupon == null || widget.coupon!.isEmpty) return;

    String? appliedCoupon = checkoutHandler.getAppliedCoupon();
    if (appliedCoupon == widget.coupon) {
      setState(() {
        _isCouponActive = true;
      });

      return;
    }

    try {
      await checkoutHandler.addCoupon(coupon: widget.coupon!);
      appliedCoupon = checkoutHandler.getAppliedCoupon();
      setState(() {
        _isCouponActive = (appliedCoupon == widget.coupon);
      });
    } catch (e) {
      setState(() {
        _isCouponActive = false;
      });
    }
  }

  String _buttonText() {
    if (_isCouponActive) {
      return 'Aplicado';
    }
    return widget.buttonText ?? 'Aplicar cupom';
  }

  @override
  Widget build(BuildContext context) {
    return AzzasCmsContentSpacer(
      child: VisibilityDetector(
        key: Key('clock_body_${widget.bannerNameGA4}'),
        onVisibilityChanged: (info) {
          if (info.visibleFraction >= 0.8 && widget.onVisible != null) {
            widget.onVisible!(widget.bannerNameGA4);
          }
        },
        child: AzzasSimpleBodyClock(
          isActive: widget.isActive,
          title: widget.title,
          subtitle: widget.subtitle,
          rulesButtonText: widget.rulesButtonText,
          rulesDescription: widget.rulesDescription,
          buttonText: _buttonText(),
          coupon: widget.coupon,
          backgroundColor: widget.backgroundColor,
          textColor: widget.textColor,
          onTapButton: () async {
           
            await _onTapCoupon();
          },
          startTime: widget.startTime,
          endTime: widget.endTime,
          isButtonSelected: _isCouponActive,
          buttonPosition: widget.buttonPosition,
        ),
      ),
    );
  }
}
