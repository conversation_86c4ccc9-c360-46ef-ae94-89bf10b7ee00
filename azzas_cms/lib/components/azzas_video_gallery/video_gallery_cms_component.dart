import 'package:azzas_cms/azzas_cms.dart';
import 'package:azzas_cms/models/cms_component.dart';
import 'package:azzas_cms/models/cms_media.dart';

class VideoGalleryCmsComponent extends CmsComponent {
  const VideoGalleryCmsComponent({
    required super.id,
    required super.componentType,
    this.title,
    this.subtitle,
    required this.items,
  });

  factory VideoGalleryCmsComponent.fromJson(Map<String, dynamic> json) {
    return VideoGalleryCmsComponent(
      id: json['id'],
      componentType: json['__component'],
      title: json['title'],
      subtitle: json['subtitle'],
      items: (json['items'] as List)
          .map((j) => VideoGalleryCmsComponentItem.fromJson(j))
          .toList(),
     
    );
  }
 
  final String? title;
  final String? subtitle;
  final List<VideoGalleryCmsComponentItem> items;
}

class VideoGalleryCmsComponentItem {
  const VideoGalleryCmsComponentItem({
    required this.id,
    required this.image,
    required this.videoUrl,
    required this.title,
    required this.description,
    required this.navigateTo,
    required this.bannerNameGA4,
  });

  factory VideoGalleryCmsComponentItem.fromJson(Map<String, dynamic> json) {
    return VideoGalleryCmsComponentItem(
      id: json['id'],
      image: CmsMedia.fromJson(json['image']),
      videoUrl: json['video_url'],
      title: json['title'],
      description: json['description'],
      navigateTo: json['navigate_to'],
      bannerNameGA4: json['banner_name_ga4'] is String
          ? BannerNameGA4.fromString(json['banner_name_ga4'])
          : null,
    );
  }

  final int id;
  final CmsMedia? image;
  final String? videoUrl;
  final String? title;
  final String? description;
  final String? navigateTo;
  final BannerNameGA4? bannerNameGA4;
}
