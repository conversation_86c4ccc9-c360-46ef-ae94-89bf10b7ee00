import 'package:azzas_cms/azzas_cms.dart';
import 'package:azzas_cms/extensions/list_extensions.dart';
import 'package:azzas_cms/models/cms_component.dart';
import 'package:azzas_cms/models/cms_media.dart';
import 'package:azzas_cms/utils/color_utils.dart';
import 'package:flutter/material.dart';

class GridCategoryListingCmsComponent extends CmsComponent {
  const GridCategoryListingCmsComponent({
    required super.id,
    required super.componentType,
    this.title,
    this.subtitle,
    this.callToAction,
    this.navigateTo,
    this.backgroundColor,
    required this.items,
  });

  factory GridCategoryListingCmsComponent.fromJson(Map<String, dynamic> json) {
    return GridCategoryListingCmsComponent(
      id: json['id'],
      componentType: json['__component'],
      title: json['title'],
      subtitle: json['subtitle'],
      callToAction: json['call_to_action'],
      navigateTo: json['navigate_to'],
      backgroundColor:
          ColorUtils.parseStrapiColor(json['background']) ?? Colors.white,
      items: (json['items'] as List)
          .tryMap((e) => ItemGridCategory.fromJson(e))
          .toList(),
    );
  }

  final String? title;
  final String? subtitle;
  final String? callToAction;
  final String? navigateTo;
  final Color? backgroundColor;
  final List<ItemGridCategory> items;
}

class ItemGridCategory {
  ItemGridCategory({
    required this.id,
    required this.media,
    required this.navigateTo,
    required this.bannerNameGA4,
  });

  factory ItemGridCategory.fromJson(Map<String, dynamic> json) {
    return ItemGridCategory(
      id: json['id'],
      media: CmsMedia.fromJson(json['media']),
      navigateTo: json['navigate_to'],
      bannerNameGA4: json['banner_name_ga4'] is String
          ? BannerNameGA4.fromString(json['banner_name_ga4'])
          : null,
    );
  }

  final int id;
  final CmsMedia media;
  final String navigateTo;
  final BannerNameGA4? bannerNameGA4;
}
