import 'package:azzas_app_commons/azzas_app_commons.dart' show AzzasCmsThemeProvider, ListExtensions;
import 'package:azzas_cms/components/azzas_categories_mosaic/azzas_categories_mosaic.dart';
import 'package:azzas_cms/components/azzas_categories_mosaic/categories_mosaic_cms_component.dart';
import 'package:azzas_cms/utils/azzas_cms_content_spacer.dart';
import 'package:azzas_app_commons/utils/navigator_dynamic.dart';
import 'package:flutter/material.dart';

typedef CategoriesMosaicCallback = Future<void> Function(
    CategoryMosaicItemCmsComponent item, int index);

class CategoriesMosaicCmsWidget extends StatefulWidget {
  final String? title;
  final List<CategoryMosaicItemCmsComponent> items;
  final String componentType;
  final CategoriesMosaicCallback? onItemSelected;
  final CategoriesMosaicCallback? onVisible;

  const CategoriesMosaicCmsWidget({
    super.key,
    this.title,
    required this.items,
    required this.componentType,
    this.onItemSelected,
    this.onVisible,
  });

  factory CategoriesMosaicCmsWidget.fromCategoriesMosaicCmsComponent(
    CategoriesMosaicCmsComponent component, {
    CategoriesMosaicCallback? onItemSelected,
    CategoriesMosaicCallback? onVisible,

  }) {
    return CategoriesMosaicCmsWidget(
      title: component.title,
      items: component.items,
      componentType: component.componentType,
      onItemSelected: onItemSelected,
      onVisible: onVisible,
    );
  }

  @override
  State<CategoriesMosaicCmsWidget> createState() =>
      _CategoriesMosaicCmsWidgetState();
}

class _CategoriesMosaicCmsWidgetState extends State<CategoriesMosaicCmsWidget> {

@override
  void initState() {
    super.initState();
    if (widget.onVisible != null) {
      widget.items.asMap().forEach((index, item) {
        widget.onVisible!(item, index);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = AzzasCmsThemeProvider.of(context).categories;

    final productsGroupsList = widget.items
        .mapIndexed((index, item) => ProductsGroup(
              image: MosaicItemImage.fromCMSMediaDataAttributes(
                  item.media.data.attributes),
              linkOnTap: () => _onTap(context, item, index),
            ))
        .toList();

    final contentSpace = theme.contentSpace ?? const EdgeInsets.all(8);

    return AzzasCmsContentSpacer(
      child: Container(
        padding: contentSpace,
        child: AzzasCategoriesMosaic(
          mosaicTitle: widget.title,
          productsGroupsList: productsGroupsList,
        ),
      ),
    );
  }

  void _onTap(BuildContext context, CategoryMosaicItemCmsComponent item,
      int index) async {
    if (widget.onItemSelected != null) {
      await widget.onItemSelected!(item, index);
    }
    NavigatorDynamic.call(item.navigateTo);
  }
}
