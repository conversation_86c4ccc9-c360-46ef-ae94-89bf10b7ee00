import 'package:azzas_cms/azzas_cms.dart';
import 'package:azzas_cms/models/cms_component.dart';
import 'package:azzas_cms/models/cms_media.dart';

class HomeCompositionCmsComponent extends CmsComponent {
  final List<HomeComponentCmsComponent> components;

  HomeCompositionCmsComponent({
    required super.id,
    required super.componentType,
    required this.components,
  });

  factory HomeCompositionCmsComponent.fromJson(Map<String, dynamic> json) {
    final components = (json['home_components'] as List)
        .map((c) => HomeComponentCmsComponent.fromJson(c))
        .whereType<HomeComponentCmsComponent>().toList();
    return HomeCompositionCmsComponent(
      id: json['id'],
      componentType: json['__component'],
      components: components,
    );
  }
}

class HomeComponentCmsComponent {
  final CmsMedia? media;
  final String? videoUrl;
  final String? navigateTo;
  final String? buttonText;
  final String? buttonNavigateTo;
  final bool? hasBorder;
  final bool hasValidButton;
  final BannerNameGA4? bannerNameGA4;

  HomeComponentCmsComponent({
    this.media,
    this.videoUrl,
    this.navigateTo,
    this.buttonText,
    this.buttonNavigateTo,
    this.hasBorder = false,
    required this.bannerNameGA4,

    required this.hasValidButton,
  });

  factory HomeComponentCmsComponent.fromJson(Map<String, dynamic> json) {
    return HomeComponentCmsComponent(
      media: CmsMedia.fromJson(json['media']),
      videoUrl: json['video_url'],
      navigateTo: json['navigate_to'],
      buttonText: json['button_text'],
      buttonNavigateTo: json['button_navigate_to'],
      hasBorder: json['has_border'],
      hasValidButton:
          json['button_text'] != null && json['button_navigate_to'] != null,
      bannerNameGA4: json['banner_name_ga4'] is String
          ? BannerNameGA4.fromString(json['banner_name_ga4'])
          : null,

    );
  }
}
