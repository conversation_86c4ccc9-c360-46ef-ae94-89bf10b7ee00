import 'package:azzas_cms/azzas_cms.dart';
import 'package:flutter/material.dart';

class ProductCarouselImageCmsComponent extends CmsComponent {
  final String title;
  final String subtitle;
  final String? callToAction;
  final String filterCategoryOrCluster;
  final String? orderBy;
  final Color? textColor;
  final Color? backgroundColor;
  final CmsMedia image;
  final BannerNameGA4? bannerNameGA4;

  const ProductCarouselImageCmsComponent({
    required super.id,
    required super.componentType,
    required this.title,
    required this.filterCategoryOrCluster,
    required this.subtitle,
    this.callToAction,
    this.orderBy,
    this.textColor,
    this.backgroundColor,
    required this.image,
    required this.bannerNameGA4,
  });

  factory ProductCarouselImageCmsComponent.fromJson(Map<String, dynamic> json) {
    return ProductCarouselImageCmsComponent(
      id: json['id'],
      componentType: json['__component'] ??
          "product-carousel-image.product-carousel-image",
      title: json['title'],
      subtitle: json['subtitle'],
      callToAction: json['call_to_action'],
      filterCategoryOrCluster: json['filter_category_or_cluster'],
      orderBy: json['orderby'],
      backgroundColor: ColorUtils.parsePrismicColor(json['background_color']),
      textColor: ColorUtils.parsePrismicColor(json['text_color']),
      image: CmsMedia.fromJson(json['image']),
      bannerNameGA4: json['banner_name_ga4'] is String
          ? BannerNameGA4.fromString(json['banner_name_ga4'])
          : null,
    );
  }
}
