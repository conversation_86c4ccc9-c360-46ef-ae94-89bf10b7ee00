import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:azzas_cms/components/azzas_coupon_banner/azzas_coupon_banner.dart';
import 'package:flutter/material.dart';
import 'package:visibility_detector/visibility_detector.dart';

class CouponBannerCmsWidget extends StatelessWidget {
  const CouponBannerCmsWidget({
    super.key,
    this.title,
    this.description,
    required this.coupon,
    this.backgroundColor,
    this.textColor,
    this.navigateTo,
    this.hasMargin,
    required this.componentType,
    this.bannerNameGA4,
    this.onVisible,
    this.onTap,
  });

  final String? title;
  final String? description;
  final String coupon;
  final Color? backgroundColor;
  final Color? textColor;
  final String? navigateTo;
  final bool? hasMargin;
  final String componentType;
  final BannerNameGA4? bannerNameGA4;
  final Function(BannerNameGA4? bannerNameGA4, int index)? onVisible;
  final Function(BannerNameGA4? bannerNameGA4, int index)? onTap;

  factory CouponBannerCmsWidget.fromCouponBannerCmsComponent(
    CouponBannerCmsComponent component, {
    Function(BannerNameGA4? bannerNameGA4, int index)? onVisible,
    Function(BannerNameGA4? bannerNameGA4, int index)? onTap,
  }
  ) {
    return CouponBannerCmsWidget(
      title: component.title,
      description: component.description,
      coupon: component.coupon,
      backgroundColor: component.backgroundColor,
      textColor: component.textColor,
      navigateTo: component.navigateTo,
      hasMargin: component.hasMargin,
      componentType: component.componentType,
      bannerNameGA4: component.bannerNameGA4,
      onTap: onTap,
      onVisible: onVisible,
    );
  }


  @override
  Widget build(BuildContext context) {
    return AzzasCmsContentSpacer.noSpacingWhenPreviousComponentIs(
      previousComponentType: CarouselListCmsComponent,
      child: VisibilityDetector(
        key: Key('coupon_banner_${title}'),
        onVisibilityChanged: (info) {
          if (info.visibleFraction >= 0.8 && onVisible != null) {
            onVisible!(bannerNameGA4, 1);
          }
        },
        child: AzzasCouponBanner(
          title: title,
          description: description,
          coupon: coupon,
          backgroundColor: backgroundColor,
          textColor: textColor,
          linkOnTap: () =>
              navigateTo != null ? NavigatorDynamic.call(navigateTo!) : () {},
          hasMargin: hasMargin ?? false,
          bannerNameGA4: bannerNameGA4,
        ),
      ),
    );
  }

}
