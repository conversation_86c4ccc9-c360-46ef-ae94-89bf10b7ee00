import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:flutter/material.dart';

class CompleteUserDataCubit extends Cubit<CompleteUserDataState> {
  final AddClientProfileUseCase _addClientProfileUseCase;
  final UpdateUserInfoUseCase _updateUserInfoUseCase;
  final orderFormCubit = Modular.get<OrderFormCubit>();

  CompleteUserDataCubit({
    required AddClientProfileUseCase addClientProfileUseCase,
    required UpdateUserInfoUseCase updateUserInfoUseCase,
  })  : _updateUserInfoUseCase = updateUserInfoUseCase,
        _addClientProfileUseCase = addClientProfileUseCase,
        super(CompleteUserDataState.initial());

  Future<void> completeUserData({
    required String orderFormId,
    required UserInfo userInfo,
    required bool receiveEmail,
    required bool receiveSMS,
  }) async {
    emit(state.copyWith(loading: true));

    try {
      final [
        orderFormUpdated as OrderForm,
        updateUser,
      ] = await Future.wait([
        _addClientProfileUseCase(
          dataClientProfile: BodyAddClientProfile(
            firstname: userInfo.personName,
            lastname: userInfo.personSurname,
            email: userInfo.personEmail,
            phone: userInfo.personTelephone
                ?.replaceAll(' ', '')
                .replaceAll('-', ''),
            document: userInfo.personDocumentCpf
                ?.replaceAll('-', '')
                .replaceAll('.', ''),
          ),
          orderFormId: orderFormId,
        ),
        _updateUserInfoUseCase(
          receiveEmail: receiveEmail,
          receiveSMS: receiveSMS,
          userInfo: userInfo,
        ),
      ]);

      orderFormCubit.updateOrderForm(orderForm: orderFormUpdated);

      emit(
        state.copyWith(
          success: true,
          orderForm: orderFormUpdated,
          loading: false,
        ),
      );

      _setAnalyticsUserInfo(userInfo);
    } catch (e, st) {
      _handleError(e, st);
      emit(state.copyWith(error: true, loading: false));
    }
  }

  /// Lida com erros de maneira centralizada
  void _handleError(Object e, StackTrace st) {
    debugPrint(e.toString());
    debugPrintStack(stackTrace: st);
  }

  void _setAnalyticsUserInfo(UserInfo userInfo) {
    final eventDispatcher = Modular.get<EventDispatcher>();
    if (userInfo.personEmail != null) {
      eventDispatcher.setUserInfo(
        email: userInfo.personEmail!,
        firstName: userInfo.personName,
        lastName: userInfo.personSurname,
        phone:
            userInfo.personTelephone?.replaceAll(' ', '').replaceAll('-', ''),
        birthday: userInfo.personDateBirth,
      );
    }
  }
}
