part of 'login_create_password_cubit.dart';

class LoginCreatePasswordState {
  final bool isLoading;
  final bool hasError;
  final PasswordInputState passwordInputState;
  final PasswordInputState confirmPasswordInputState;
  final bool hasPasswordError;
  final bool isEmailValid;
  final bool isFormValid;

  LoginCreatePasswordState({
    required this.isLoading,
    required this.hasError,
    required this.passwordInputState,
    required this.confirmPasswordInputState,
    required this.hasPasswordError,
    required this.isEmailValid,
    required this.isFormValid,
  });

  factory LoginCreatePasswordState.initial() {
    return LoginCreatePasswordState(
      isLoading: false,
      hasError: false,
      passwordInputState: PasswordInputState.initial(),
      confirmPasswordInputState: PasswordInputState.initial(),
      hasPasswordError: false,
      isEmailValid: false,
      isFormValid: false,
    );
  }

  LoginCreatePasswordState copyWith({
    bool? isLoading,
    bool? hasError,
    PasswordInputState? passwordInputState,
    PasswordInputState? confirmPasswordInputState,
    bool? hasPasswordError,
    bool? isEmailValid,
    bool? isFormValid,
  }) {
    return LoginCreatePasswordState(
      isLoading: isLoading ?? this.isLoading,
      hasError: hasError ?? this.hasError,
      passwordInputState: passwordInputState ?? this.passwordInputState,
      confirmPasswordInputState:
          confirmPasswordInputState ?? this.confirmPasswordInputState,
      hasPasswordError: hasPasswordError ?? this.hasPasswordError,
      isEmailValid: isEmailValid ?? this.isEmailValid,
      isFormValid: isFormValid ?? this.isFormValid,
    );
  }
}

class PasswordInputState {
  final bool isObscure;
  final PasswordValidationState passwordValidationState;

  PasswordInputState({
    required this.isObscure,
    required this.passwordValidationState,
  });

  PasswordInputState copyWith({
    bool? isObscure,
    PasswordValidationState? passwordValidationState,
  }) {
    return PasswordInputState(
      isObscure: isObscure ?? this.isObscure,
      passwordValidationState:
          passwordValidationState ?? this.passwordValidationState,
    );
  }

  factory PasswordInputState.initial() {
    return PasswordInputState(
      isObscure: true,
      passwordValidationState: PasswordValidationState.initial(),
    );
  }
}
