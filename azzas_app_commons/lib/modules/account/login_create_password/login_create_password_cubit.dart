import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:azzas_app_commons/modules/account/common/models/password_validation_state.dart';
import 'package:flutter/material.dart';

part 'login_create_password_state.dart';

class LoginCreatePasswordCubit extends Cubit<LoginCreatePasswordState> {
  final SendLoginUseCase _sendLoginUseCase;

  LoginCreatePasswordCubit(this._sendLoginUseCase)
      : super(LoginCreatePasswordState.initial());

  Future<String?> createPassword({
    required String email,
    required String password,
    required String confirmedPassword,
  }) async {
    try {
      emit(state.copyWith(isLoading: true, hasPasswordError: false));

      if (!_isFormValid(email, password, confirmedPassword)) {
        return null;
      }

      if (password != confirmedPassword) {
        emit(state.copyWith(hasPasswordError: true));
        return null;
      }

      final tokenResponse = await _sendLoginUseCase(email);
      final token = tokenResponse.replaceAll("_vss=", "");

      return token;
    } catch (e) {
      debugPrint("Error in createPassword: $e");
      emit(state.copyWith(hasError: true));
      return null;
    } finally {
      emit(state.copyWith(isLoading: false));
    }
  }

  void validatePassword(String password, bool isFromConfirmPassword) {
    final passwordValidationState =
        PasswordValidationState.fromPassword(password);

    final newState = isFromConfirmPassword
        ? state.copyWith(
            confirmPasswordInputState: state.confirmPasswordInputState
                .copyWith(passwordValidationState: passwordValidationState))
        : state.copyWith(
            passwordInputState: state.passwordInputState
                .copyWith(passwordValidationState: passwordValidationState));

    emit(newState);
  }

  void validateEmail(String email) {
    final isEmailValid = TextHelper.isEmail(email);
    emit(state.copyWith(isEmailValid: isEmailValid));
  }

  void validateForm({
    required String email,
    required String password,
    required String confirmedPassword,
  }) {
    final isEmailValid = TextHelper.isEmail(email);
    final passwordValidationState =
        PasswordValidationState.fromPassword(password);
    final confirmPasswordValidationState =
        PasswordValidationState.fromPassword(confirmedPassword);

    final isFormValid = isEmailValid &&
        passwordValidationState.isValid &&
        confirmPasswordValidationState.isValid &&
        password == confirmedPassword &&
        password.isNotEmpty &&
        confirmedPassword.isNotEmpty;

    emit(state.copyWith(
      isEmailValid: isEmailValid,
      isFormValid: isFormValid,
      passwordInputState: state.passwordInputState.copyWith(
        passwordValidationState: passwordValidationState,
      ),
      confirmPasswordInputState: state.confirmPasswordInputState.copyWith(
        passwordValidationState: confirmPasswordValidationState,
      ),
      hasPasswordError: password.isNotEmpty &&
          confirmedPassword.isNotEmpty &&
          password != confirmedPassword,
    ));
  }

  bool _isFormValid(String email, String password, String confirmedPassword) {
    final isEmailValid = TextHelper.isEmail(email);
    final passwordValidationState =
        PasswordValidationState.fromPassword(password);
    final confirmPasswordValidationState =
        PasswordValidationState.fromPassword(confirmedPassword);

    return isEmailValid &&
        passwordValidationState.isValid &&
        confirmPasswordValidationState.isValid &&
        password == confirmedPassword &&
        password.isNotEmpty &&
        confirmedPassword.isNotEmpty;
  }

  void toggleObscurePassword({
    bool isFromConfirmPassword = false,
  }) {
    final newState = isFromConfirmPassword
        ? state.copyWith(
            confirmPasswordInputState: state.confirmPasswordInputState.copyWith(
                isObscure: !state.confirmPasswordInputState.isObscure))
        : state.copyWith(
            passwordInputState: state.passwordInputState
                .copyWith(isObscure: !state.passwordInputState.isObscure));

    emit(newState);
  }

  void resetState() {
    emit(LoginCreatePasswordState.initial());
  }
}
