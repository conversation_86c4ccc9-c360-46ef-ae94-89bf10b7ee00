import 'package:azzas_analytics/services/analytics_logger.dart';
import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:flutter/foundation.dart';

part 'orders_list_state.dart';

class OrdersListCubit extends Cubit<OrdersListState> {
  final GetUserOrdersUseCase _getMyOrdersUseCase;
  final GetUserOrderUseCase _getMyOrderUseCase;
  final GetUserOrderStatusUseCase _getOrderStatusUseCase;
  final GetOrdersUseCase _getOrdersUseCase;
  final AuthStorageService storage;

  OrdersListCubit(
    this._getMyOrdersUseCase,
    this._getMyOrderUseCase,
    this._getOrderStatusUseCase,
    this._getOrdersUseCase, {
    required this.storage,
  }) : super(OrdersListState.initial()) {
    _init();
  }

  Future<void> _init() async {
    final userInfo = await storage.readUserInfo();

    if (userInfo != null) {
      emit(state.copyWith(
        userInfo: userInfo,
      ));
    }
  }

  Future<List<StatusOrder>?> getOrderStatus(String orderId) async {
    try {
      return await _getOrderStatusUseCase(orderId: orderId);
    } catch (e) {
      debugPrint("Error in getOrderStatus $orderId: $e");
      return null;
    }
  }

  Future<void> getOrder(
    String orderId, {
    Future<void> Function()? finishOrder,
  }) async {
    try {
      emit(state.copyWith(
        isLoading: true,
        hasError: false,
      ));
      await finishOrder?.call();
      final response = await _getMyOrderUseCase(orderId: orderId);

      final Map<String, List<StatusOrder>> statusMap = {};
      List<StatusOrder>? status = await getOrderStatus(orderId);
      if (status != null) {
        status.removeWhere((element) {
          return element.description?.contains('cancelado') == true &&
              element.date == null;
        });
        status = applyRules(status, response);
      }
      statusMap[orderId] = status ?? [];

      final orderInDetail = response;
      emit(
        state.copyWith(
          hasError: false,
          isLoading: false,
          orderInDetail: orderInDetail,
          orderStatusMap: statusMap,
        ),
      );
    } catch (e) {
      debugPrint("Error in getMyOrder $orderId: $e");

      emit(
        state.copyWith(
          hasError: true,
          isLoading: false,
          orderInDetail: null,
        ),
      );
    } finally {
      emit(state.copyWith(isLoading: false));
    }
  }

  List<StatusOrder> applyRules(
      List<StatusOrder> statuslist, ListUserOrder order) {
    /// Caso não contenha da de realização do pedido no status, resgata do pedido
    if (order.orderConfirmationDate != null || order.cancellationDate != null) {
      statuslist.forEach((s) {
        if (s.id == 'ORDER_CONFIRMATION' && s.date == null) {
          s.date = order.creationDate;
        }
        if (s.id == 'ORDER_CANCELED' &&
            s.date != null &&
            order.cancellationDate != null) {
          s.date = order.cancellationData?.cancelationDate;
        }
      });
    }

    /// Caso o pedido tenha sido cancelado, remove todos os status sem data.
    final isCanceled =
        statuslist.any((s) => s.id == 'ORDER_CANCELED' && s.date != null);
    if (isCanceled) {
      statuslist.removeWhere((s) => s.date == null);
    }
    return statuslist;
  }

  void setOrderDetail(ListUserOrder order) => emit(
        state.copyWith(
          orderInDetail: order,
        ),
      );

  Future<void> getMyOrders() async {
    try {
      emit(state.copyWith(
        isLoading: true,
        activeOrders: [],
        completedOrders: [],
        orderInDetail: null,
      ));

      final response = await _getMyOrdersUseCase();
      final activeOrders = <ListUserOrder>[];
      final completedOrders = <ListUserOrder>[];
      for (var element in response.list ?? <ListUserOrder>[]) {
        if (element.cancellationDate != null || element.status == 'entregue') {
          completedOrders.add(element);
        } else {
          activeOrders.add(element);
        }
      }
      emit(
        state.copyWith(
          isLoading: false,
          activeOrders: activeOrders,
          completedOrders: completedOrders,
        ),
      );
    } catch (e) {
      debugPrint("Error in getMyOrders: $e");

      emit(
        state.copyWith(
          hasError: true,
          isLoading: false,
          activeOrders: [],
          completedOrders: [],
        ),
      );
    } finally {
      emit(state.copyWith(isLoading: false));
    }
  }

  Future<void> refreshStatus() async {
    final orders = state.activeOrders;
    if (orders.isEmpty) return;

    final Map<String, List<StatusOrder>> statusMap = {};

    for (var it in orders) {
      if (it.orderId == null) return;
      final status = await getOrderStatus(it.orderId!);
      if (status != null) {
        status.removeWhere((element) {
          return element.description?.contains('cancelado') == true &&
              element.date == null;
        });
      }
      statusMap[it.orderId!] = status ?? [];
    }

    emit(state.copyWith(orderStatusMap: statusMap));
  }

  Future<void> getOrders({
    required String orderGroup,
    bool getStatus = true,
    Future<void> Function()? finishOrder,
  }) async {
    try {
      emit(
        state.copyWith(
          isLoading: true,
          activeOrders: [],
          orderStatusMap: {},
        ),
      );
      final orders = await _getOrdersUseCase.call(orderGroup: orderGroup);

      final Map<String, List<StatusOrder>> statusMap = {};

      if (getStatus) {
        for (var it in orders) {
          if (it.orderId == null) return;
          final status = await getOrderStatus(it.orderId!);
          if (status != null) {
            status.removeWhere((element) {
              return element.description?.contains('cancelado') == true &&
                  element.date == null;
            });
          }
          statusMap[it.orderId!] = status ?? [];
        }
      }

      emit(state.copyWith(
        hasError: false,
        activeOrders: orders,
        orderStatusMap: statusMap,
      ));
      await finishOrder?.call();
    } catch (e, s) {
      debugPrint("Error in getOrders: $e");

      emit(
        state.copyWith(
          hasError: true,
          isLoading: false,
          activeOrders: [],
          orderStatusMap: {},
        ),
      );

      _logOrderConfirmationError(e, s);
    } finally {
      emit(state.copyWith(isLoading: false));
    }
  }

  void _logOrderConfirmationError(Object error, StackTrace stackTrace) {
    final eventDispatcher = Modular.get<EventDispatcher>();
    eventDispatcher.logError(
      message: 'Tela de confirmação não foi carregada',
      eventName: 'order_confirmation',
      error: error,
      stackTrace: stackTrace,
    );
  }
}
