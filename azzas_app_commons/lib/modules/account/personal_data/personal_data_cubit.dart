import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:flutter/foundation.dart';

class PersonalDataCubit extends Cubit<PersonalDataState> {
  final UpdateUserInfoUseCase updateUserInfoUseCase;
  final GetUserInfoUseCase getUserInfoUseCase;

  PersonalDataCubit({
    required this.updateUserInfoUseCase,
    required this.getUserInfoUseCase,
  }) : super(PersonalDataState(
            isLoading: false,
            personalDataSuccessSent: false,
            userInfo: null,
            receiveEmail: null,
            receiveSMS: null));

  Future<void> updateUserInfo(UserInfo userInfo,
      {required bool receiveSMS, required bool receiveEmail}) async {
    emit(state.copyWith(isLoading: true));

    try {
      await updateUserInfoUseCase.call(
        userInfo: userInfo,
        receiveEmail: receiveEmail,
        receiveSMS: receiveSMS,
      );

      _setAnalyticsUserInfo(userInfo);

      emit(state.copyWith(personalDataSuccessSent: true));
    } catch (e) {
      debugPrint("Error in updateUserInfo: $e");
      emit(state.copyWith(personalDataSuccessSent: false));
      rethrow;
    } finally {
      emit(state.copyWith(isLoading: false));
    }
  }

  void _setAnalyticsUserInfo(UserInfo userInfo) {
    final eventDispatcher = Modular.get<EventDispatcher>();
    if (userInfo.personEmail != null) {
      eventDispatcher.setUserInfo(
        email: userInfo.personEmail!,
        firstName: userInfo.personName,
        lastName: userInfo.personSurname,
        phone:
        userInfo.personTelephone?.replaceAll(' ', '').replaceAll('-', ''),
        birthday: userInfo.personDateBirth,
      );
    }
  }

  Future<void> getPersonalData(PersonalDataModel personalDataModel) async {
    emit(state.copyWith(isLoading: true));
    try {
      final userInfo = await getUserInfoUseCase.call();
      personalDataModel.updateFields(userInfo);
      emit(state.copyWith(userInfo: userInfo, isLoading: false));
    } catch (e) {
      debugPrint("Error in updateUserInfo: $e");
      emit(state.copyWith(isLoading: false));
    }
  }

  Future<void> loadValues() async {
    final shared = SharedPreferencesService();
    final firstValue = await shared.getBool('firstValue');
    final secondValue = await shared.getBool('secondValue');
    emit(state.copyWith(receiveEmail: firstValue, receiveSMS: secondValue));
  }

  Future<void> saveValues(
      {required bool receiveEmail, required bool receiveSMS}) async {
    final shared = SharedPreferencesService();
    await shared.saveBool('firstValue', receiveEmail);
    await shared.saveBool('secondValue', receiveSMS);

    emit(state.copyWith(receiveEmail: receiveEmail, receiveSMS: receiveSMS));
  }

  void enableSaveButton() {
    emit(state.copyWith(isSaveButtonDisabled: false));
  }

  void disableSaveButton() {
    emit(state.copyWith(isSaveButtonDisabled: true));
  }
}
