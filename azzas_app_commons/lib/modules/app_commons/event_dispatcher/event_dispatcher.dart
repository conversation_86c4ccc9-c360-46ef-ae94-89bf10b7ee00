import 'package:azzas_analytics/events/purchase/model/purchase_model.dart';
import 'package:azzas_analytics/events/notify_me/model/notify_me_model.dart';
import 'package:azzas_analytics/events/refine_result/model/refine_result_model.dart';
import 'package:azzas_analytics/events/search/index.dart';
import 'package:azzas_analytics/events/user_info/index.dart';
import 'package:azzas_analytics/events/view_item_list/model/view_item_list_model.dart';
import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:azzas_app_commons/modules/app_commons/event_dispatcher/mappers/index.dart';
import 'package:azzas_analytics/utils/analytics_constants.dart';
import 'package:azzas_analytics/utils/region_enum.dart';
import 'package:azzas_app_commons/modules/app_commons/event_dispatcher/mappers/purchase_item_mapper.dart';
import 'package:azzas_app_commons/modules/app_commons/event_dispatcher/mappers/remove_from_cart_info_mapper.dart';
import 'package:azzas_app_commons/modules/app_commons/event_dispatcher/mappers/zipcode_mapper.dart';

/// Classe responsável por armazenar dados de eventos de analytics relacionados a promoções e itens exibidos.
///
/// Como a maioria desses dados só existe no widget mais acima na árvore, podemos adicionar e resgatar os dados
/// em qualquer widget sem precisar passar parâmetros entre a árvore (Prop Drilling)
class AnalyticsEventTracker {
  String? creativeName;
  String? creativeSlot;
  String? promotionName;
  String? promotionId;
  String? itemListName;
  String? itemListId;
  String? itemListPosition;

  AnalyticsEventTracker.empty()
      : creativeName = null,
        creativeSlot = null,
        promotionName = null,
        promotionId = null,
        itemListName = null,
        itemListId = null,
        itemListPosition = null;

  void trackItemList({
    String? itemListName,
    String? itemListId,
    String? itemListPosition,
  }) {
    this.itemListName = itemListName;
    this.itemListId = itemListId;
    this.itemListPosition = itemListPosition;
  }

  void trackPromotionData(String creativeName, String creativeSlot,
      String promotionName, String promotionId) {
    this.creativeName = creativeName;
    this.creativeSlot = creativeSlot;
    this.promotionName = promotionName;
    this.promotionId = promotionId;
  }

  void clearTracking() {
    creativeName = null;
    creativeSlot = null;
    promotionName = null;
    promotionId = null;
    itemListName = null;
    itemListId = null;
    itemListPosition = null;
  }
}

class EventDispatcher {
  EventDispatcher(this.analyticsEventTracker);

  final _orderFormCubit = Modular.get<OrderFormCubit>();
  final AuthCubit _authCubit = Modular.get<AuthCubit>();

  OrderForm? get orderForm => _orderFormCubit.state.orderForm;

  String get _coupon => _orderFormCubit.getCouponApplied() ?? '';

  String get _sellerCouponName => orderForm?.sellerCouponCode ?? '';

  String get _shippingTier =>
      orderForm?.deliveryType.getDeliveryAnalyticsEventValue ?? '';

  double get _totalizersDiscount => orderForm?.discountValue ?? 0;

  double get _totalItemDiscount => orderForm?.totalDiscount ?? 0;

  double get _totalDiscount => _totalizersDiscount + _totalItemDiscount;

  double get _value => (orderForm?.value ?? 0) / 100;

  double get _subtotal => (orderForm?.getTotalItemsValue ?? 0) / 100;

  double get _shippingValue => orderForm?.shippingValue ?? 0;

  PaymentType get _paymentType =>
      orderForm?.paymentType ?? PaymentType.notSelected;

  bool get _isZipcodeValid => orderForm?.deliveryType != DeliveryType.unknown;

  AnalyticsEventTracker analyticsEventTracker;

  AzzasAnalyticsPaymentType get _azzasAnalyticsPaymentType {
    switch (_paymentType) {
      case PaymentType.pix:
        return AzzasAnalyticsPaymentType.pix;
      case PaymentType.creditCard:
        return AzzasAnalyticsPaymentType.creditCard;
      case PaymentType.giftCard:
        return AzzasAnalyticsPaymentType.giftCard;
      default:
        return AzzasAnalyticsPaymentType.notSelected;
    }
  }

  List<AnalyticsEventItem> get _analyticsEventItems =>
      orderForm?.getAddedItems.asMap().entries.map((entry) {
        final index = entry.key;
        final product = entry.value;
        return product.analyticsEventItem(index: index);
      }).toList() ??
      [];

  // ====================================================
  // Add Payment Info Events
  // ====================================================

  Future<void> logAddPaymentInfo({
    AzzasAnalyticsPaymentType? paymentType,
    bool preFilled = false,
  }) async {
    final paymentInfoModel = PaymentInfoMapper.fromOrderForm(
      orderForm: _orderFormCubit.orderForm,
      subtotal: _subtotal,
      preFilled: preFilled,
      totalDiscount: _totalDiscount,
    );
    AzzasAnalyticsEvents.logAddPaymentInfo(paymentInfoModel: paymentInfoModel);
    return;
  }

  // ====================================================
  // Add SellerCode or Discount Coupon Info Events
  // ====================================================

  Future<void> logAddCouponInfo(AddCouponModel couponInfo) async {
    AzzasAnalyticsEvents.logAddCouponInfo(couponInfo: couponInfo);
  }

  // ====================================================
  // Add Shipping Info Events
  // ====================================================

  void logAddShippingInfo() {
    final items = orderForm?.getAddedItems
        .map((e) => ShippingInfoMapper.fromOrderFormProduct(e, _shippingTier))
        .toList();

    AzzasAnalyticsEvents.logAddShippingInfo(
        addShippingInfoModel: AddShippingInfoModel(
      preFilled: 'true',
      shippingTier: _shippingTier,
      currency: 'BRL',
      value: _value,
      shipping: _shippingValue,
      totalDiscount: _totalDiscount,
      subtotal: _subtotal,
      items: items ?? [],
    ));
  }

  // ====================================================
  // View Cart Events
  // ====================================================

  void logViewCart() {
    final items = orderForm?.getAddedItems
        .map((e) => ViewCartItemMapper.fromOrderFormProduct(e))
        .toList();

    AzzasAnalyticsEvents.logViewCart(
      viewCartModel: ViewCartModel(
        items: items ?? [],
        value: _value,
        subtotal: _subtotal,
        totalDiscount: _totalDiscount,
      ),
    );
  }

  // ====================================================
  // Remove From Cart Events
  // ====================================================

  void logRemoveFromCart(
      {Product? product, String? region, String? itemId}) async {
    if (product != null) {
      final items = [
        RemoveFromCartInfoMapper.fromProduct(
            product: product, itemId: itemId, itemShippingTier: _shippingTier)
      ];
      AzzasAnalyticsEvents.logRemoveFromCart(
        removeFromCartInfoModel: RemoveFromCartInfoModel(
          value: product.getPrice().toDouble(),
          items: items,
          region: RegionEnum.fromString(region),
        ),
      );
    }
  }

  // ====================================================
  // Order Reviewed
  // ====================================================

  Future<void> logOrderReviewed() async {
    final orderForm = _orderFormCubit.orderForm;

    final params = OrderReviewedMapper.fromOrderForm(
      orderForm: orderForm,
      subtotal: _subtotal,
    );
    await OrderReviewed.send(params);
  }

  Future<void> logRemoveAllUnavailableItems() async {
    double value = 0;
    final items = orderForm?.getUnavailableItems.map((e) {
      final index = orderForm?.indexProductInBag(e);
      value += e.priceDouble;
      return e.analyticsEventItem(index: index);
    }).toList();

    await AzzasAnalyticsEvents.logRemoveAllUnavailableItems(
      items: items ?? [],
      value: value,
    );
  }

  // ====================================================
  // Add To Cart Events
  // ====================================================

  ///TODO rever a lógica desse método em futuros eventos
  void logAddToCart(
      {Product? product,
      OrderFormProduct? orderFormProduct,
      int? quantity,
      int? index,
      String? itemId,
      String? itemListPosition,
      String? itemListName,
      String addAllToCart = 'false',
      String? region}) {
    if (product != null) {
      final items = [
        AddToCartItemMapper.fromProduct(
            product: product,
            index: index,
            itemId: itemId,
            itemListName: itemListName)
      ];

      AddToCart.send(AddToCartModel(
          currency: 'BRL',
          value: product.getPrice().toDouble(),
          region: RegionEnum.fromString(region),
          items: items));

      return;
    }

    if (orderFormProduct != null) {
      final items = [
        AddToCartItemMapper.fromOrderFormProduct(
            product: orderFormProduct,
            index: index,
            itemId: itemId,
            itemListName: itemListName)
      ];

      AddToCart.send(AddToCartModel(
          currency: 'BRL',
          value: orderFormProduct.priceDouble,
          region: RegionEnum.fromString(region),
          items: items));
    }
  }

  void logAddAllToCart(
      {required List<Product> products,
      required List<OrderFormItem> items,
      required AzzasAnalyticsAddAllCart azzasAnalyticsAddAllCart}) {
    double value = 0;
    List<AnalyticsEventItem> productItems = [];
    final itemById = {for (final it in items) it.itemId: it};

    for (var idx = 0; idx < products.length; idx++) {
      final product = products[idx];

      final matchingSku = product.items?.firstWhere(
        (sku) => itemById.containsKey(sku.itemId),
      );

      productItems.add(
        product.analyticsEventItem(
          index: idx,
          itemId: matchingSku?.itemId ?? '',
          itemListName: azzasAnalyticsAddAllCart.itemListName,
        ),
      );
      value += product.getPrice().toDouble();
    }

    AzzasAnalyticsEvents.logAddToCart(
        items: productItems,
        value: value,
        itemListPosition: azzasAnalyticsAddAllCart.itemListPosition,
        itemListName: azzasAnalyticsAddAllCart.itemListName,
        addAllToCart: azzasAnalyticsAddAllCart.addAllToCart,

        ///Verificar se manteremos esse parâmetro para esse evento
        availableGrid: '');
  }

  // ====================================================
  // Login Events
  // ====================================================

  void logLogin() {
    var azzasAnalyticsLoginType = AzzasAnalyticsLoginType.loggedOut;
    switch (_authCubit.state.loginType) {
      case LoginType.google:
        azzasAnalyticsLoginType = AzzasAnalyticsLoginType.google;
        break;
      case LoginType.apple:
        azzasAnalyticsLoginType = AzzasAnalyticsLoginType.apple;
        break;
      case LoginType.email:
        azzasAnalyticsLoginType = AzzasAnalyticsLoginType.email;
        break;
      case LoginType.password:
        azzasAnalyticsLoginType = AzzasAnalyticsLoginType.password;
        break;
      case LoginType.loggedOut:
        azzasAnalyticsLoginType = AzzasAnalyticsLoginType.loggedOut;
        break;
      case LoginType.biometrics:
        azzasAnalyticsLoginType = AzzasAnalyticsLoginType.biometrics;
        break;
    }

    AzzasAnalyticsEvents.logLogin(
      loginType: azzasAnalyticsLoginType,
    );
  }

  // ====================================================
  // Purchase Events
  // ====================================================

  Future<void> logPurchase({required String transactionId}) async {
    final items = orderForm?.getAddedItems
        .map((e) => PurchaseItemMapper.fromOrderFormProduct(e, _shippingTier))
        .toList();
    await AzzasAnalyticsEvents.logPurchase(
      PurchaseModel(
        items: items,
        value: _value,
        transactionId: transactionId,
        sellerCodName: _sellerCouponName,
        coupon: _coupon,
        shipping: _shippingValue,
        shippingTier: _shippingTier,
        totalDiscount: _totalDiscount,
        subtotal: _subtotal,
        paymentType: _azzasAnalyticsPaymentType.value,
      ),
    );
  }

  // ====================================================
  // Begin Checkout Events
  // ====================================================

  void logBeginCheckout() {
    final items = orderForm?.getAddedItems
        .map((e) => BeginCheckoutMapper.fromOrderFormProduct(e, _shippingTier))
        .toList();

    AzzasAnalyticsEvents.logBeginCheckout(
        beginCheckoutModel: BeginCheckoutModel(
      totalDiscount: _totalDiscount,
      subtotal: _subtotal,
      currency: 'BRL',
      value: _value,
      items: items ?? [],
    ));
  }

  // ====================================================
  // Item List Events
  // ====================================================

  void logViewItemList(
      {String? itemListName, required List<Product> products, String? region}) {
    AzzasAnalyticsEvents.logViewItemList(
      viewItemListModel: ViewItemListModel(
        items: ViewItemListItemMapper.fromProducts(products, itemListName),
        itemListName: itemListName,
      ),
    );
  }

  // ====================================================
  // Item Events
  // ====================================================

  void logViewItem({
    required String availableGrid,
    required Product product,
  }) {
    AzzasAnalyticsEvents.logViewItem(
      availableGrid: availableGrid,
      model: ViewItemMapper.fromProduct(product),
    );
  }

  // ====================================================
  // Select Item Events
  // ====================================================

  void logSelectItem(
      {required String itemListName,
      required Product product,
      required int index,
      String? region}) {
    AzzasAnalyticsEvents.logSelectItem(
        selectItemModel: SelectItemModel(
            itemListName: itemListName,
            item: SelectItemMapper.fromProduct(product, index, itemListName),
            region: RegionEnum.fromString(region)));
  }

  // ====================================================
  // Wishlist Events
  // ====================================================

  void logAddToWishlist({
    Product? product,
    OrderFormProduct? orderFormProduct,
    String? region,
    int? index,
  }) {
    if (product != null) {
      final items = [WishlistItemMapper.fromProduct(product, index)];

      AzzasAnalyticsEvents.logAddToWishlist(
          addToWishlistModel: AddToWishlistModel(
        currency: AnalyticsConstants.currency,
        region: RegionEnum.fromString(region),
        value: product.getPrice().toDouble(),
        items: items,
      ));
      return;
    }

    if (orderFormProduct != null) {
      AzzasAnalyticsEvents.logAddToWishlist(
          addToWishlistModel: AddToWishlistModel(
              currency: AnalyticsConstants.currency,
              region: RegionEnum.fromString(region),
              value: orderFormProduct.priceDouble,
              items: [
            WishlistItemMapper.fromOrderFormProduct(orderFormProduct, index)
          ]));
      return;
    }
  }

  // ====================================================
  // AddPersonalInfo Events
  // ====================================================

  void logAddPersonalInfo({
    required String preFilled,
  }) {
    AzzasAnalyticsEvents.logAddPersonalInfo(
        addPersonalInfoModel: AddPersonalInfoModel(
            preFilled: preFilled,
            currency: 'BRL',
            totalDiscount: _totalDiscount,
            subTotal: _subtotal,
            value: _value,
            items: _analyticsEventItems));
  }

  // ====================================================
  // Select Content Events
  // ====================================================

  void logSelectedContent(String param) {
    AzzasAnalyticsEvents.logSelectContent(contentType: param);
  }

  void logSelectPromotion({
    String? region,
    String? creativeName,
    String? creativeSlot,
    String? promotionName,
    List<Product>? items,
  }) {
    PromotionModel selectPromotionModel = SelectPromotionMapper.fromData(
      region,
      creativeName,
      creativeSlot,
      promotionName,
      items,
    );

    AzzasAnalyticsEvents.logSelectPromotion(selectPromotionModel);
  }

  void logViewPromotion({
    String? region,
    String? creativeName,
    String? creativeSlot,
    String? promotionName,
    List<Product>? items,
  }) {
    PromotionModel selectPromotionModel = SelectPromotionMapper.fromData(
      region,
      creativeName,
      creativeSlot,
      promotionName,
      items,
    );

    AzzasAnalyticsEvents.logViewPromotion(selectPromotionModel);
  }

  // ====================================================
  // ZipCode Events
  // ====================================================

  void logSearchZipCode({
    required String zipCode,
    required bool flagPickup,
    required String region,
    List<PackageModel>? shippings,
    ShippingOptions? shippingOptions,
  }) {
    List<SearchZipCodeItem>? shippingsList;

    if (shippingOptions != null) {
      shippingsList = ZipcodeMapper.fromShippingOptions(
        shippingOptions: shippingOptions,
      );
    } else if (shippings != null) {
      shippingsList = ZipcodeMapper.fromPackageModel(shippings);
    }

    AzzasAnalyticsEvents.logSearchZipCode(
        searchZipCodeModel: SearchZipCodeModel(
      zipCode: zipCode,
      flagPickup: flagPickup.toString(),
      region: RegionEnum.fromString(region),
      shippings: shippingsList,
    ));
  }

  // ====================================================
  // Auth Action event
  // ====================================================

  Future<void> logAuthAction({required AuthActionType authActionType}) async {
    var method;
    var type;
    switch (authActionType) {
      case AuthActionType.login:
        type = 'login';
        break;
      case AuthActionType.signup:
        type = 'signup';
        break;
    }
    switch (_authCubit.state.loginType) {
      case LoginType.google:
        method = 'google';
        break;
      case LoginType.apple:
        method = 'apple id';
        break;
      case LoginType.email:
        method = 'email';
        break;
      case LoginType.password:
        method = 'password';
        break;
      case LoginType.biometrics:
        method = 'biometric';
        break;
      case LoginType.loggedOut:
        method = null;
        break;
    }
    AzzasAnalyticsEvents.logAuthAction(
        authActionModel: AuthActionModel(
      method: method,
      type: type,
    ));
  }

  // ====================================================
  // Notify Me Events
  // ====================================================

  void logNotifyMe({String? size, String? itemRef, String? lineItems}) {
    AzzasAnalyticsEvents.logNotifyMe(
      notifyMeModel: NotifyMeModel(
        size: size,
        itemRef: itemRef,
        lineItems: lineItems,
      ),
    );
  }

  // ====================================================
  // Search Events
  // ====================================================

  void logSearch({
    required String searchTerm,
    required bool searchFound,
  }) {
    AzzasAnalyticsEvents.logSearch(
      searchModel: SearchModel(
        searchTerm: searchTerm,
        searchFound: searchFound,
      ),
    );
  }

  // ====================================================
  //Share Events
  // ====================================================

  void logShare({String? lineItems, String? itemRef}) {
    AzzasAnalyticsEvents.logShare(
      model: ShareModel(
        itemRef: itemRef,
        lineItems: lineItems,
      ),
    );
  }

  // ====================================================
  // Refine Results Events
  // ====================================================

  void logRefineResults({
    String? size,
    String? color,
    String? priceRange,
    String? ordering,
    String? category,
    String? region,
  }) {
    AzzasAnalyticsEvents.logRefineResult(RefineResultModel(
      size: size,
      color: color,
      priceRange: priceRange,
      ordering: ordering,
      category: category,
      region: region,
    ));
  }

  // ====================================================
  // User Info (Set User Property)
  // ====================================================

  void setUserInfo({
    required String email,
    String? firstName,
    String? lastName,
    String? phone,
    String? zipcode,
    String? street,
    String? streetNumber,
    String? city,
    String? gender,
    String? orderFormId,
    String? uf,
    String? country,
    String? birthday,
    String? neighborhood,
  }) {
    AzzasAnalyticsEvents.setUserInfo(
      userInfoModel: UserInfoModel(
        email: email,
        firstName: firstName,
        lastName: lastName,
        phone: phone,
        zipcode: zipcode,
        street: street,
        streetNumber: streetNumber,
        city: city,
        gender: gender,
        orderFormId: orderFormId,
        uf: uf,
        country: country,
        birthday: birthday,
        neighborhood: neighborhood,
      ),
    );
  }

  // ====================================================
  // Error Events
  // ====================================================

  Future<void> logError({
    required String message,
    required String eventName,
    dynamic error,
    StackTrace? stackTrace,
    String? region,
  }) async {
    AzzasAnalyticsEvents.logError(
      message: message,
      eventName: eventName,
      error: error,
      stackTrace: stackTrace,
    );
  }

  void logVideoLoopStart({
    String? lineItems,
    String? itemRef}) {
    AzzasAnalyticsEvents.logVideoLoopStart(
      model: VideoLoopStartModel(
        itemRef: itemRef,
        lineItems: lineItems,
      ),
    );
  }

  void logLegacyRemoveFromCart({
    required OrderFormProduct orderFormProduct,
    String? itemId,
    required String region}) {
      final items = [RemoveFromCartInfoMapper.fromOrderFormProduct(product: orderFormProduct)];
      AzzasAnalyticsEvents.logRemoveFromCart(removeFromCartInfoModel: RemoveFromCartInfoModel(
        value: orderFormProduct.priceDouble,
        items: items,
        region: RegionEnum.fromString(region),
        ),
      );
    }

  void logLegacySelectItem({
    required OrderFormProduct orderFormProduct,
    required String itemListName,
    required String region,
    int? index,}) {
    AzzasAnalyticsEvents.logSelectItem(
        selectItemModel: SelectItemModel(
            itemListName: itemListName,
            item: SelectItemMapper.fromOrderFormProduct(
                product: orderFormProduct,
                itemListName: itemListName,
                index: index
            ),
            region: RegionEnum.fromString(region)));
  }
}

enum AuthActionType { login, signup }
