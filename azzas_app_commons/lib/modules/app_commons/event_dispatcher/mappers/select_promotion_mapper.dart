import 'package:azzas_analytics/utils/region_enum.dart';
import 'package:azzas_app_commons/azzas_app_commons.dart';

class SelectPromotionMapper {
  static PromotionModel fromData(
    String? region,
    String? promotionName,
    String? creativeName,
    String? creativeSlot,
    List<Product>? products,
  ) {
    List<PromotionItemModel>? items = products
        ?.map((product) => PromotionItemModel(
              currency: 'BRL',
              itemId: product.productId,
              itemName: product.productName,
              itemBrand: product.brand,
              itemCategory: product.saleOrCollection,
              itemCategory2: product.extractDeepestCategory,
              itemVariant: product.productColor,
              itemRef: product.productReferenceCode,
              discount: double.parse(
                  (product.getListPrice() - product.getPrice()).toString()),
              price: double.parse(product.getPrice().toString()),
              quantity: 1,
            ))
        .toList();
    return PromotionModel(
      region: RegionEnum.fromString(region),
      promotionName: promotionName,
      creativeName: creativeName,
      creativeSlot: creativeSlot,
      items: items,
    );
  }
}
