import 'package:azzas_analytics/events/view_item_list/model/view_item_list_item_model.dart';
import 'package:azzas_app_commons/azzas_app_commons.dart';

class ViewItemListItemMapper {
  static List<ViewItemListItemModel> fromProducts(
      List<Product> products, String? itemListName) {
    return products.asMap().entries.map((entry) {
      final index = entry.key;
      final product = entry.value;

      return ViewItemListItemModel(
        price: product.getPrice().toDouble(),
        quantity: 1,
        itemRef: product.productReferenceCode,
        index: index,
        itemBrand: product.brand,
        itemName: product.productName,
        itemCategory: product.saleOrCollection,
        itemVariant: product.productColor,
        itemCategory2: product.categories?.firstOrNull?.split('/')[1],
        itemId: product.productId,
        discount: (product.getListPrice() - product.getPrice()).toDouble(),
        itemListName: itemListName,
      );
    }).toList();
  }
}
