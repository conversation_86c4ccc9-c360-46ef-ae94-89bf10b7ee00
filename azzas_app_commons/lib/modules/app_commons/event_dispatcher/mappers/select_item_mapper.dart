import 'package:azzas_app_commons/azzas_app_commons.dart';

class SelectItemMapper {
  static SelectItemItemModel fromProduct(
      Product product, int index, String itemListName) {
    return SelectItemItemModel(
        itemId: product.productId,
        itemRef: product.productReferenceCode,
        itemName: product.productName,
        itemBrand: product.brand,
        itemCategory: product.saleOrCollection,
        itemCategory2: product.extractDeepestCategory,
        itemVariant: product.productColor,
        index: index,
        quantity: 1,
        itemListName: itemListName,
        price: double.parse(product.getPrice().toString()),
        discount: double.parse(
            (product.getListPrice() - product.getPrice()).toString()));
  }

  static SelectItemItemModel fromOrderFormProduct({
    required OrderFormProduct product,
    int? index,
    String? itemListName,
  }) {
    return SelectItemItemModel(
      itemId: product.productId,
      itemRef: product.productRefId,
      itemName: product.productNameWithoutSkuName,
      itemBrand: product.productBrand,
      itemCategory: product.saleOrCollection,
      itemCategory2: product.extractMostSpecificCategoryFromMap,
      itemVariant: product.productColor,
      index: index,
      quantity: 1,
      itemListName: itemListName,
      discount: product.totalDiscount,
      price: product.priceDouble,
    );
  }
}
