import 'package:azzas_app_commons/azzas_app_commons.dart';

class OrderReviewedMapper {
  static OrderReviewedParams fromOrderForm({
    required OrderForm orderForm,
    required double subtotal,
  }) {
    return OrderReviewedParams(
      currency: 'BRL',
      value: orderForm.totalItemsWithShipping,
      subtotal: subtotal,
      totalDiscount: orderForm.discountValue + orderForm.totalDiscount,
      paymentType: orderForm.paymentType.value,
      itemStrLine: orderForm.items?.map((e) => e.id).join(',') ?? '',
    );
  }
}
