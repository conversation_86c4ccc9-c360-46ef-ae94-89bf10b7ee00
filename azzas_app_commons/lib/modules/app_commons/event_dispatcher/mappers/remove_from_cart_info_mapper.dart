import 'package:azzas_app_commons/azzas_app_commons.dart';

class RemoveFromCartInfoMapper {
  static RemoveFromCartInfoItem fromProduct({
    required Product product,
    String? itemId,
    String? itemShippingTier,
  }) {

    final item = product.items?.firstWhere((item) => item.itemId == itemId);

    return RemoveFromCartInfoItem(
        itemId: product.productId,
        itemRef: product.productReferenceCode,
        itemName: product.productName,
        itemBrand: product.brand,
        itemCategory: product.saleOrCollection,
        itemCategory2: product.extractDeepestCategory,
        itemCategory4: null,
        itemVariant: product.productColor,
        quantity: 1,
        price: double.parse(product.getPrice().toString()),
        discount: double.parse((product.getListPrice() - product.getPrice()).toString()),
        itemSku: itemId,
        itemVariant2:item?.tamanho?.isNotEmpty == true ? item!.tamanho!.first : null,
        itemShippingTier: itemShippingTier);
  }

  static RemoveFromCartInfoItem fromOrderFormProduct({
    required OrderFormProduct product,
    String? itemId,
    String? itemShippingTier,
  }) {
    return RemoveFromCartInfoItem(
      itemId: product.productId,
      itemName: product.productNameWithoutSkuName,
      itemBrand: product.productBrand,
      itemCategory: product.saleOrCollection,
      itemCategory2: product.extractMostSpecificCategoryFromMap,
      itemCategory4: null,
      itemVariant: product.productColor,
      itemVariant2: product.getSize,
      itemSku: product.itemId,
      itemRef: product.productRefId,
      discount: product.totalDiscount,
      price: product.priceDouble,
      quantity: 1,
      itemShippingTier: itemShippingTier,
    );
  }
}
