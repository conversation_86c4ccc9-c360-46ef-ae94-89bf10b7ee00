import 'package:azzas_app_commons/azzas_app_commons.dart';

class WishlistItemMapper {

  static AddToWishlistItem fromProduct(Product product, int? index) {
    return AddToWishlistItem(
        itemId: product.productId,
        itemRef: product.productReferenceCode,
        itemName: product.productName,
        itemBrand: product.brand,
        itemCategory: product.saleOrCollection,
        itemCategory2: product.extractDeepestCategory,
        itemVariant: product.productColor,
        index: index,
        quantity: 1,
        price: double.parse(product.getPrice().toString()),
        discount: double.parse((product.getListPrice() - product.getPrice()).toString())
    );
  }

  static AddToWishlistItem fromOrderFormProduct(OrderFormProduct product, int? index) {
    return AddToWishlistItem(
        itemId: product.productId,
        itemRef: product.productRefId,
        itemName: product.productNameWithoutSkuName,
        itemBrand: product.productBrand,
        itemCategory: product.saleOrCollection,
        itemCategory2: product.extractMostSpecificCategoryFromMap,
        itemVariant: product.productColor,
        index: index,
        quantity: 1,
        price: product.priceDouble,
        discount: product.totalDiscount
    );
  }
}