import 'package:azzas_analytics/events/view_item/model/view_item_model.dart';
import 'package:azzas_core/commons/models/product.dart';

class ViewItemMapper {
  static ViewItemModel fromProduct(Product product) {
    return ViewItemModel(
      price: product.getPrice().toDouble(),
      quantity: 1,
      itemRef: product.productReferenceCode ?? '',
      itemBrand: product.brand ?? '',
      itemName: product.productName ?? '',
      itemCategory: product.saleOrCollection,
      itemVariant: product.productColor ?? '',
      itemCategory2: product.categories?.firstOrNull?.split('/')[1],
      itemId: product.productId ?? '',
      discount: (product.getListPrice() - product.getPrice()).toDouble(),
    );
  }
}
