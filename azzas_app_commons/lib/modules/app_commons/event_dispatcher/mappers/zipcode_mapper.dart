import 'package:azzas_app_commons/azzas_app_commons.dart';

class ZipcodeMapper {
static List<SearchZipCodeItem> fromPackageModel(List<PackageModel> packagesList ){
 List<SearchZipCodeItem> allDeliveryOptions = [];

  for (final package in packagesList) {
    for (final option in package.deliveryOptions) {
      final deliveryChannel = option.deliveryChannel == 'delivery'
          ? "receba em casa"
          : "retire na loja";
      final deliveryType = option.id;

      int? value = int.tryParse(option.shippingEstimate ?? '');
      final deliveryInDays = value == null ? null : (value / 24).ceil();

      final deliveryPrice =
          option.price == null ? null : (option.price! / 100);

      final item = SearchZipCodeItem(
        deliveryTime: deliveryInDays,
        shipping: deliveryPrice,
        lineItems: package.items.join(','),
        shippingTier: "$deliveryChannel: $deliveryType",
      );

      allDeliveryOptions.add(item);
    }
  }

  return allDeliveryOptions;
}

static List<SearchZipCodeItem> fromShippingOptions({
  required ShippingOptions shippingOptions,
}) {
  final List<SearchZipCodeItem> allDeliveryOptions = [];
  final deliveryData = shippingOptions.deliveryData;
  List<String> _lineItems = [];

  if (deliveryData != null && deliveryData.packs.isNotEmpty) {
    _lineItems =  deliveryData.packs
        .expand((pack) => pack.itemsInPack)
        .map((item) => item.id)
        .toList();
  }

  // Entregas em casa
  for (final option in shippingOptions.onePackAllOptions ?? []) {
    final deliveryTime = int.tryParse(option.shippingEstimate) ?? 0;
    final deliveryPrice = option.price / 100;

    allDeliveryOptions.add(
      SearchZipCodeItem(
        deliveryTime: deliveryTime,
        shipping: deliveryPrice,
        lineItems: _lineItems.join(','),
        shippingTier: 'receba em casa: ${option.title ?? 'desconhecido'}',
      ),
    );
  }

  // Retirada na loja
  for (final sla in shippingOptions.pickupInPoint?.pickupPoints ?? []) {
    final deliveryTime = int.tryParse(sla.shippingEstimate ?? '') ?? 0;

    allDeliveryOptions.add(
      SearchZipCodeItem(
        deliveryTime: deliveryTime,
        shipping: 0.0,
        lineItems: _lineItems.join(','),
        shippingTier: 'retire na loja: ${sla.friendlyName ?? 'Loja desconhecida'}',
      ),
    );
  }

  return allDeliveryOptions;
  }
}
