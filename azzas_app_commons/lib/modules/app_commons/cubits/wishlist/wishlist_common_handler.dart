import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:flutter/material.dart';

enum WishlistCase {
  // bottom sheet de exclusao
  pdp,
  pdc,
}

/// Handler para ser utilizado nas telas fora da Wishlist.
class WishlistHandler {
  WishlistHandler({
    required this.cubit,
  });
  final WishlistCommonCubit cubit;

  /// Handler completo. Avalia se o produto deve ser adicionado ou removido.
  /// Diferentes métodos de remoção conforme WishlistCase
  /// Emite feedbacks de adição/remoção do produto da wishlist
  Future<void> completeHandle({
    required BuildContext context,
    required Product product,
    int? index,
    FavoriteCallback? onFavoriteCallback,
    RemoveFavoriteCallback? onRemoveFavoriteCallback,
    required TickerProvider vsync,
    WishlistCase? wishlistCase,
  }) async {
    if (cubit.isProductInWishlist(product.productId)) {
      effectiveRemoveProduct(
        context: context,
        product: product,
        vsync: vsync,
        wishlistCase: wishlistCase,
        onRemoveFavoriteCallback: onRemoveFavoriteCallback,
      );
    } else {
      await addFavorite(
        context: context,
        product: product,
        index: index,
        onFavoriteCallback: onFavoriteCallback,
      );
    }
  }

  /// Define o método de remoção do produto
  Future<void> effectiveRemoveProduct({
    required BuildContext context,
    required Product product,
    RemoveFavoriteCallback? onRemoveFavoriteCallback,
    required TickerProvider vsync,
    WishlistCase? wishlistCase,
  }) async {
    final brand = Modular.get<AppConfig>().brand;
    if (brand == Brand.farm &&
        (wishlistCase == WishlistCase.pdp ||
            wishlistCase == WishlistCase.pdc)) {
      _showBottomSheetRemoveProduct(
        context: context,
        product: product,
        onRemoveFavoriteCallback: onRemoveFavoriteCallback,
        vsync: vsync,
      );
    } else {
      await _removeFavorite(
        context: context,
        product: product,
        onRemoveFavoriteCallback: onRemoveFavoriteCallback,
      );
      _showAzzasSnackBar(
          message: _getEffectiveRemoveProductMessage(), context: context);
    }
  }

  /// Adicona produto aos favoritos e emite feedback.
  Future<void> addFavorite({
    required Product product,
    FavoriteCallback? onFavoriteCallback,
    required BuildContext context,
    int? index,
    bool showFeedback = true,
  }) async {
    try {
      final _eventDispatcher = Modular.get<EventDispatcher>();
      await cubit.addProduct(product.productId ?? '');
      if (showFeedback) {
        _showAzzasSnackBar(message: _getEffectiveMessage(), context: context);
      }
      onFavoriteCallback?.call(product);
      _eventDispatcher.logAddToWishlist(
        product: product,
        index: index,
      );
    } catch (error) {
      _showError(context);
    }
  }

  /// Remove produto da wishlist
  Future<void> _removeFavorite(
      {required BuildContext context,
      RemoveFavoriteCallback? onRemoveFavoriteCallback,
      required Product product}) async {
    try {
      await cubit.removeProduct(product.productId ?? '');
      onRemoveFavoriteCallback?.call(product);
    } catch (error) {
      _showError(context);
    }
  }

  /// Exibe o bottom sheet para confirmar remoção do produto da wishlist.
  Future<void> _showBottomSheetRemoveProduct({
    required BuildContext context,
    required Product product,
    RemoveFavoriteCallback? onRemoveFavoriteCallback,
    required TickerProvider vsync,
  }) async {
    return showAzzasBottomSheet(
        vsync: vsync,
        context: context,
        builder: (_) {
          return BottomSheetRemoveProduct(
            onTapRemoveProduct: () => _removeFavorite(
              context: context,
              product: product,
              onRemoveFavoriteCallback: onRemoveFavoriteCallback,
            ),
          );
        });
  }

  /// Mensagem de remoção do produto da wishlist conforme marca
  String _getEffectiveRemoveProductMessage() {
    final brand = Modular.get<AppConfig>().brand;
    final effectiveMessage = brand == Brand.farm
        ? 'Produto removido dos desejos em desejos'
        : 'Produto removido com sucesso!';
    return effectiveMessage;
  }

  /// Mensagem de adição do produto da wishlist conforme marca
  String _getEffectiveMessage() {
    final brand = Modular.get<AppConfig>().brand;
    final effectiveMessage = brand == Brand.farm
        ? 'Produto salvo em desejos'
        : 'Produto adicionado com sucesso!';
    return effectiveMessage;
  }

  /// Exibe feedback de erro
  _showError(BuildContext context) {
    final theme = AzzasThemeProvider.of(context).snackBar;
    _showAzzasSnackBar(
      message: 'Erro ao realizar operação!',
      backgroundColor: theme.dangerColor,
      context: context,
    );
  }

  _showAzzasSnackBar(
      {required String message,
      Color? backgroundColor,
      required BuildContext context}) {
    final theme = AzzasThemeProvider.of(context).snackBar;

    AzzasSnackBar.show(
      context: context,
      message: message,
      backgroundColor: backgroundColor ?? theme.successColor,
    );
  }
}
