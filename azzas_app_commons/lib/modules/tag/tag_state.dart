import 'package:azzas_app_commons/azzas_app_commons.dart';

class TagState {
  final List<CmsComponent> components;

  final List<AzzasProductsTagsModel>? productsTags;
  final List<AzzasProductsTagsModel>? etcProductsTagsList;
  final List<StampTagCmsComponent>? stampTags;
  final List<StampTagProductCmsComponent>? stampTagsProduct;
  final List<ProductStampCmsComponent>? productStamp;

  TagState({
    this.components = const [],
    this.productsTags = const [],
    this.stampTags = const [],
    this.stampTagsProduct = const [],
    this.etcProductsTagsList = const [],
    this.productStamp = const [],
  });

  TagState copyWith({
    List<CmsComponent>? components,
    List<AzzasProductsTagsModel>? productsTags,
    List<AzzasProductsTagsModel>? etcProductsTagsList,
    List<StampTagCmsComponent>? stampTags,
    List<StampTagProductCmsComponent>? stampTagsProduct,
    List<ProductStampCmsComponent>? productStamp,
  }) {
    return TagState(
      components: components ?? this.components,
      productsTags: productsTags ?? this.productsTags,
      stampTags: stampTags ?? this.stampTags,
      stampTagsProduct: stampTagsProduct ?? this.stampTagsProduct,
      etcProductsTagsList: etcProductsTagsList ?? this.etcProductsTagsList,
      productStamp: productStamp ?? this.productStamp,
    );
  }
}
