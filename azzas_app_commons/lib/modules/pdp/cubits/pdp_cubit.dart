import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:flutter/material.dart';

class PdpCubit extends Cubit<PdpState> {
  final GetSimilarProductsUseCase _getSimilarProductsUseCase;
  final GetSimilarProductsByRefIdUseCase _getSimilarProductsByRefIdUseCase;
  final GetProductsByIdsUseCase _getProductsByIdsUseCase;
  final WarnMeUseCase _warnMeUseCase;
  final OrderCheckoutHandler _orderCheckoutHandler =
      Modular.get<OrderCheckoutHandler>();
  final _cmsService = Modular.get<CmsService>();
  final GetProductLookSuggestionsUseCase _getProductLookSuggestionsUseCase =
      Modular.get<GetProductLookSuggestionsUseCase>();

  final SimulateShippingOptionsUseCase _simulateShippingOptionsUseCase;
  final _eventDispatcher = Modular.get<EventDispatcher>();

  PdpCubit({
    required GetSimilarProductsUseCase getSimilarProductsUseCase,
    required GetSimilarProductsByRefIdUseCase getSimilarProductsByRefIdUseCase,
    required GetProductsByIdsUseCase getProductsByIdsUseCase,
    required WarnMeUseCase warnMeUseCase,
    required SimulateShippingOptionsUseCase simulateShippingOptionsUseCase,
  })  : _getSimilarProductsUseCase = getSimilarProductsUseCase,
        _getSimilarProductsByRefIdUseCase = getSimilarProductsByRefIdUseCase,
        _getProductsByIdsUseCase = getProductsByIdsUseCase,
        _warnMeUseCase = warnMeUseCase,
        _simulateShippingOptionsUseCase = simulateShippingOptionsUseCase,
        super(PdpState.initial());

  /// Seleciona uma cor do produto
  Future<void> onColorSelected(Product product) async {
    _resetStateForNewProduct(product);

    if (_hasValidProductId(product)) {
      try {
        /// Busca o produto atualizado, isso é necessário pois o Product Model
        /// do similar products não possui todas as informações para os tamanhos
        final updatedProduct = await _fetchProductById(product.productId!);
        emit(state.copyWith(productSelected: updatedProduct));
        await getSimilarProductsByProductId(updatedProduct.productId!);
      } catch (e, st) {
        _handleError(e, st);
      }
    }
  }

  /// Atualiza o produto selecionado
  void fetchSelectedProduct(Product product) {
    _resetStateForNewProduct(product);

    if (_hasValidProductId(product)) {
      getSimilarProductsByProductId(product.productId!);
    }
  }

  /// Define o tamanho do produto selecionado
  void setProductSize(OrderFormItem? size) {
    emit(state.copyWith(selectedSize: size));
  }

  /// Busca produtos similares usando o [productId]
  Future<void> getSimilarProductsByProductId(String productId) async {
    await _fetchSimilarProducts(
      fetcher: () =>
          _getSimilarProductsUseCase(productId: int.parse(productId)),
    );
  }

  /// Busca produtos similares usando o [referenceId]
  Future<void> getSimilarProductsByReferenceId(String referenceId) async {
    await _fetchSimilarProducts(
      fetcher: () => _getSimilarProductsByRefIdUseCase(refId: referenceId),
    );
  }

  /// Busca produtos similares usando o [referenceId]
  Future<void> getProductLookSuggestions(String productId) async {
    List<String> suggestionsProductsIds = [];
    try {
      await _fetchSimilarProducts(
        fetcher: () =>
            _getProductLookSuggestionsUseCase(productId: int.parse(productId)),
        hasSuggestionProducts: true,
      );

      for (var suggestionProduct
          in state.similarProductsState.suggestionsProducts!) {
        suggestionsProductsIds.add(suggestionProduct.productId ?? '');
      }
      emit(
        state.copyWith(
          similarProductsState: state.similarProductsState.copyWith(
            suggestionsProductsIds: suggestionsProductsIds,
          ),
        ),
      );
    } catch (e, st) {
      _handleError(e, st);
      _updateSimilarProductsStatus(hasError: true);
    }
  }

  /// Busca produtos similares com base no fetcher
  Future<void> _fetchSimilarProducts({
    required Future<List<Product>> Function() fetcher,
    bool hasSuggestionProducts = false,
  }) async {
    _updateSimilarProductsStatus(isLoading: true);

    try {
      final response = await fetcher();
      final filteredProducts = _filterAndAddUniqueProducts(response);

      if (hasSuggestionProducts) {
        _updateSimilarProductsStatus(suggestionsProducts: filteredProducts);
      } else {
        _updateSimilarProductsStatus(similarProducts: filteredProducts);
      }
    } catch (e, st) {
      _handleError(e, st);
      _updateSimilarProductsStatus(hasError: true);
    }
  }

  /// Atualiza o estado de produtos similares
  void _updateSimilarProductsStatus({
    bool isLoading = false,
    bool hasError = false,
    List<Product> similarProducts = const [],
    List<Product>? suggestionsProducts = const [],
  }) {
    emit(
      state.copyWith(
        similarProductsState: state.similarProductsState.copyWith(
          loading: isLoading,
          error: hasError,
          similarProducts: similarProducts,
          suggestionsProducts: suggestionsProducts,
        ),
      ),
    );
  }

  /// Filtra produtos para remover duplicatas e valida sua disponibilidade
  List<Product> _filterAndAddUniqueProducts(List<Product> products) {
    final productMap = <String, Product>{};

    for (final product in products) {
      final id = product.productId;
      if (id != null && _isAvailableInDefaultSeller(product)) {
        productMap[id] = product;
      }
    }

    return productMap.values.toList();
  }

  /// Verifica se o produto está disponível no vendedor padrão
  bool _isAvailableInDefaultSeller(Product product) {
    return product.firstAvailableSeller?.commertialOffer?.isAvailable ?? false;
  }

  /// Verifica se o produto é o mesmo que já está selecionado
  bool _isSameProduct(Product product) {
    return product == state.productSelected;
  }

  /// Verifica se o produto possui um ID válido
  bool _hasValidProductId(Product product) {
    return product.productId?.isNotEmpty ?? false;
  }

  /// Reseta o estado para um novo produto
  void _resetStateForNewProduct(Product product) {
    if (_isSameProduct(product)) return;

    emit(state.copyWith(productSelected: product, selectedSize: null));
    _updateSimilarProductsStatus(similarProducts: []);
  }

  /// Busca detalhes do produto pelo ID
  Future<Product> _fetchProductById(String productId) async {
    final products =
        await _getProductsByIdsUseCase(ids: [int.parse(productId)]);
    return products.first;
  }

  /// Envia um aviso de disponibilidade
  Future<void> warnMe({
    required String name,
    required String email,
    required String skuId,
  }) async {
    try {
      _updateWarnMeStatus(isLoading: true);

      await _warnMeUseCase(
        WarnMe(
          name: name,
          email: email,
          skuId: skuId,
        ),
      );

      _updateWarnMeStatus(success: true);
    } catch (e, st) {
      _updateWarnMeStatus(hasError: true);
      _handleError(e, st);
    }
  }

  /// Atualiza o estado de aviso de disponibilidade
  void _updateWarnMeStatus({
    bool isLoading = false,
    bool hasError = false,
    bool success = false,
  }) {
    emit(
      state.copyWith(
        warnMeState: state.warnMeState.copyWith(
          loading: isLoading,
          error: hasError,
          success: success,
        ),
      ),
    );
  }

  void resertWarnMeStatus() {
    emit(
      state.copyWith(
        warnMeState: state.warnMeState.copyWith(
          loading: false,
          error: false,
          success: false,
        ),
      ),
    );
  }

  /// Simula opções de frete
  Future<void> simulateShippingOptions({
    required String itemId,
    required String seller,
    required String postalCode,
  }) async {
    try {
      updateSimulateShippingStatus(isLoading: true);

      final response = await _simulateShippingOptionsUseCase(
        postalCode: postalCode,
        products: [
          SimulationProduct(
            id: itemId,
            seller: seller,
          ),
        ],
      );

      updateSimulateShippingStatus(shippingOptions: response);

      _eventDispatcher.logSearchZipCode(
        zipCode: postalCode,
        flagPickup: (state.simulateShippingState.shippingOptions?.pickupInPoint?.pickupPoints ?? []).isNotEmpty,
        region: 'pdp',
        shippingOptions: state.simulateShippingState.shippingOptions,
      );
    } catch (e, st) {
      updateSimulateShippingStatus(hasError: true);
      _handleError(e, st);
    }
  }

  /// Atualiza o estado de simulação de frete
  void updateSimulateShippingStatus({
    bool isLoading = false,
    bool hasError = false,
    ShippingOptions? shippingOptions,
  }) {
    emit(
      state.copyWith(
        simulateShippingState: state.simulateShippingState.copyWith(
          loading: isLoading,
          error: hasError,
          shippingOptions: shippingOptions,
        ),
      ),
    );
  }

  /// Lida com erros de maneira centralizada
  void _handleError(Object e, StackTrace st) {
    debugPrint(e.toString());
    debugPrintStack(stackTrace: st);
  }

  Future<void> addItem(
      {required List<OrderAddItem> orderAddItem, bool? purchaseNow}) async {
    if (purchaseNow == false) {
      updatePDPBagState(isLoading: true);
    } else {
      updatePDPBagState(purchaseNow: true, success: true);
    }

    try {
      await _orderCheckoutHandler.addItems(items: orderAddItem);
    } catch (e, stackTrace) {
      Future.delayed(const Duration(seconds: 2), () {
        updatePDPBagState(isLoading: false, hasError: true);
      });
      debugPrint(e.toString());
      debugPrintStack(stackTrace: stackTrace);
    } finally {
      updatePDPBagState(isLoading: false, success: true, purchaseNow: false);
    }
  }

  void updatePDPBagState({
    bool isLoading = false,
    bool hasError = false,
    bool success = false,
    bool purchaseNow = false,
  }) {
    emit(
      state.copyWith(
        pdpBagState: state.pdpBagState.copyWith(
            loading: isLoading,
            error: hasError,
            success: success,
            purchaseNow: purchaseNow),
      ),
    );
  }

  void updateShowCardWarnMe(bool value) {
    emit(state.copyWith(
        warnMeState: state.warnMeState.copyWith(
      showCardWarnMe: value,
    )));
  }

  void loadCmsPdpComponents(String documentType) async {
    final pdpComponents =
        await _cmsService.loadComponentsForDocument(documentType);
    if (pdpComponents != null) {
      emit(state.copyWith(
        pdpCmsComponents: pdpComponents.toList(),
      ));
    }
  }

  void setInitialItemsinFullLook(List<OrderFormItem> itemsList) {
    emit(
      state.copyWith(
        similarProductsState: state.similarProductsState.copyWith(
          activeFullLookItems: itemsList,
        ),
      ),
    );
  }

  void changeSize(OrderFormItem newItem) {
    final activeItems = state.similarProductsState.activeFullLookItems;
    if (activeItems == null) return;
    final item = activeItems
        .firstWhere((i) => i.complementName == newItem.complementName);
    final itemIndex = activeItems.indexOf(item);
    if (itemIndex == -1) {
      return;
    }
    activeItems[itemIndex] = newItem;
  }
}
