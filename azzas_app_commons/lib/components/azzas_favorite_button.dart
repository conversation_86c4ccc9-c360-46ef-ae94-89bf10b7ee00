import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:flutter/material.dart';

typedef FavoriteCallback = void Function(Product product);
typedef RemoveFavoriteCallback = void Function(Product product);

class AzzasFavoriteButton extends StatefulWidget {
  const AzzasFavoriteButton({
    super.key,
    required this.product,
    this.onFavoriteCallback,
    this.onRemoveFavoriteCallback,
    this.index,
    this.buttonHeight,
    this.buttonWidth,
    this.iconSize,
    this.decoration,
    this.alignment,
    this.wishlistCase,
  });

  final Product product;
  final FavoriteCallback? onFavoriteCallback;
  final RemoveFavoriteCallback? onRemoveFavoriteCallback;
  final int? index;
  final double? buttonHeight;

  final double? buttonWidth;
  final WishlistCase? wishlistCase;

  final double? iconSize;
  final BoxDecoration? decoration;
  final AlignmentGeometry? alignment;

  @override
  State<AzzasFavoriteButton> createState() => _AzzasFavoriteButtonState();
}

class _AzzasFavoriteButtonState extends State<AzzasFavoriteButton>
    with TickerProviderStateMixin {
  final wishlistCommonCubit = Modular.get<WishlistCommonCubit>();
  final wishlistHandler = Modular.get<WishlistHandler>();
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<WishlistCommonCubit, WishlistCommonState>(
      bloc: wishlistCommonCubit,
      builder: (context, state) {
        bool isProductInWishlist =
            wishlistCommonCubit.isProductInWishlist(widget.product.productId!);

        return AzzasFavoriteButtonBase(
            params: AzzasFavoriteButtonParams(
                buttonHeight: widget.buttonHeight,
                buttonWidth: widget.buttonWidth,
                iconSize: widget.iconSize,
                isActive: isProductInWishlist,
                decoration: widget.decoration,
                alignment: widget.alignment,
                onTap: () => wishlistHandler.completeHandle(
                      context: context,
                      index: widget.index,
                      product: widget.product,
                      vsync: this,
                      onFavoriteCallback: widget.onFavoriteCallback,
                      onRemoveFavoriteCallback: widget.onRemoveFavoriteCallback,
                      wishlistCase: widget.wishlistCase,
                    )));
      },
    );
  }
}
