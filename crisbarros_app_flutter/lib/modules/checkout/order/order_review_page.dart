import 'package:azzas_analytics/services/analytics_service.dart';
import 'package:azzas_analytics/events/navigation/navigation_events.dart';
import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:crisbarros_app_flutter/config/design_tokens/tokens.dart';
import 'package:crisbarros_app_flutter/modules/checking_account/widgets/checkout_balance_feedback.dart';
import 'package:crisbarros_app_flutter/modules/checkout/order/widgets/order_review_delivery_packages.dart';
import 'package:crisbarros_app_flutter/modules/checkout/order/widgets/order_review_payment.dart';
import 'package:flutter/material.dart';

class OrderReviewPage extends StatefulWidget {
  const OrderReviewPage({super.key});

  @override
  State<OrderReviewPage> createState() => _OrderReviewPageState();
}

class _OrderReviewPageState extends State<OrderReviewPage>
    with TickerProviderStateMixin {
  final orderFormCubit = Modular.get<OrderFormCubit>();
  final mainPageCubit = Modular.get<MainPageCubit>();
  final paymentCubit = Modular.get<PaymentCubit>();
  final _eventDispatcher = Modular.get<EventDispatcher>();

  //TODO: Todos esses estados devem ser movidos para um cubit
  final GlobalKey<OrderReviewPaymentState> orderReviewPaymentState =
      GlobalKey<OrderReviewPaymentState>();

  final cvvController = TextEditingController();
  bool isLoading = false;
  bool isLoadingTransaction = false;

  @override
  void initState() {
    _eventDispatcher.logOrderReviewed();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<OrderFormCubit, OrderFormState>(
      bloc: orderFormCubit,
      builder: (context, state) {
        String itemsQuantityText() {
          return orderFormCubit.orderForm.getItemsQuantity! > 1
              ? '${orderFormCubit.orderForm.getItemsQuantity} produtos'
              : '1 produto';
        }

        return Scaffold(
          body: CustomScrollView(
            scrollDirection: Axis.vertical,
            slivers: [
              AzzasDefaultAppBar(
                title: 'Revise e feche o seu pedido',
                titleTextStyle: Tokens.typography.body.medium.mediumRegular,
                pinned: false,
                prefixWidget: IconButton(
                  onPressed: () {
                    Modular.to.popUntil(ModalRoute.withName('/'));
                    Modular.to.pushNamed('/', arguments: null);
                    mainPageCubit.switchTab(MainPageTab.bag);
                  },
                  iconSize: 32,
                  icon: Icon(Tokens.icons.navigation.left),
                ),
              ),
              SliverToBoxAdapter(
                child: Column(
                  children: [
                    Padding(
                      padding: EdgeInsets.symmetric(
                          horizontal: Tokens.spacing.spacingMedium),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SizedBox(height: Tokens.spacing.spacingXLarge),
                          AzzasListItemButton(
                            buttonText: 'Editar',
                            onTap: () async {
                              await AnalyticsService.trackEvent(
                                  'select_content', {
                                'content_type': 'editar-pedido',
                              });
                              Modular.to.pushNamed('/bag');
                            },
                            title: "Resumo do pedido",
                            subtitle: itemsQuantityText(),
                          ),
                          OrderReviewDeliveryPackages(
                              orderFormCubit: orderFormCubit),
                          SizedBox(height: Tokens.spacing.spacingXLarge),
                          const AzzasLine(),
                          SizedBox(height: Tokens.spacing.spacingXLarge),
                          OrderReviewPayment(
                            key: orderReviewPaymentState,
                          ),
                          SizedBox(height: Tokens.spacing.spacingXLarge),
                          CheckoutBalanceFeedback(
                            checkoutStep: CheckoutStep.review,
                          ),
                        ],
                      ),
                    ),
                    AzzasCheckoutResume(
                      boxVerticalSpace: const EdgeInsets.all(20),
                      title: itemsQuantityText(),
                      totalAmount: CurrencyHelper.format(
                        amount: orderFormCubit.orderForm.getTotalItemsValue,
                        dividedBy100: true,
                      ),
                      totalDesc: orderFormCubit.orderForm.getCurrentInstallment,
                      totalTitle: "Total",
                      deliveryType: orderFormCubit.orderForm.shippingPrice,
                      deliveryTitle: 'Entrega',
                      submitLabel: 'Finalizar compra',
                      onButtonPressed: finishOrder,
                      isLoading: isLoading,
                      discountTitle: orderFormCubit.orderForm.hasDiscount
                          ? 'Descontos'
                          : null,
                      discountValue: orderFormCubit.orderForm.hasDiscount
                          ? orderFormCubit.orderForm.discountPrice
                          : null,
                      giftCardTitle: 'Saída "Saldo em créditos”',
                      giftCardValue: orderFormCubit
                              .orderForm.paymentData!.giftCards!.isNotEmpty
                          ? CurrencyHelper.format(
                              amount:
                                  orderFormCubit.orderForm.getGiftCardValues(),
                              dividedBy100: true,
                            )
                          : null,
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  //TODO: Reminder to move all the logics below to an cubit in azzas_app_commons
  void finishOrder() async {
    setState(() => isLoading = true);

    //Adiciona os dados de sessão do usuario no orderForm
    try {
      await orderFormCubit.updateAnalyticsData();
    } catch (e) {
      debugPrint('Error updating analytics data: $e');
    }

    if (orderFormCubit.state.orderForm?.orderFormId == null) return;
    if (orderFormCubit.state.orderForm?.paymentData == null) return;

    paymentCubit.clearCreditCardError();

    if (paymentCubit.isPixSelected) {
      await finishPixPayment();
    } else {
      if (paymentCubit.state.creditCardInfo.cvv.isNullOrEmpty &&
          paymentCubit.state.selectedPaymentType ==
              SelectedPaymentType.creditCard) {
        setState(() => isLoading = false);
        return _showCompleteCvvCardInfo(
          cardInfo: paymentCubit.state.creditCardInfo,
        );
      }
      await finishCreditCardPayment();
    }

    setState(() => isLoading = false);
  }

  void _showCompleteCvvCardInfo({required CreditCardInfoData cardInfo}) {
    final cardFromPaymentSystemId = CreditCardHelper.getTypeByPaymentSystem(
      cardInfo.selectedPaymentSystemId ?? '',
    ).toAzzasCreditCardType();

    final brandIcon = AzzasCardHelper.getCardIconByCardType(
            cardType: cardFromPaymentSystemId, context: context) ??
        const AssetImage('');

    showAzzasBottomSheet(
      context: context,
      onDismiss: () {
        paymentCubit.clearCvv();
      },
      builder: (_) {
        return BlocBuilder<OrderFormCubit, OrderFormState>(
          bloc: orderFormCubit,
          builder: (context, state) {
            final installmentsLabel =
                state.orderForm?.getCurrentInstallment ?? "";

            return StatefulBuilder(builder: (context, setState) {
              return AzzasCompleteCardInfoBottomSheet(
                  onTapConfirm: () async {
                    paymentCubit.copyCreditCardInfo(
                      CreditCardInfoData(
                        cvv: cvvController.text,
                      ),
                    );
                    setState(() => isLoadingTransaction = true);
                    AzzasAnalyticsEvents.logSelectContent(
                      contentType: 'confirmar-pagamento',
                    );
                    await finishCreditCardPayment();
                    setState(() => isLoadingTransaction = false);
                  },
                  onTapSelectInstallments: () {
                    orderReviewPaymentState.currentState
                        ?.showInstallmentsBottomSheet(context);
                  },
                  isLoading: isLoadingTransaction,
                  titleStyle: Tokens.typography.headings.small.smallRegular,
                  cvvController: cvvController,
                  cardFinalNumber: cardInfo.finalNumber,
                  cardBrandIcon: brandIcon,
                  cardPaymentLabelStyle: Tokens
                      .typography.body.extraSmall.extraSmallRegular
                      .copyWith(
                    color: Tokens.colors.typography.medium,
                  ),
                  onCloseTap: () {
                    Navigator.of(context).pop();
                  },
                  installmentsStyle:
                      Tokens.typography.body.extraSmall.extraSmallRegular,
                  installmentsLabel: installmentsLabel,
                  cardFinalNumberStyle:
                      Tokens.typography.body.extraSmall.extraSmallRegular);
            });
          },
        );
      },
      vsync: this,
    );
  }

  Future<void> finishCreditCardPayment() async {
    final paymentTransactionUseCase = Modular.get<PaymentTransactionUseCase>();

    final recaptchaResult = await _getRecaptchaChecker();

    List<TransactionPayment> params = [];
    late final TransactionPaymentCreditCardFields fields;
    final payment = orderFormCubit.orderForm.firstSelectedPayment;
    var giftCards = paymentCubit.getGiftcardsTransaction(null);

    if (giftCards.isNotEmpty) {
      params.add(TransactionPayment(giftCard: giftCards));
    }

    for (final merchant in orderFormCubit.orderForm.merchantSellerPayments) {
      TransactionPaymentCreditCard? creditCard;
      if (payment != null) {
        if (paymentCubit.state.creditCardInfo.isNew == true) {
          fields = TransactionPaymentCreditCardFields(
            cardNumber: paymentCubit.state.creditCardInfo.cardNumber,
            holderName: paymentCubit.state.creditCardInfo.cardHolderName,
            validationCode: paymentCubit.state.creditCardInfo.cvv,
            dueDate: paymentCubit.state.creditCardInfo.expirationDate,
            address: paymentCubit
                .state.creditCardInfo.availableBillingAddresses?.firstOrNull,
            addressId:
                paymentCubit.state.creditCardInfo.availableBillingAddresses ==
                        null
                    ? orderFormCubit.orderForm.shippingData!.selectedAddresses!
                        .first.addressId!
                    : null,
          );
        } else {
          fields = TransactionPaymentCreditCardFields(
            bin: paymentCubit.state.creditCardInfo.bin,
            accountId: paymentCubit.state.creditCardInfo.cardAccountId,
            addressId: orderFormCubit
                .orderForm.shippingData?.selectedAddresses?.first.addressId,
            validationCode: paymentCubit.state.creditCardInfo.cvv,
          );
        }
        creditCard = TransactionPaymentCreditCard(
          paymentSystem: payment.paymentSystem,
          value: merchant.value,
          referenceValue: merchant.referenceValue,
          interestValue: 0,
          hasDefaultBillingAddress: true,
          installmentsInterestRate: 0,
          fields: fields,
          isBillingAddressDifferent: false,
          installments: merchant.installments == 0 ? 1 : merchant.installments,
          interestRate: 0,
          installmentValue: merchant.installmentValue,
          chooseToUseNewCard:
              paymentCubit.state.creditCardInfo.isNew == true ? true : false,
          currencyCode: 'BRL',
          originalPaymentIndex: 0,
          groupName: 'creditCardPaymentGroup',
        );
      }
      params.add(TransactionPayment(creditCard: creditCard));
    }

    try {
      var response = await paymentTransactionUseCase(
        orderFormId: orderFormCubit.orderForm.orderFormId!,
        transactionPaymentSelect: params,
        recaptchaKey: recaptchaResult.key,
        recaptchaToken: recaptchaResult.token,
      );

      if (response.isNotEmpty) {
        final orderGroup = response.first;
        Modular.to.pushNamed("/order_completed", arguments: orderGroup);
      }

      return;
    } on CreditCardValidationException catch (e, s) {
      paymentCubit.setCreditCardError(paymentCubit.state.creditCardInfo);

      if (mounted) {
        Modular.to.pushNamed('/checkout/select-payment');
      }

      _eventDispatcher.logError(
        message:
            'Erro pagamento Cartão: ${PaymentErrorType.notAuthorizedCreditCard.getFormattedError()}',
        eventName: 'purchase',
        error: e,
        stackTrace: s,
      );
    } catch (e, s) {
      final isTooManyRequestsError =
          e.runtimeType == TooManyOrderRequestsException;
      if (mounted) {
        _showAzzasSnackBar(
          context: context,
          message: e.toString(),
          isError: true,
          padding: isTooManyRequestsError
              ? EdgeInsets.all(
                  Tokens.spacing.spacingSmall,
                )
              : null,
        );
      }

      _eventDispatcher.logError(
        message:
        'Erro pagamento Cartão: ${PaymentErrorType.notAuthorizedCreditCard.getFormattedError()}',
        eventName: 'purchase',
        error: e,
        stackTrace: s,
      );
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

  Future<void> finishPixPayment() async {
    String? pixUrl;
    String? orderGroup;
    try {
      final response = await _createOrder(orderForm: orderFormCubit.orderForm);
      final transactionId = response.merchantTransactions!.first.transactionId!;
      final merchantName = response.merchantTransactions!.first.merchantName!;
      orderGroup = response.orderGroup!;

      await _pixTransaction(
        orderForm: orderFormCubit.orderForm,
        orderGroup: orderGroup,
        transactionId: transactionId,
        merchantName: merchantName,
      );

      final token = await _generateCodePixSecondCall(
        orderForm: orderFormCubit.orderForm,
        orderGroup: orderGroup,
      );

      final url = token.paymentAuthorizationAppCollection!.first.appPayload;
      final isPixInstallments =
          orderFormCubit.orderForm.firstSelectedPayment?.paymentSystem ==
              PaymentSystemIds.pixInstallments;

      if (isPixInstallments && url != null) {
        pixUrl = '$url&cd=/cancel';
      } else {
        pixUrl = url ?? '';
      }
    } catch (e, s) {
      if (mounted) {
        _showAzzasSnackBar(
          context: context,
          message: e.toString(),
          isError: true,
        );
      }
      setState(() => isLoading = false);

      _eventDispatcher.logError(
        message: 'Erro pagamento PIX: $e',
        eventName: 'purchase',
        error: e,
        stackTrace: s,
      );
    }

    if (pixUrl != null) _openPixWebView(url: pixUrl, orderGroup: orderGroup);
  }

  Future<CreateOrder> _createOrder({required OrderForm orderForm}) async {
    final createOrderUseCase = Modular.get<CreateOrderUseCase>();
    if (orderForm.orderFormId == null) throw Exception();

    try {
      final recaptchaResult = await _getRecaptchaChecker();
      var response = await createOrderUseCase(
        orderFormId: orderForm.orderFormId!,
        value: int.parse(orderForm.value!.toString()),
        interestValue: 0,
        recaptchaKey: recaptchaResult.key,
        recaptchaToken: recaptchaResult.token,
      );
      return response;
    } catch (e) {
      throw Exception();
    }
  }

  Future<void> _pixTransaction({
    required OrderForm orderForm,
    required String transactionId,
    required String merchantName,
    required String orderGroup,
  }) async {
    final pixTransactionUseCase = Modular.get<PixTransactionUseCase>();
    if (orderForm.firstSelectedPayment == null) return;

    final payment = orderForm.firstSelectedPayment!;

    final transaction = TransactionPaymentTransaction(
      id: transactionId,
      merchantName: merchantName,
    );

    final pix = TransactionPaymentPix(
      paymentSystem: payment.paymentSystem,
      value: payment.value,
      paymentSystemName: payment.paymentSystem,
      referenceValue: payment.referenceValue,
      installmentValue: payment.merchantSellerPayments.first.installmentValue,
      id: payment.merchantSellerPayments.first.id,
      transaction: transaction,
    );
    var giftCards = paymentCubit.getGiftcardsTransaction(transaction);

    final params = TransactionPayment(pix: pix, giftCard: giftCards);

    await pixTransactionUseCase.call(
      transactionId: transactionId,
      transactionPaymentSelect: params,
      orderId: orderGroup,
    );
  }

  Future<GenerateCodePix> _generateCodePixSecondCall({
    required OrderForm orderForm,
    required String orderGroup,
  }) async {
    final generateCodePixSecondCallUseCase =
        Modular.get<GenerateCodePixSecondCallUseCase>();

    return await generateCodePixSecondCallUseCase.call(
      orderGroupId: orderGroup,
    );
  }

  void _openPixWebView({required String url, required String? orderGroup}) {
    NavigatorDynamic.call(
      "webview",
      arguments: WebViewParams(
        url: url,
        showNavBar: false,
        avoidKeyboard: false,
        onUpdateVisitedHistory: (controller, uri, _) {
          if (uri?.path == "/pix-finish") {
            Future.delayed(const Duration(milliseconds: 500), () {
              controller.goBack();
              Modular.to.pushNamed(
                "/order_completed",
                arguments: orderGroup,
              );
            });
          }
          if (uri?.path == "/pix-fail" || uri?.path == "/cancel") {
            Future.delayed(const Duration(milliseconds: 500), () {
              controller.goBack();
              if (mounted) Navigator.pop(context);
            });
          }
        },
      ),
    );
  }

  Future<RecaptchaResult> _getRecaptchaChecker() async {
    //TODO Change to get CB keys from app config
    final recaptchaResult = await RecaptchaChecker(
      "6LfvVcsqAAAAAA0cTMofemIoZ68ZvYT1jI36qabz",
      "6Leo08YqAAAAADvwYu9YpnaDBRFPHasHbelYezO-",
    ).check(
      'pix_transaciton',
    );
    return recaptchaResult;
  }

  void _showAzzasSnackBar({
    required BuildContext context,
    required String message,
    bool isError = false,
    EdgeInsets? padding,
  }) {
    AzzasSnackBar.show(
      context: context,
      message: message,
      padding: padding,
      backgroundColor:
          isError ? Tokens.colors.error.pure : Tokens.colors.success.pure,
      maxLines: 4,
      duration: Duration(seconds: 3),
    );
  }
}
