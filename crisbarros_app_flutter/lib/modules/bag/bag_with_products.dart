import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:crisbarros_app_flutter/config/design_tokens/tokens.dart';
import 'package:crisbarros_app_flutter/modules/bag/bag_add_coupons.dart';
import 'package:crisbarros_app_flutter/modules/bag/bag_button_close_purchase.dart';
import 'package:crisbarros_app_flutter/modules/bag/bag_delivery.dart';
import 'package:crisbarros_app_flutter/modules/bag/bag_list_products.dart';
import 'package:crisbarros_app_flutter/modules/bag/bag_seller_code.dart';
import 'package:crisbarros_app_flutter/modules/bag/bag_send_gift.dart';
import 'package:crisbarros_app_flutter/modules/bag/bottom_sheet/bag_product_unavailable_bottom_sheet.dart';
import 'package:crisbarros_app_flutter/modules/bag/widgets/unavailable_items.dart';
import 'package:crisbarros_app_flutter/modules/main/main_page.dart';
import 'package:crisbarros_app_flutter/modules/wishlist/pages/widgets/checkout_wishlist.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';

import '../checking_account/widgets/widgets.dart';

class BagWithProducts extends StatefulWidget {
  final BagPageCubit bagPageCubit;
  final OrderFormCubit orderFormCubit;
  final bool scrollToAddress;

  const BagWithProducts({
    super.key,
    required this.bagPageCubit,
    required this.orderFormCubit,
    this.scrollToAddress = false,
  });

  @override
  State<BagWithProducts> createState() => _BagWithProductsState();
}

class _BagWithProductsState extends State<BagWithProducts>
    with TickerProviderStateMixin, ProductGestureCounter {
  final CmsService cmsService = Modular.get<CmsService>();
  final OrderCheckoutHandler _orderCheckoutHandler =
      Modular.get<OrderCheckoutHandler>();
  final WishlistCommonCubit _wishlistCommonCubit =
      Modular.get<WishlistCommonCubit>();
  final EventDispatcher _eventDispatcher = Modular.get<EventDispatcher>();
  final ScrollController _scrollController = ScrollController();
  final storage = GetKeyValueStorage();

  bool isLoaded = false;
  List<CmsComponent> components = [];
  bool cameFromPdp = false;
  bool _hasLoggedViewCart = false;

  @override
  void initState() {
    super.initState();
    final navigationArguments = Modular.args.data;
    if (navigationArguments is Map &&
        navigationArguments["cameFromPdp"] != null) {
      cameFromPdp = navigationArguments["cameFromPdp"] == true;
    }
    loadBagComponents();

    widget.bagPageCubit.loadAddressInfoFromOrderForm();
    widget.orderFormCubit.showWarningValue(false);

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      int animationCount = getAnimationCount();
      if (animationCount < 3) {
        animateActionButtons();
        saveProductGestureCounter(animationCount + 1);
      }
    });

    final bool shouldScroll = (widget.scrollToAddress &&
            widget.orderFormCubit.state.scrollToAddress) ||
        (Modular.args.data == true && Modular.to.path == '/bag');

    if (shouldScroll) {
      Future.delayed(Duration.zero, () {
        _scrollToAddress();
      });
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (!_hasLoggedViewCart) {
      _hasLoggedViewCart = true;
      _eventDispatcher.logViewCart();
    }
  }

  Future<void> addProductWishList({required OrderFormProduct item}) async {
    try {
      await _wishlistCommonCubit.addProduct(item.productId ?? '');
      await _orderCheckoutHandler.updateQuantityItem(
          itemId: item.id ?? '', quantity: 0);

      if (mounted) {
        _showAzzasSnackBar(
            context: context, message: "Produto adicionado aos desejos");
      }
    } catch (e) {
      if (mounted) {
        _showAzzasSnackBar(
          context: context,
          message: 'Erro ao adicionar produto aos desejos',
          isError: true,
        );
      }
    }
  }

  void _showUnavailableProductsBottomSheet(
    BuildContext context,
    List<OrderFormProduct> unavailableProducts,
    bool isLoading,
  ) {
    showAzzasBottomSheet(
      context: context,
      builder: (context) {
        return const BagProductUnavailableBottomSheet();
      },
      vsync: this,
    );
  }

  Future<void> _addGiftToItem(List<OrderFormProduct> availableGiftProducts,
      AzzasProductCardProductParams product) async {
    try {
      final orderFormProduct = availableGiftProducts
          .firstWhere((element) => element.id == product.itemId);

      await _orderCheckoutHandler.addGiftToItem(
        orderFormProduct: orderFormProduct,
      );
    } catch (e) {
      if (mounted) {
        _showAzzasSnackBar(
            context: context,
            message: "Erro ao embalar produto para presente",
            isError: true);
      }
    }
  }

  Future<void> _removeGiftItem(List<OrderFormProduct> availableProducts,
      AzzasProductCardProductParams param) async {
    try {
      final orderFormProduct = availableProducts.firstWhere(
        (element) => element.id == param.itemId,
      );

      await _orderCheckoutHandler.removeGiftItem(
        orderFormProduct: orderFormProduct,
      );
    } catch (e) {
      if (mounted) {
        _showAzzasSnackBar(
          context: context,
          message: 'Erro ao remover embalagem de presente',
          isError: true,
        );
      }
    }
  }

  void _showAzzasSnackBar({
    required BuildContext context,
    required String message,
    bool isError = false,
  }) {
    AzzasSnackBar.show(
      context: context,
      message: message,
      backgroundColor:
          isError ? Tokens.colors.error.pure : Tokens.colors.success.pure,
    );
  }

  bool _isItemSelected(List<OrderFormProduct> availableProducts,
      AzzasProductCardProductParams product) {
    final orderFormProduct =
        availableProducts.firstWhere((element) => element.id == product.itemId);

    return orderFormProduct.isSentToGift;
  }

  void gestureSlide(GestureBinding gestureBinding, double x, double y,
      AxisDirection axisDirection) {
    double yInitialPosition = y;
    double xInitialPosition = x;
    PointerEvent addPointer = PointerAddedEvent(
        pointer: 1, position: Offset(xInitialPosition, yInitialPosition));
    PointerEvent downPointer = PointerDownEvent(
        pointer: 1, position: Offset(xInitialPosition, yInitialPosition));
    gestureBinding.handlePointerEvent(addPointer);
    gestureBinding.handlePointerEvent(downPointer);
    double dx = 7;
    double updateCount = dx;
    for (int i = 0; i < dx; i++) {
      PointerEvent movePointer = PointerMoveEvent(
          pointer: 1,
          delta: axisDirection == AxisDirection.right
              ? Offset(-dx, 0)
              : Offset(dx, 0),
          position: axisDirection == AxisDirection.right
              ? Offset(xInitialPosition - i * dx, yInitialPosition)
              : Offset(xInitialPosition + i * dx, yInitialPosition));
      gestureBinding.handlePointerEvent(movePointer);
    }
    PointerEvent upPointer = PointerUpEvent(
        pointer: 1,
        position:
            Offset(xInitialPosition - dx * updateCount, yInitialPosition));
    gestureBinding.handlePointerEvent(upPointer);
  }

  void animateActionButtons() async {
    await Future.delayed(const Duration(milliseconds: 1000));
    gestureSlide(GestureBinding.instance, 122.8, 300.9, AxisDirection.right);
    await Future.delayed(const Duration(milliseconds: 1500));
    gestureSlide(GestureBinding.instance, 300.8, 300.9, AxisDirection.left);
    await Future.delayed(const Duration(milliseconds: 1000));
    gestureSlide(GestureBinding.instance, 300.8, 300.9, AxisDirection.left);
    await Future.delayed(const Duration(milliseconds: 1500));
    gestureSlide(GestureBinding.instance, 122.8, 300.9, AxisDirection.right);
  }

  String itemsQuantityText() {
    return widget.orderFormCubit.orderForm.getItemsQuantity! > 1
        ? '${widget.orderFormCubit.orderForm.getItemsQuantity} produtos'
        : '1 produto';
  }

  void _showGiftBottomSheet(BuildContext context) {
    showAzzasBottomSheet(
      context: context,
      builder: (context) {
        return BlocBuilder<OrderFormCubit, OrderFormState>(
          bloc: widget.orderFormCubit,
          builder: (context, state) {
            final availableGiftProducts =
                state.orderForm?.getItemsSendToGift ?? [];

            return AzzasSendGiftBottomSheet(
              addGiftItemFn: (product) {
                _addGiftToItem(availableGiftProducts, product);
              },
              removeGiftItemFn: (product) {
                _removeGiftItem(availableGiftProducts, product);
              },
              isItemSelected: (product) {
                return _isItemSelected(availableGiftProducts, product);
              },
              titleStyle: Tokens.typography.headings.small.smallRegular,
              descriptionStyle:
                  Tokens.typography.body.small.smallRegular.copyWith(
                color: Tokens.colors.typography.medium,
              ),
              products: availableGiftProducts
                  .map((product) => product.toCardParams())
                  .toList(),
              onTapSelectProducts: () {
                final authCubit = Modular.get<AuthCubit>();
                if (authCubit.state.isLoggedIn) {
                  return _handleLoggedInUser();
                }
                return _navigateToLogin();
              },
              isDisabled:
                  (state.orderForm?.hasProductToSendGift ?? false) == false,
              isLoading: state.isLoading,
            );
          },
        );
      },
      vsync: this,
    );
  }

  void _scrollToAddress() {
    final bagProducts = widget.orderFormCubit.orderForm.getAddedItems;
    _scrollController.animateTo(
      bagProducts.length * 240,
      duration: const Duration(milliseconds: 900),
      curve: Curves.linear,
    );

    widget.orderFormCubit.showWarningValue(true);
    widget.orderFormCubit.updateScrollToAddress(false);
  }

  @override
  Widget build(BuildContext context) {
    final bagProducts = widget.orderFormCubit.orderForm.getAddedItems;
    final totalOrder = widget.orderFormCubit.orderForm.totalFormatted;
    final currentRoute = Modular.to.path;
    final canPop = currentRoute == '/bag';

    return BlocBuilder<OrderFormCubit, OrderFormState>(
      bloc: widget.orderFormCubit,
      builder: (context, state) {
        return Scaffold(
          body: SafeArea(
            child: Stack(
              children: [
                Padding(
                  padding: EdgeInsets.only(
                    bottom: Tokens.spacing.spacingXXUltraLarge,
                  ),
                  child: CustomScrollView(
                    controller: _scrollController,
                    slivers: [
                      if (canPop && Modular.to.path == '/bag')
                        AzzasSmallAppBar(
                          title: 'Sacola',
                          onBackButton: () {
                            if (cameFromPdp) {
                              Navigator.of(context).pop();
                            } else {
                              MainPage.goHome();
                            }
                          },
                          backIcon: Icons.close,
                          pinned: true,
                          toolbarHeight: 68,
                        ),
                      SliverToBoxAdapter(
                        child:
                            state.orderForm?.getUnavailableItems.isNotEmpty ==
                                    true
                                ? UnavailableItems(
                                    onPressed: () =>
                                        _showUnavailableProductsBottomSheet(
                                      context,
                                      state.orderForm!.getUnavailableItems,
                                      state.isLoading,
                                    ),
                                  )
                                : const SizedBox.shrink(),
                      ),
                      BagListProducts(
                        bagProducts: bagProducts,
                        bagPageCubit: widget.bagPageCubit,
                      ),
                      SliverToBoxAdapter(
                        child: Padding(
                          padding: EdgeInsets.symmetric(
                            vertical: Tokens.spacing.spacingMedium,
                            horizontal: Tokens.spacing.spacingMedium,
                          ),
                          child: Column(
                            children: [
                              BagSendToGift(
                                bagPageCubit: widget.bagPageCubit,
                              ),
                              BagDelivery(
                                bagPageCubit: widget.bagPageCubit,
                                showWarningField: state.showWarningFieldCep &&
                                    state.selectedDeliveryOption == null,
                              ),
                              BagAddCoupons(
                                couponApplied:
                                    widget.orderFormCubit.getCouponApplied(),
                              ),
                              BagAddSellerCode(
                                sellerCodeApplied: widget.orderFormCubit
                                    .getSalesPersonCodeApplied(),
                              ),
                            ].separated(SizedBox(
                              height: Tokens.spacing.spacingXLarge,
                            )),
                          ),
                        ),
                      ),
                      const SliverToBoxAdapter(
                        child: CheckoutBalanceFeedback(
                          checkoutStep: CheckoutStep.bag,
                        ),
                      ),
                      SliverToBoxAdapter(
                        child: AzzasCheckoutResume(
                            boxVerticalSpace: EdgeInsets.symmetric(
                              horizontal: Tokens.spacing.spacingSmall,
                              vertical: Tokens.spacing.spacingXLarge,
                            ),
                            title: itemsQuantityText(),
                            totalAmount: CurrencyHelper.format(
                              amount: widget
                                  .orderFormCubit.orderForm.getTotalItemsValue,
                              dividedBy100: true,
                            ),
                            discountTitle:
                                widget.orderFormCubit.orderForm.hasDiscount
                                    ? 'Descontos'
                                    : null,
                            discountValue: widget
                                    .orderFormCubit.orderForm.hasDiscount
                                ? widget.orderFormCubit.orderForm.discountPrice
                                : null,
                            deliveryTitle: 'Entrega',
                            deliveryType:
                                widget.orderFormCubit.orderForm.shippingPrice,
                            submitLabel: 'Seguir com a compra',
                            totalDesc:
                                widget.orderFormCubit.orderForm.totalFormatted,
                            totalTitle: 'Total',
                            giftCardTitle: 'Saída "Saldo em créditos”',
                            giftCardValue: widget.orderFormCubit.orderForm
                                    .paymentData!.giftCards!.isEmpty
                                ? null
                                : CurrencyHelper.format(
                                    amount: widget.orderFormCubit.orderForm
                                        .getGiftCardValues(),
                                    dividedBy100: true,
                                  )),
                      ),
                      SliverList(
                        delegate: SliverChildBuilderDelegate(
                          (context, index) {
                            final component = components[index];
                            return buildComponentWidget(component);
                          },
                          childCount: components.length,
                        ),
                      ),
                      SliverToBoxAdapter(
                        child: Padding(
                          padding: EdgeInsets.only(
                              top: Tokens.spacing.spacingXLarge),
                          child: CheckoutWishlist(
                            bagPageCubit: widget.bagPageCubit,
                          ),
                        ),
                      )
                    ],
                  ),
                ),
                Positioned(
                  bottom: Tokens.spacing.spacingXXSmall,
                  left: Tokens.spacing.spacingMedium,
                  right: Tokens.spacing.spacingMedium,
                  child: GestureDetector(
                    onTap: () {
                      if (state.selectedDeliveryOption == null) {
                        _scrollToAddress();
                      }
                    },
                    child: BagButtonClosePurchase(
                      onPressed: _onTapClosePurchase,
                      totalOrderFormValue: totalOrder,
                      isLoading: widget.orderFormCubit.state.isLoading,
                      isDisabled: state.selectedDeliveryOption == null,
                    ),
                  ),
                )
              ],
            ),
          ),
        );
      },
    );
  }

  Future<void> loadBagComponents() async {
    final response =
        await cmsService.loadComponentsForDocument('bag-crisbarros');
    if (response != null) {
      setState(() {
        components = response;
        isLoaded = true;
      });
    }
  }

  void _onTapProduct(Product product) async {
    Modular.to.pushNamed('/pdp', arguments: product);
  }

  Widget buildComponentWidget(dynamic component) {
    final widgetBuilders = {
      SpotProductCmsComponent: (component) =>
          SpotProductCmsWidget.fromSpotProductCmsComponent(component,
              onTap: _onTapProduct),
      HighlightOptionalCmsComponent: (component) =>
          HighlightOptionalCmsWidget.fromComponent(component),
    };

    final builder = widgetBuilders[component.runtimeType];
    return builder != null ? builder(component) : const SizedBox.shrink();
  }

  //TODO: Reminder to move all the logics below to an cubit in azzas_app_commons
  void _onTapClosePurchase() {
    final authCubit = Modular.get<AuthCubit>();
    _eventDispatcher.logBeginCheckout();

    if (widget.orderFormCubit.orderForm.getItemsQuantity! > 1 &&
        widget.orderFormCubit.orderForm.hasProductToSendGift) {
      return _showGiftBottomSheet(context);
    }

    if (authCubit.state.isLoggedIn) {
      _handleLoggedInUser();
    } else {
      _navigateToLogin();
    }
  }

  void _handleLoggedInUser() {
    if (!clientProfileIsComplete()) {
      _handleUserIncompleteData();
      return;
    } else {
      _proceedToNextSteps();
      return;
    }
  }

  void _handleUserIncompleteData() {
    Modular.to.pushNamed("/account/complete_user_data", arguments: {
      "overrideFinishFlow": () => _handleLoggedInUser(),
    }).then((_) => _eventDispatcher.logViewCart());
  }

  void _navigateToLogin() {
    Modular.to.pushNamed("/account/login_email", arguments: {
      "overrideFinishFlow": () => _handleLoggedInUser(),
    }).then((_) => _eventDispatcher.logViewCart());
  }

  void _navigateToOrderReview() {
    Modular.to
        .pushNamed('/order_review',)
        .then((_) => _eventDispatcher.logViewCart());
  }

  void _navigateToDeliveryAddressFilled() {
    Modular.to.pushNamed('/delivery-address-filled', arguments: {
      "overrideFinishFlow": () => _proceedToNextSteps(),
    }).then((_) => _eventDispatcher.logViewCart());
  }

  void _proceedToNextSteps() async {
    final addressNumber =
        widget.orderFormCubit.orderForm.shippingData?.address?.number;
    final addressReceiver =
        widget.orderFormCubit.orderForm.shippingData?.address?.receiverName;
    if (addressNumber == null || addressReceiver == null) {
      _navigateToDeliveryAddressFilled();

      return;
    }

    //Check payment
    final payments = widget.orderFormCubit.orderForm.paymentData?.payments;

    final firstSelectedPayment =
        widget.orderFormCubit.orderForm.firstSelectedPayment;

    final hasInstallments = firstSelectedPayment?.installments != null &&
        firstSelectedPayment!.installments > 0;
    final hasAccountId = firstSelectedPayment?.accountId != null;
    final hasBin = firstSelectedPayment?.bin != null;
    final hasIncompleteData = (!hasInstallments || !hasBin || !hasAccountId) &&
        widget.orderFormCubit.orderForm.isCreditCardSelected;

    if (payments == null || payments.isEmpty || hasIncompleteData) {
      Modular.to.pushNamed('/select-payment');

      return;
    }

    _navigateToOrderReview();
  }

  bool clientProfileIsComplete() {
    final orderFormCubit = Modular.get<OrderFormCubit>();
    final clientProfileData = orderFormCubit.orderForm.clientProfileData;
    return clientProfileData?.isComplete() ?? false;
  }
}
