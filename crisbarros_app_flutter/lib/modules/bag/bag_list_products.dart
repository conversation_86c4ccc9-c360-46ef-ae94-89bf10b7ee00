import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:crisbarros_app_flutter/config/design_tokens/tokens.dart';
import 'package:flutter/material.dart';

class BagListProducts extends StatefulWidget {
  final List<OrderFormProduct> bagProducts;
  final BagPageCubit bagPageCubit;

  const BagListProducts(
      {super.key, required this.bagProducts, required this.bagPageCubit});

  @override
  State<BagListProducts> createState() => _BagListProductsState();
}

class _BagListProductsState extends State<BagListProducts>
    with TickerProviderStateMixin {
  final orderCheckoutHandler = Modular.get<OrderCheckoutHandler>();
  final wishlistCommonCubit = Modular.get<WishlistCommonCubit>();
  final orderFormCubit = Modular.get<OrderFormCubit>();
  final _eventDispatcher = Modular.get<EventDispatcher>();

  Future<void> addProductWishList(
      {required OrderFormProduct item, required int index}) async {
    try {
      await wishlistCommonCubit.addProduct(item.productId ?? '');
      await orderCheckoutHandler.updateQuantityItem(
          itemId: item.id ?? '', quantity: 0);
      widget.bagPageCubit.loadAddressInfoFromOrderForm();
      _eventDispatcher.logAddToWishlist(orderFormProduct: item, index: index);
      if (mounted) {
        _showAzzasSnackBar(
            context: context, message: "Produto adicionado aos desejos");
      }
    } catch (e) {
      if (mounted) {
        _showAzzasSnackBar(
          context: context,
          message: 'Erro ao adicionar produto aos desejos',
          isError: true,
        );
      }
    }
  }

  Future<void> _removeProduct({
    required OrderFormProduct item,
  }) async {
    try {
      _eventDispatcher.logLegacyRemoveFromCart(
          orderFormProduct: item,
          itemId: item.itemId,
          region: 'checkout');
      await orderCheckoutHandler.updateQuantityItem(
          itemId: item.id ?? '', quantity: 0);
      widget.bagPageCubit.loadAddressInfoFromOrderForm();

      if (mounted) {
        _showAzzasSnackBar(
          context: context,
          message: 'Item excluído com sucesso',
        );
      }
    } catch (e) {
      if (mounted) {
        _showAzzasSnackBar(
          context: context,
          message: 'Erro ao remover item',
          isError: true,
        );
      }
    } finally {
      Navigator.of(context).pop();
    }
  }

  void _showAzzasSnackBar({
    required BuildContext context,
    bool isError = false,
    required String message,
  }) {
    AzzasSnackBar.show(
      context: context,
      message: message,
      backgroundColor:
          isError ? Tokens.colors.error.pure : Tokens.colors.success.pure,
    );
  }

  Future<void> _updateQuantity(
      {required String itemId,
      required int quantity,
      required OrderFormProduct item,
      required int index}) async {
    try {
      await orderCheckoutHandler.updateQuantityItem(
          itemId: itemId, quantity: quantity);
      widget.bagPageCubit.loadAddressInfoFromOrderForm();
      _eventDispatcher.logAddToCart(
        orderFormProduct: item,
        index: index,
        itemId: itemId,
        quantity: quantity,
      );
      if (mounted) {
        _showAzzasSnackBar(
          context: context,
          message: 'Quantidade atualizada com sucesso',
        );
      }
    } catch (e) {
      if (mounted) {
        _showAzzasSnackBar(
          context: context,
          message: 'Erro ao atualizar quantidade',
          isError: true,
        );
      }
    } finally {
      Navigator.of(context).pop();
    }
  }

  void changeQuantity(BuildContext context, OrderFormProduct item, int index) {
    showAzzasBottomSheet(
      context: context,
      builder: (context) {
        return BlocBuilder<BagPageCubit, BagPageState>(
          bloc: widget.bagPageCubit,
          builder: (context, state) {
            return BlocSelector<OrderFormCubit, OrderFormState, bool>(
              bloc: orderFormCubit,
              selector: (orderFormState) {
                return orderFormState.isLoading;
              },
              builder: (context, isOrderFormLoading) {
                return AzzasChangeQuantityBottomSheet(
                  showDragBar: true,
                  quantity: state.currentProductQuantity,
                  isLoading: state.isLoadingQuantity || isOrderFormLoading,
                  onCloseTap: () => Navigator.pop(context),
                  title: 'Trocar quantidade',
                  titleStyle: Tokens.typography.headings.small.smallRegular,
                  getItemQuantityFn: () =>
                      widget.bagPageCubit.getAvailableQuantityForProduct(
                    item.productId ?? '',
                    item.id ?? '',
                  ),
                  onSelectQuantity: (quantity) async {
                    if (quantity == 0) {
                      return await _removeProduct(item: item);
                    }
                    return await _updateQuantity(
                      itemId: item.id ?? '',
                      quantity: quantity,
                      item: item,
                      index: index,
                    );
                  },
                );
              },
            );
          },
        );
      },
      vsync: this,
    );
  }

  void _changeSize(BuildContext context, OrderFormProduct item) {
    widget.bagPageCubit.getAvailableSizes(
      productId: item.productId ?? '',
    );

    showAzzasBottomSheet(
      context: context,
      builder: (context) {
        return BlocBuilder<BagPageCubit, BagPageState>(
          bloc: widget.bagPageCubit,
          builder: (context, state) {
            final productSizes = state.currentProductAvailableSizes;

            return AzzasSkuSelectionBottomSheet(
              showDragBar: true,
              onCloseTap: () => Navigator.pop(context),
              title: 'Trocar tamanho',
              titleStyle: Tokens.typography.headings.small.smallRegular,
              content: state.isLoadingProductSizes
                  ? Center(
                      child: Padding(
                      padding: EdgeInsets.all(
                        Tokens.spacing.spacingMedium,
                      ),
                      child: const AzzasSpinner(),
                    ))
                  : AzzasPicker(
                      height: 200,
                      onSelectedItemChanged: (index) {
                        final item = productSizes[index];
                        widget.bagPageCubit.setActiveItemSize(item);
                      },
                      options: productSizes.map((i) {
                        String? supportText;

                        if (i.hasOne) {
                          supportText = 'resta apenas 1!';
                        } else if (i.hasTwo) {
                          supportText = 'restam apenas 2!';
                        } else if (!i.isAvailable) {
                          supportText = 'esgotado';
                        }

                        return AzzasPickerData(
                          title: i.itemSize,
                          description: supportText ?? ' ',
                        );
                      }).toList(),
                    ),
              action: AzzasPrimaryButton(
                onPressed: () async {
                  if (state.activeItemSize?.isAvailable == true) {
                    try {
                      await orderCheckoutHandler.updateItemSize(
                        newItemId: state.activeItemSize?.itemId ?? '',
                        currentItemId: item.id ?? '',
                        newQuantity: 1,
                      );
                      widget.bagPageCubit.loadAddressInfoFromOrderForm();
                    } catch (e) {
                      if (mounted) {
                        _showAzzasSnackBar(
                          context: context,
                          message: 'Erro ao trocar tamanho',
                          isError: true,
                        );
                      }
                    } finally {
                      Navigator.of(context).pop();
                    }
                  }
                },
                expanded: true,
                child: Text(
                  state.activeItemSize?.isAvailable == true
                      ? 'Selecionar tamanho'
                      : 'Avise-me',
                ),
              ),
            );
          },
        );
      },
      vsync: this,
    );
  }

  void bottomSheetRemove(BuildContext context, OrderFormProduct item) {
    showAzzasBottomSheet(
      context: context,
      builder: (context) {
        return BlocBuilder<OrderFormCubit, OrderFormState>(
          bloc: orderFormCubit,
          builder: (context, state) {
            return AzzasRemoveProductBottomSheet(
              padding: EdgeInsets.all(Tokens.spacing.spacingLarge),
              closeIcon: Tokens.icons.action.close,
              showDragBar: true,
              isLoading: state.isLoading,
              isCancelButtonDisabled: state.isLoading,
              titleStyle:
                  Tokens.typography.headings.small.smallRegular.copyWith(
                color: Tokens.colors.neutral.dark,
              ),
              onCancelTap: () {
                Navigator.pop(context);
              },
              onRemoveTap: () async {
                await _removeProduct(item: item);
              },
            );
          },
        );
      },
      vsync: this,
    );
  }

  @override
  Widget build(BuildContext context) {
    return SliverList(
      delegate: SliverChildBuilderDelegate(
        (context, index) => AzzasCardSlide(
          startActionPane: AzzasCardSlideTheme(
            backgroundColor: Tokens.colors.success.light,
            foregroundColor: Tokens.colors.error.medium,
            onPressed: (_) => addProductWishList(
                item: widget.bagProducts[index], index: index),
            child: Icon(
              Tokens.icons.action.favoriteFalse,
              size: 22,
              color: Tokens.colors.neutral.pure,
            ),
          ),
          endActionPane: AzzasCardSlideTheme(
            backgroundColor: Tokens.colors.error.medium,
            foregroundColor: Tokens.colors.success.light,
            onPressed: (_) => bottomSheetRemove(
              context,
              widget.bagProducts[index],
            ),
            child: Icon(
              Tokens.icons.action.delete,
              size: 22,
              color: Tokens.colors.neutral.pure,
            ),
          ),
          onPressed: (_) {},
          slidableWidget: AzzasCheckoutProductCard(
            onTapCard: () {
              _eventDispatcher.logLegacySelectItem(
                  orderFormProduct: widget.bagProducts[index],
                  region: 'checkout',
                  itemListName: 'checkout',
                  index: index);

              NavigatorDynamic.call(
                  'pdp/${widget.bagProducts[index].productId}');
            },
            imageHeight: 288,
            imageWidth: 186,
            saveProductText: 'Salvar para depois',
            showRemoveButton: true,
            onTapSaveProduct: () => addProductWishList(
              item: widget.bagProducts[index],
              index: index,
            ),
            onTapRemoveProduct: () async {
              _removeProduct(item: widget.bagProducts[index]);
            },
            onTapProductQuantity: () => changeQuantity(
              context,
              widget.bagProducts[index],
              index,
            ),
            onTapSizeButton: () =>
                _changeSize(context, widget.bagProducts[index]),
            product: widget.bagProducts[index].toCardParams(),
          ),
        ),
        childCount: widget.bagProducts.length,
      ),
    );
  }
}
