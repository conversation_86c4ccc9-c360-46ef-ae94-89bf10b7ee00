import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:azzas_analytics/events/navigation/navigation_events.dart';
import 'package:crisbarros_app_flutter/config/design_tokens/tokens.dart';
import 'package:crisbarros_app_flutter/modules/main/main_page.dart';
import 'package:flutter/material.dart';

class CompleteUserDataPage extends StatefulWidget {
  const CompleteUserDataPage({
    this.isRegister = false,
    this.overrideFinishFlow,
    super.key,
  });

  final bool? isRegister;
  final Function()? overrideFinishFlow;

  @override
  State<CompleteUserDataPage> createState() => _CompleteUserDataPageState();
}

class _CompleteUserDataPageState extends State<CompleteUserDataPage> {
  final personalDataModel = PersonalDataModel();
  final authCubit = Modular.get<AuthCubit>();
  final completeUserDataCubit = Modular.get<CompleteUserDataCubit>();

  bool isSaveButtonDisabled = true;
  bool receiveSMS = false;
  bool receiveEmail = false;
  late bool hasUserInfo;

  final _eventDispatcher = Modular.get<EventDispatcher>();

  @override
  void initState() {
    NavigationEvents.logPageView(local: 'complete_user_data');
    super.initState();
    _onInit();
  }

  @override
  Widget build(BuildContext context) {
    final headerTitle =
        hasUserInfo ? 'Bem vinda de volta!' : 'Seja bem vindo (a)';
    final headerDescription = hasUserInfo
        ? 'Recuperamos sua conta e agora você pode aproveitar para atualizar seus dados.'
        : 'Preencha seus dados para continuar';
    return Scaffold(
      body: BlocConsumer<CompleteUserDataCubit, CompleteUserDataState>(
        bloc: completeUserDataCubit,
        listener: (context, state) {
          if (state.success) {
            if (widget.overrideFinishFlow != null) {
              widget.overrideFinishFlow!();
              return;
            }

            if (widget.isRegister == true) {
              MainPage.goMyAccount();
              Modular.to.popUntil((route) => route.settings.name == '/');
            }

            //TODO: Adicionar navegação para a próxima tela
            AzzasSnackBar.show(
              context: context,
              message: "Dados salvos com sucesso",
              status: SnackBarStatus.success,
              backgroundColor: Tokens.colors.success.pure,
            );

            _updateUserInfo();

            return;
          }
        },
        builder: (context, state) {
          return SingleChildScrollView(
            child: SafeArea(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (widget.isRegister != true)
                    SizedBox(
                      height: 72,
                      child: Padding(
                        padding: const EdgeInsets.only(right: 4.0, left: 8.0),
                        child: IconButton(
                          onPressed: () {
                            Navigator.of(context).pop();
                          },
                          icon: Icon(
                            Tokens.icons.navigation.left,
                            size: 32.0,
                          ),
                        ),
                      ),
                    ),
                  SizedBox(height: Tokens.spacing.spacingLarge),
                  Padding(
                    padding: EdgeInsets.symmetric(
                        horizontal: Tokens.spacing.spacingMedium),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          headerTitle,
                          style: Tokens
                              .typography.body.extraLarge.extraLargeRegular,
                        ),
                        SizedBox(height: Tokens.spacing.spacingSmall),
                        Text(
                          headerDescription,
                          style: Tokens.typography.body.small.smallRegular,
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: Tokens.spacing.spacingLarge),
                  const Divider(color: Color(0xFFE5E5E5), height: 1),
                  BlocBuilder<AuthCubit, AuthState>(
                    bloc: authCubit,
                    builder: (context, state) {
                      final email = state.localUserInfo?.personEmail;

                      personalDataModel.emailController.text = email ?? '';

                      return Container(
                        width: double.infinity,
                        padding: EdgeInsets.symmetric(
                          horizontal: Tokens.spacing.spacingMedium,
                          vertical: Tokens.spacing.spacingSmall,
                        ),
                        color: const Color(0xFFF5F5F5),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Você entrou como',
                              style: Tokens
                                  .typography.body.extraSmall.extraSmallRegular
                                  .copyWith(
                                color: Tokens.colors.typography.medium,
                              ),
                            ),
                            SizedBox(height: Tokens.spacing.spacingXSmall),
                            Text(
                              email ?? '',
                              style: Tokens.typography.body.small.smallRegular,
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                  SizedBox(height: Tokens.spacing.spacingLarge),
                  Padding(
                    padding: EdgeInsets.symmetric(
                      horizontal: Tokens.spacing.spacingMedium,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        AzzasInput(
                          enabled: !state.loading,
                          textEditingController:
                              personalDataModel.cpfController,
                          hintText: "Digite seu CPF",
                          placeholder: "Digite seu CPF",
                          cursorHeight: 24.0,
                          inputFormatter: AzzasInputMask.cpf.formatter,
                          onChanged: (value) {
                            _validateFields();
                          },
                          validator: (value) => StringHelper.isCpf(value!)
                              ? null
                              : "CPF inválido",
                          autoValidateMode: AutovalidateMode.onUserInteraction,
                          size: AzzasInputSize.large,
                          contentPadding: EdgeInsets.all(
                            Tokens.spacing.spacingSmall,
                          ),
                        ),
                        SizedBox(height: Tokens.spacing.spacingSmall),
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Expanded(
                              child: AzzasInput(
                                enabled: !state.loading,
                                textEditingController:
                                    personalDataModel.nameController,
                                hintText: "Nome",
                                placeholder: "Nome",
                                cursorHeight: 24.0,
                                validator: (value) =>
                                    value!.isEmpty ? "Nome inválido" : null,
                                autoValidateMode:
                                    AutovalidateMode.onUserInteraction,
                                size: AzzasInputSize.large,
                                contentPadding: EdgeInsets.all(
                                  Tokens.spacing.spacingSmall,
                                ),
                                onChanged: (value) {
                                  _validateFields();
                                },
                              ),
                            ),
                            SizedBox(width: Tokens.spacing.spacingXSmall),
                            Expanded(
                              child: AzzasInput(
                                enabled: !state.loading,
                                textEditingController:
                                    personalDataModel.lastNameController,
                                hintText: "Sobrenome",
                                cursorHeight: 24.0,
                                placeholder: "Sobrenome",
                                validator: (value) => value!.isEmpty
                                    ? "Sobrenome inválido"
                                    : null,
                                autoValidateMode:
                                    AutovalidateMode.onUserInteraction,
                                size: AzzasInputSize.large,
                                contentPadding: EdgeInsets.all(
                                  Tokens.spacing.spacingSmall,
                                ),
                                onChanged: (value) {
                                  _validateFields();
                                },
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: Tokens.spacing.spacingSmall),
                        AzzasInput(
                          enabled: !state.loading,
                          textEditingController:
                              personalDataModel.phoneController,
                          inputFormatter: AzzasInputMask.phone.formatter,
                          hintText: "Telefone celular",
                          placeholder: "Telefone celular",
                          cursorHeight: 24.0,
                          onChanged: (value) {
                            _validateFields();
                          },
                          validator: (value) => StringHelper.isPhoneNumber(
                                  value!
                                      .replaceAll(' ', '')
                                      .replaceAll('-', ''))
                              ? null
                              : "Telefone inválido",
                          keyboardType: TextInputType.number,
                          autoValidateMode: AutovalidateMode.onUserInteraction,
                          size: AzzasInputSize.large,
                          contentPadding: EdgeInsets.all(
                            Tokens.spacing.spacingSmall,
                          ),
                        ),
                        SizedBox(height: Tokens.spacing.spacingSmall),
                        AzzasInput(
                          enabled: !state.loading,
                          textEditingController:
                              personalDataModel.birthDateController,
                          hintText: "Data de nascimento",
                          placeholder: "Data de nascimento",
                          validator: validateBirthDate,
                          autoValidateMode: AutovalidateMode.onUserInteraction,
                          size: AzzasInputSize.large,
                          cursorHeight: 24.0,
                          contentPadding: EdgeInsets.all(
                            Tokens.spacing.spacingSmall,
                          ),
                          keyboardType: const TextInputType.numberWithOptions(),
                          inputFormatter: AzzasInputMask.dateDDMMYY.formatter,
                          onChanged: (value) {
                            _validateFields();
                          },
                        ),
                        SizedBox(height: Tokens.spacing.spacingLarge),
                        AzzasCheckbox(
                          isDisabled: state.loading,
                          isChecked: receiveEmail,
                          labelText:
                              "Aceito informações referentes ao meu pedido",
                          labelStyle: Tokens
                              .typography.body.extraSmall.extraSmallRegular,
                          borderColor: const Color(0xFFE5E5E5),
                          onTap: (value) {
                            receiveEmail = value;
                            _validateFields();
                          },
                        ),
                        SizedBox(height: Tokens.spacing.spacingSmall),
                        AzzasCheckbox(
                          isDisabled: state.loading,
                          labelText: "Aceito receber sms promocionais",
                          labelStyle: Tokens
                              .typography.body.extraSmall.extraSmallRegular,
                          onTap: (value) {
                            receiveSMS = value;
                            _validateFields();
                          },
                          borderColor: const Color(0xFFE5E5E5),
                          isChecked: receiveSMS,
                        ),
                        SizedBox(height: Tokens.spacing.spacingLarge),
                        Text(
                          'Ao se cadastrar, você concorda com nossa política de privacidade',
                          style: Tokens
                              .typography.body.extraSmall.extraSmallRegular,
                        ),
                        SizedBox(height: Tokens.spacing.spacingLarge),
                        Padding(
                          padding: EdgeInsets.only(
                            bottom: Tokens.spacing.spacingMedium,
                          ),
                          child: AzzasButton.primary(
                            isLoading: state.loading,
                            expanded: true,
                            onPressed: _completeUserData,
                            isDisabled: isSaveButtonDisabled,
                            child: const Text('Continuar'),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  void _onInit() {
    hasUserInfo = authCubit.state.localUserInfo?.personDocumentCpf != null;

    if (hasUserInfo) {
      personalDataModel.updateFields(authCubit.state.localUserInfo!);
    }

    _validateFields();
  }

  void _enableSaveButton() {
    setState(() {
      isSaveButtonDisabled = false;
    });
  }

  void _disableSaveButton() {
    setState(() {
      isSaveButtonDisabled = true;
    });
  }

  void _validateFields() {
    if (personalDataModel.validate()) {
      _enableSaveButton();
    } else {
      _disableSaveButton();
    }
  }

  Future<void> _completeUserData() async {
    await completeUserDataCubit.completeUserData(
      receiveEmail: receiveEmail,
      receiveSMS: receiveSMS,
      orderFormId: Modular.get<OrderFormCubit>().orderForm.orderFormId ?? '',
      userInfo: personalDataModel.getUserInfo(),
    );
    _eventDispatcher.logAddPersonalInfo(preFilled: hasUserInfo ? 'true' : 'false');
  }

  Future<void> _updateUserInfo() async {
    await authCubit.getUserInfo();
  }

  String? validateBirthDate(String? value) {
    if (value == null || value.isEmpty) {
      return "Data inválida";
    }

    try {
      List<String> parts = value.split('/');
      if (parts.length < 3) return "Data inválida";

      int day = int.parse(parts[0]);
      int month = int.parse(parts[1]);
      int year = int.parse(parts[2]);
      if (year < 100) {
        year += 2000;
      }

      DateTime birthDate = DateTime(year, month, day);
      DateTime today = DateTime.now();

      if (birthDate.isAfter(today)) {
        return "Data inválida";
      }
    } catch (e) {
      return "Data inválida";
    }

    return null;
  }
}
