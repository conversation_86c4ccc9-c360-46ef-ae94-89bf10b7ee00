import 'dart:async';
import 'package:visibility_detector/visibility_detector.dart';
import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:crisbarros_app_flutter/config/design_tokens/tokens.dart';
import 'package:crisbarros_app_flutter/modules/pdp/widgets/pdp_add_to_bag_bottom_sheet.dart';
import 'package:crisbarros_app_flutter/modules/pdp/widgets/pdp_full_look_add_to_bag_bottom_sheet.dart';
import 'package:crisbarros_app_flutter/modules/pdp/widgets/pdp_measurement_table_bottom_sheet.dart';
import 'package:crisbarros_app_flutter/modules/pdp/widgets/pdp_sold_out_bottom_sheet.dart';
import 'package:flutter/material.dart';

class PdpFullLookProductList extends StatefulWidget {
  final String? productIdsSeparateByComma;
  final VoidCallback navigateToBag;
  const PdpFullLookProductList({
    super.key,
    required this.productIdsSeparateByComma,
    required this.navigateToBag,
  });

  @override
  State<PdpFullLookProductList> createState() => _PdpFullLookProductListState();
}

class _PdpFullLookProductListState extends State<PdpFullLookProductList>
    with TickerProviderStateMixin {
  List<Product> productsResultSet = [];
  final searchProducts = Modular.get<SearchProductsUseCase>();
  final _eventDispatcher = Modular.get<EventDispatcher>();
  final pdpCubit = Modular.get<PdpCubit>();
  bool _hasDispatchedEvent = false;

  @override
  void initState() {
    super.initState();
    scheduleMicrotask(() async {
      productsResultSet = await searchProducts.call(
        search: Search(
          productIdsSeparateByComma: widget.productIdsSeparateByComma,
        ),
      );
      pdpCubit.setInitialItemsinFullLook(initialItems);
    });
  }

  void _logViewItemListEvent() {
    _eventDispatcher.logViewItemList(
      itemListName: 'Complete o look',
      products: productsResultSet,
      region: 'pdp'
    );
  }

  List<OrderFormItem> get initialItems {
    List<OrderFormItem> initialItems = [];

    for (var product in productsResultSet) {
      final firtsSizeAvailable =
          product.items?.firstWhereOrNull((item) => item.isAvailable);
      if (firtsSizeAvailable == null) return initialItems;
      initialItems.add(firtsSizeAvailable);
    }

    return initialItems;
  }

  @override
  Widget build(BuildContext context) {
    return AnalyticsMetadataProvider(
      metadata: const {
        'item_list_name': 'complete o look',
        'item_list_position': 'corpo da pdp',
      },
      child: BlocBuilder<PdpCubit, PdpState>(
        bloc: pdpCubit,
        builder: (context, state) {
          return VisibilityDetector(
            key: Key('full_look_list_${widget.hashCode}'),
            onVisibilityChanged: (visibilityInfo) {
              if (!mounted ||
                  _hasDispatchedEvent ||
                  productsResultSet.isEmpty) {
                return;
              }
              if (visibilityInfo.visibleFraction > 0.9) {
                _logViewItemListEvent();
                _hasDispatchedEvent = true;
              }
            },
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Complete o look',
                  style: Tokens.typography.body.extraLarge.extraLargeRegular,
                ),
                SizedBox(height: Tokens.spacing.spacingMedium),
                ...productsResultSet.asMap().entries.map((entry) {
                  final index = entry.key;
                  final fullLookProduct = entry.value;
                  if (state.similarProductsState.activeFullLookItems?.isEmpty ==
                      true) {
                    return const SizedBox.shrink();
                  }
                  return Padding(
                    padding:
                        EdgeInsets.only(bottom: Tokens.spacing.spacingXLarge),
                    child: FullLookProductCard(
                      product: fullLookProduct,
                      pdpCubit: pdpCubit,
                      navigateToBag: widget.navigateToBag,
                      index: index,
                    ),
                  );
                }),
                AzzasPrimaryButton(
                  expanded: true,
                  onPressed: () {
                    if (state.similarProductsState.activeFullLookItems
                            ?.isEmpty ==
                        true) return;
                    addFullLooktoBagBottomSheet(
                      state.similarProductsState.activeFullLookItems!,
                      productsResultSet,
                    );
                    // addItemsToBag(state.similarProductsState.activeFullLookItems!);
                  },
                  child: const Text('Adicionar todos à sacola'),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  void addFullLooktoBagBottomSheet(
      List<OrderFormItem> items, List<Product> products) {
    showAzzasBottomSheet(
      context: context,
      builder: (context) {
        return PdpFullLookAddToBagBottomSheet(
          items: items,
          products: products,
          pdpCubit: pdpCubit,
          navigateToBag: widget.navigateToBag,
        );
      },
      vsync: this,
    );
  }
}

class FullLookProductCard extends StatefulWidget {
  final Product product;
  final PdpCubit pdpCubit;
  final VoidCallback navigateToBag;
  final int index;
  const FullLookProductCard({
    super.key,
    required this.product,
    required this.pdpCubit,
    required this.navigateToBag,
    required this.index,
  });

  @override
  State<FullLookProductCard> createState() => _FullLookProductCardState();
}

class _FullLookProductCardState extends State<FullLookProductCard>
    with TickerProviderStateMixin {
  late ValueNotifier<OrderFormItem> activeItemNotifier;
  final _eventDispatcher = Modular.get<EventDispatcher>();

  @override
  void initState() {
    super.initState();
    _getInitialItem();
  }

  void _getInitialItem() {
    if (widget.product.items == null || widget.product.items!.isEmpty) {
      return;
    }
    if (widget.product.isAvailability) {
      activeItemNotifier = ValueNotifier<OrderFormItem>(
          widget.product.items!.firstWhere((item) => item.isAvailable));
    } else {
      activeItemNotifier =
          ValueNotifier<OrderFormItem>(widget.product.items!.first);
    }
  }

  void onTapCloseModalSizeUnavailable(OrderFormItem activeItem) {
    if (!activeItem.isAvailable) {
      AzzasSnackBar.show(
        context: context,
        message: 'Produto indisponível no tamanho escolhido',
        status: SnackBarStatus.danger,
      );
      activeItemNotifier.value =
          widget.product.items!.firstWhere((item) => item.isAvailable);
      widget.pdpCubit.changeSize(activeItemNotifier.value);
    }
    Navigator.of(context).pop();
  }

  void chooseSizeBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      enableDrag: false,
      backgroundColor: Colors.transparent,
      transitionAnimationController: AnimationController(
        vsync: this,
        duration: const Duration(milliseconds: 300),
      ),
      builder: (context) {
        return GestureDetector(
          onTap: () => onTapCloseModalSizeUnavailable(
              activeItemNotifier.value), // Fecha ao clicar fora
          child: Scaffold(
            backgroundColor: Colors.transparent,
            body: GestureDetector(
              onTap: () {}, // Impede fechamento ao tocar no conteúdo
              child: Align(
                alignment: Alignment.bottomCenter,
                child: ValueListenableBuilder<OrderFormItem>(
                  valueListenable: activeItemNotifier,
                  builder: (context, activeItem, _) {
                    return AzzasSkuSelectionBottomSheet(
                      onCloseTap: () {
                        onTapCloseModalSizeUnavailable(activeItem);
                      },
                      title: 'Selecione o seu tamanho',
                      titleStyle: Tokens.typography.headings.small.smallRegular,
                      action: AzzasPrimaryButton(
                        isLoading: widget.pdpCubit.state.pdpBagState.loading,
                        onPressed: () => onTapAction(activeItem),
                        expanded: true,
                        child: Text(
                          activeItem.isAvailable
                              ? 'Selecionar tamanho'
                              : 'Avise-me',
                        ),
                      ),
                      content: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Wrap(
                            children: [
                              ProductBoxButton(
                                buttonText: 'Tabela de medidas',
                                textStyle: Tokens.typography.body.extraSmall
                                    .extraSmallRegular,
                                icon: Icon(Tokens.icons.navigation.right,
                                    size: 10,
                                    color: Tokens.colors.typography.light),
                                onTap: () {
                                  Navigator.of(context).pop();
                                  onTapTableSizes();
                                },
                              ),
                              SizedBox(width: Tokens.spacing.spacingSmall),
                              if (widget.product.hasSizeUnavailable)
                                ProductBoxButton(
                                  buttonText: 'Avise-me',
                                  textStyle: Tokens.typography.body.extraSmall
                                      .extraSmallRegular,
                                  icon: Icon(Tokens.icons.navigation.right,
                                      size: 10,
                                      color: Tokens.colors.typography.light),
                                  onTap: () {
                                    Navigator.of(context).pop();
                                    warneMe(activeItem.itemId ?? '');
                                  },
                                ),
                            ],
                          ),
                          SizedBox(
                            height: 250.0,
                            child: AzzasPicker(
                              initialItem:
                                  widget.product.items?.indexOf(activeItem) ??
                                      0,
                              options: widget.product.items!
                                  .asMap()
                                  .entries
                                  .map((entry) {
                                int idx = entry.key;
                                OrderFormItem val = entry.value;
                                String? textSuport;
                                if (val.hasOne) textSuport = 'Resta 1';
                                if (val.hasTwo) textSuport = 'Resta 2!';
                                if (!val.isAvailable) textSuport = 'Avise-me';

                                final size =
                                    val.tamanho?.elementAtOrNull(idx) ??
                                        val.tamanho?.firstOrNull ??
                                        'indefinido';
                                return AzzasPickerData(
                                  title: size,
                                  description: textSuport ?? ' ',
                                );
                              }).toList(),
                              onSelectedItemChanged: (index) {
                                activeItemNotifier.value =
                                    widget.product.items![index];
                              },
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Future<void> onTapAction(OrderFormItem item) async {
    if (!item.isAvailable) {
      Navigator.of(context).pop();
      widget.pdpCubit.setProductSize(item);
      warneMe(item.itemId ?? '');
      activeItemNotifier = ValueNotifier<OrderFormItem>(
          widget.product.items!.firstWhere((item) => item.isAvailable));
      widget.pdpCubit.changeSize(activeItemNotifier.value);
    } else {
      widget.pdpCubit.changeSize(item);
      Navigator.of(context).pop();
    }
  }

  void warneMe(String skuId) {
    showAzzasBottomSheet(
      isScrollControlled: true,
      context: context,
      builder: (context) {
        return PdpSoldOutBottomSheet(controller: widget.pdpCubit, skuId: skuId);
      },
      vsync: this,
    );
  }

  void onTapTableSizes() {
    showAzzasBottomSheet(
      context: context,
      vsync: this,
      builder: (BuildContext context) {
        return PdpMeasurementTableBottomSheet(product: widget.product);
      },
    );
  }

  @override
  void dispose() {
    activeItemNotifier.dispose();
    super.dispose();
  }

  void addtoBagBottomSheet(BuildContext analyticsContext) {
    final metadata = AnalyticsMetadataProvider.of(analyticsContext);
    showAzzasBottomSheet(
      context: analyticsContext,
      builder: (context) {
        return ValueListenableBuilder<OrderFormItem>(
          valueListenable: activeItemNotifier,
          builder: (context, activeItem, _) {
            return AnalyticsMetadataProvider(
              metadata: metadata,
              child: PdpAddToBagBottomSheet(
                item: widget.product,
                selectedSize: activeItem,
                controller: widget.pdpCubit,
                navigateToBag: widget.navigateToBag,
                useItem: true,
                useProductImageUrl: true,
              ),
            );
          },
        );
      },
      vsync: this,
    );
  }

  @override
  Widget build(BuildContext buildContext) {
    return BlocBuilder<PdpCubit, PdpState>(
      bloc: widget.pdpCubit,
      builder: (context, state) {
        return Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            InkWell(
              onTap: () {
                Modular.to.pushNamed('/pdp', arguments: widget.product);
                _eventDispatcher.logSelectItem(
                  itemListName: 'Complete o look',
                  product: widget.product,
                  index: widget.index,
                );
              },
              child: AzzasProductImage(
                imageUrl: widget.product.fullLookProductImage,
                heigth: 170,
                width: 130,
              ),
            ),
            SizedBox(width: Tokens.spacing.spacingSmall),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: Tokens.spacing.spacingXSmall),
                  Text(
                    widget.product.productName ?? '',
                    style: Tokens.typography.body.small.smallRegular,
                  ),
                  SizedBox(height: Tokens.spacing.spacingXSmall),
                  Text(widget.product.productFormattedListPrice,
                      style: Tokens.typography.body.extraSmall.extraSmallRegular
                          .copyWith(color: Tokens.colors.typography.medium)),
                  SizedBox(height: Tokens.spacing.spacingSmall),
                  InkWell(
                    onTap: () {
                      chooseSizeBottomSheet();
                    },
                    child: Container(
                      padding: EdgeInsets.all(Tokens.spacing.spacingSmall),
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: Tokens.colors.neutral.medium,
                          width: 1,
                        ),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          ValueListenableBuilder<OrderFormItem>(
                            valueListenable: activeItemNotifier,
                            builder: (context, activeItem, _) {
                              return Text(
                                'Tamanho: ${activeItem.itemSize}',
                                style: Tokens.typography.body.extraSmall
                                    .extraSmallRegular,
                              );
                            },
                          ),
                          Icon(
                            Tokens.icons.navigation.down,
                            size: 12,
                          ),
                        ],
                      ),
                    ),
                  ),
                  SizedBox(height: Tokens.spacing.spacingSmall),
                  AzzasButton.link(
                    onPressed: () {
                      addtoBagBottomSheet(buildContext);
                    },
                    style: AzzasButtonStyle(
                      padding: EdgeInsets.only(top: 16),
                    ),
                    trailing: Icon(
                      Tokens.icons.navigation.right,
                      size: 10,
                      color: Tokens.colors.brand.pure,
                    ),
                    child: Text(
                      'Adicionar à sacola',
                      style: Tokens.typography.body.extraSmall.extraSmallRegular
                          .copyWith(color: Tokens.colors.brand.pure),
                    ),
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }
}

extension on Product {
  String get fullLookProductImage {
    final image = items?.first.images
        ?.firstWhereOrNull((image) => image.imageLabel == "11");

    return image?.imageUrl ?? items?.first.images?.first.imageUrl ?? '';
  }
}
