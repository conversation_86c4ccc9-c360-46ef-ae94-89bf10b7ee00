import 'dart:convert';

import 'package:azzas_analytics/events/checkout/payment_events.dart';
import 'package:azzas_analytics/events/navigation/navigation_events.dart';
import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:crisbarros_app_flutter/config/design_tokens/tokens.dart';
import 'package:crisbarros_app_flutter/modules/payment/gift_card/bottom_sheet/bottom_sheet_gift_card.dart';
import 'package:crisbarros_app_flutter/modules/payment/widgets/payment_options/credit_card/credit_card_payment.dart';
import 'package:crisbarros_app_flutter/modules/payment/widgets/payment_options/pix_payment.dart';
import 'package:crisbarros_app_flutter/modules/payment/widgets/select_payment_button.dart';
import 'package:flutter/material.dart';

import '../checking_account/widgets/widgets.dart';

class SelectPaymentPage extends StatefulWidget {
  const SelectPaymentPage({super.key});

  @override
  State<SelectPaymentPage> createState() => _SelectPaymentPageState();
}

class _SelectPaymentPageState extends State<SelectPaymentPage>
    with TickerProviderStateMixin {
  final orderFormCubit = Modular.get<OrderFormCubit>();
  final paymentCubit = Modular.get<PaymentCubit>();
  final eventDispatcher = Modular.get<EventDispatcher>();

  CreditCardInfoData? selectedCard;
  InstallmentOrderForm? selectedInstallmentForCard;
  bool isNewCreditCardSelected = false;

  @override
  void initState() {
    super.initState();
    NavigationEvents.logPageView(local: 'select_payment');
    final firstPayment = orderFormCubit.orderForm.firstSelectedPayment;
    final isFirstPaymentValid = orderFormCubit.orderForm.isCreditCardSelected &&
        firstPayment != null &&
        firstPayment.installments > 0 &&
        firstPayment.accountId.isNotNullOrEmpty == true &&
        firstPayment.paymentSystem.isNotNullOrEmpty == true &&
        firstPayment.bin.isNotNullOrEmpty == true;

    if (isFirstPaymentValid) {
      final selectedCreditCardAccountId =
          orderFormCubit.orderForm.firstSelectedPayment?.accountId;
      final selectedCardAccount = orderFormCubit.orderForm.getAvailableAccounts
          .firstWhereOrNull(
              (card) => card.accountId == selectedCreditCardAccountId);
      final selectedCardInfoData = CreditCardInfoData(
        bin: selectedCard?.bin ?? '',
        cardAccountId: selectedCardAccount?.accountId ?? '',
        isNew: false,
        selectedPaymentSystemId: selectedCardAccount?.paymentSystem ?? '',
      );

      setState(() {
        selectedCard = selectedCardInfoData;
      });
    }
  }

  Future<void> _selectPixPayment() async {
    try {
      paymentCubit.clearCreditCardError();
      await paymentCubit.selectPixPayment();
      selectedCard = null;
      isNewCreditCardSelected = false;
      eventDispatcher.logAddPaymentInfo(
          paymentType: AzzasAnalyticsPaymentType.pix);
    } catch (e) {
      _showAzzasSnackbar(
        message: "Ocorreu um erro ao selecionar o Pix como pagamento",
        isError: true,
      );
    }
  }

  void _showAzzasSnackbar({required String message, required bool isError}) {
    AzzasSnackBar.show(
      context: context,
      message: message,
      backgroundColor:
          isError ? Tokens.colors.error.pure : Tokens.colors.success.pure,
    );
  }

  Future<void> _selectRecurrentCreditCardAsPayment() async {
    try {
      await paymentCubit.selectCreditCardPayment(
        creditCardInfo: selectedCard!,
        installment: selectedInstallmentForCard!,
      );

      if (paymentCubit.isRecurrentCreditCardSelected) {
        AzzasAnalyticsEvents.logSelectContent(
          contentType:
              'pagamento:parcelamento:${selectedInstallmentForCard?.count}',
        );

        eventDispatcher.logAddPaymentInfo(
            paymentType: AzzasAnalyticsPaymentType.creditCard);

        Navigator.of(context).pop();
        Modular.to.pushNamed('/checkout/order_review');
        return;
      }

      return _showAzzasSnackbar(
        message: 'Erro ao selecionar cartão de crédito',
        isError: true,
      );
    } catch (e) {
      _showAzzasSnackbar(
        message: 'Erro ao selecionar cartão de crédito',
        isError: true,
      );
    }
  }

  void _selectNewCreditCard() {
    eventDispatcher.logAddPaymentInfo(paymentType: AzzasAnalyticsPaymentType.creditCard);
    setState(() {
      isNewCreditCardSelected = true;
      selectedCard = null;
    });
  }

  void _selectRecurrentCreditCard({required CreditCardInfoData cardInfo}) {
    eventDispatcher.logAddPaymentInfo(paymentType: AzzasAnalyticsPaymentType.creditCard);
    setState(() {
      isNewCreditCardSelected = false;
      selectedCard = cardInfo;
    });
  }

  void _selectPayment() {
    paymentCubit.clearCreditCardError();
    if (paymentCubit.isPixSelected == true &&
        selectedCard == null &&
        isNewCreditCardSelected == false &&
        paymentCubit.state.selectedPaymentType !=
            SelectedPaymentType.giftCard) {
      return showAzzasBottomSheet(
        context: context,
        builder: (_) {
          return AzzasPixAlertBottomSheet(
            onCloseTap: () {
              Navigator.of(context).pop();
              Modular.to.pushNamed('/checkout/order_review');
            },
            titleStyle: Tokens.typography.headings.small.smallRegular,
            descriptionStyle: Tokens.typography.body.small.smallRegular,
          );
        },
        vsync: this,
      );
    }

    if (selectedCard != null &&
        paymentCubit.state.selectedPaymentType !=
            SelectedPaymentType.giftCard) {
      return _showInstallmentsBottomSheet();
    }

    if (isNewCreditCardSelected == true &&
        paymentCubit.state.selectedPaymentType !=
            SelectedPaymentType.giftCard) {
      Modular.to.pushNamed('/checkout/card-payment-info');
      return;
    }

    if (paymentCubit.isGiftCardSelected == true) {
      Modular.to.pushNamed('/checkout/order_review');
      return;
    }
  }

  void _showInstallmentsBottomSheet() {
    return showAzzasBottomSheet(
      context: context,
      builder: (_) {
        return BlocBuilder<OrderFormCubit, OrderFormState>(
          bloc: orderFormCubit,
          builder: (context, state) {
            final isLoadingOrderForm = state.isLoading;
            final paymentSytemId = selectedCard?.selectedPaymentSystemId;

            if (paymentSytemId == null) {
              throw Exception(
                  "Nenhum paymentSystemId foi identificado para obter as parcelas");
            }

            final availableInstallments =
                paymentCubit.getPaymentInstallmentsForCreditCard(
              paymentSytemId,
            );

            return StatefulBuilder(builder: (context, setState) {
              return AzzasSelectInstallmentsBottomSheet(
                onCloseTap: () {
                  Navigator.of(context).pop();
                },
                titleStyle: Tokens.typography.body.extraLarge.extraLargeRegular,
                isLoading: isLoadingOrderForm,
                showDragBar: true,
                borderRadius: BorderRadius.all(
                  Radius.circular(Tokens.spacing.spacingMedium),
                ),
                installmentsSubtitleTextStyle: Tokens
                    .typography.body.extraSmall.extraSmallRegular
                    .copyWith(
                  color: Tokens.colors.typography.medium,
                ),
                isDisabled: selectedInstallmentForCard == null,
                onSelectInstallment: (installment) {
                  final selectedInstallment =
                      availableInstallments.firstWhereOrNull(
                    (element) =>
                        element.count == installment.count &&
                        element.value == installment.value,
                  );

                  if (selectedInstallment == null) {
                    _showAzzasSnackbar(
                        isError: true, message: 'Parcelamento não encontrado');
                  }

                  setState(() {
                    selectedInstallmentForCard = selectedInstallment;
                  });
                },
                onButtonPressed: () async {
                  await _selectRecurrentCreditCardAsPayment();
                },
                installments: availableInstallments
                    .map(
                      (installment) => installment.toAzzasInstallmentInfoParams(
                        isChecked: installment.equalsTo(
                          selectedInstallmentForCard,
                        ),
                      ),
                    )
                    .toList(),
              );
            });
          },
        );
      },
      vsync: this,
    );
  }

  void _showAddVounchersBottomSheet() {
    return showAzzasBottomSheet(
      isScrollControlled: true,
      context: context,
      builder: (_) {
        return BottomSheetGiftCard(
          paymentCubit: paymentCubit,
        );
      },
      vsync: this,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: CustomScrollView(
          slivers: [
            AzzasSmallAppBar(
              title: '',
              pinned: true,
              toolbarHeight: Tokens.spacing.spacingXUltraLarge,
              onBackButton: () {
                if (paymentCubit.state.selectedPaymentType != null) {
                  Navigator.of(context).pop();
                } else {
                  Modular.to.pushNamed('/bag');
                }
              },
            ),
            BlocBuilder<OrderFormCubit, OrderFormState>(
              bloc: orderFormCubit,
              builder: (context, state) {
                return SliverFillRemaining(
                  hasScrollBody: false,
                  child: Padding(
                    padding: EdgeInsets.symmetric(
                      horizontal: Tokens.spacing.spacingMedium,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(
                          height: Tokens.spacing.spacingLarge,
                        ),
                        Text(
                          'Pagamento',
                          style: Tokens
                              .typography.body.extraLarge.extraLargeRegular,
                        ),
                        SizedBox(
                          height: Tokens.spacing.spacingLarge,
                        ),
                        BlocBuilder<PaymentCubit, PaymentState>(
                          bloc: paymentCubit,
                          builder: (context, state) {
                            if (state.creditCardErrorInfo != null) {
                              return Padding(
                                padding: EdgeInsets.symmetric(
                                  vertical: Tokens.spacing.spacingSmall,
                                ),
                                child: AzzasFeedbackCard(
                                  card: AzzasFeedbackCardParams(
                                    backgroundColor: Tokens.colors.error.light,
                                    textStyle: Tokens
                                        .typography.body.medium.mediumRegular
                                        .copyWith(
                                            color: Tokens.colors.error.pure),
                                    title:
                                        "Não foi possível finalizar sua compra. Por favor, escolha outra forma de pagamento ou tente novamente.",
                                    type: AzzasFeedbackCardType.error,
                                  ),
                                ),
                              );
                            }
                            return SizedBox.shrink();
                          },
                        ),
                        PaymentMethodCard(
                          paymentCubit: paymentCubit,
                        ),
                        SizedBox(
                          height: Tokens.spacing.spacingSmall,
                        ),
                        if (state.orderForm?.acceptsPixPayment == true)
                          PixPaymentOption(
                            paymentCubit: paymentCubit,
                            isPixSelected: paymentCubit.isPixSelected &&
                                selectedCard == null &&
                                isNewCreditCardSelected == false,
                            onTapSelectPix: _selectPixPayment,
                          ),
                        if (state.orderForm?.acceptsCreditCardPayment ==
                            true) ...[
                          SizedBox(
                            height: Tokens.spacing.spacingSmall,
                          ),
                          CreditCardPaymentOption(
                            paymentCubit: paymentCubit,
                            currentSelectedCard: selectedCard,
                            isNewCreditCardSelected:
                                isNewCreditCardSelected && selectedCard == null,
                            onNewCreditCardSelected: () {


                              _selectNewCreditCard();
                            },
                            onSelectCard: (card) => _selectRecurrentCreditCard(
                              cardInfo: card,
                            ),
                          ),
                        ],
                        SizedBox(
                          height: Tokens.spacing.spacingSmall,
                        ),
                        Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: Tokens.spacing.spacingSmall,
                            vertical: Tokens.spacing.spacingXSmall,
                          ),
                          decoration: BoxDecoration(
                            color: Tokens.colors.neutral.medium,
                            border: Border.all(
                              color: Tokens.colors.neutral.medium,
                              width: 0.5,
                            ),
                          ),
                          width: double.infinity,
                          child: Center(
                            child: Text(
                              'Não se preocupe, você ainda não será cobrado',
                              style: Tokens
                                  .typography.body.extraSmall.extraSmallRegular,
                            ),
                          ),
                        ),
                        SizedBox(
                          height: Tokens.spacing.spacingXXLarge,
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              textScaler: TextScaleHelper.clampTextScale(
                                  context,
                                  maxScaleFactor: 1.35),
                              'Resumo do pedido',
                              style: Tokens
                                  .typography.body.extraSmall.extraSmallRegular
                                  .copyWith(
                                color: Tokens.colors.neutral.dark,
                              ),
                            ),
                            const Spacer(),
                            Text(
                              textScaler: TextScaleHelper.clampTextScale(
                                  context,
                                  maxScaleFactor: 1.35),
                              state.orderForm?.getTotalValueFormatted ?? '',
                              style: Tokens.typography.body.small.smallRegular,
                            ),
                          ],
                        ),
                        SizedBox(
                          height: Tokens.spacing.spacingMedium,
                        ),
                        BlocBuilder<PaymentCubit, PaymentState>(
                          bloc: paymentCubit,
                          builder: (context, paymentState) {
                            return SelectPaymentButton(
                              text: paymentCubit.state.selectedPaymentType ==
                                          SelectedPaymentType.giftCard &&
                                      paymentCubit.isGiftCardSelected
                                  ? 'Pagar com saldo'
                                  : null,
                              selectedCreditCard: selectedCard,
                              selectedInstallmentForCard:
                                  selectedInstallmentForCard,
                              onSelectPayment: _selectPayment,
                              isLoading: state.isLoading,
                              isDisabled: !(paymentCubit.isPixSelected ||
                                  (paymentCubit.state.selectedPaymentType ==
                                          SelectedPaymentType.giftCard &&
                                      paymentCubit.isGiftCardSelected) ||
                                  selectedCard != null ||
                                  isNewCreditCardSelected == true),
                            );
                          },
                        ),
                        SizedBox(
                          height: Tokens.spacing.spacingMedium,
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}
