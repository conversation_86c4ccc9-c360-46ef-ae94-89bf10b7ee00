import 'package:azzas_analytics/events/navigation/navigation_events.dart';
import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:crisbarros_app_flutter/config/services/ab_testing/firebase_ab_testing_service.dart';
import 'package:crisbarros_app_flutter/config/services/ab_testing/navigation/checkout_navigation_manager.dart';
import 'package:crisbarros_app_flutter/modules/main/main_page.dart';
import 'package:diacritic/diacritic.dart';
import 'package:flutter/material.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  final homeCubit = Modular.get<HomeCubit>();
  final _eventDispatcher = Modular.get<EventDispatcher>();
  final orderFormCubit = Modular.get<OrderFormCubit>();

  @override
  void initState() {
    super.initState();
    _setNewCheckoutUTMI();
    NavigationEvents.logPageView(local: 'home');
  }

  Future<void> _setNewCheckoutUTMI() async {
    try {
      if (orderFormCubit.orderForm.marketingData?.marketingTags?.isNotEmpty ==
          true) {
        return;
      }

      final isNewCheckout = FirebaseABTestingService.instance
          .isExperimentEnabled(ABTestingConfig.experimentCheckoutFlow);

      final utmiCampaignHelper =
          await Modular.get<UtmiCampaignHelper>().getDeviceTags();

      final marketingTags = [
        ...utmiCampaignHelper,
        'new_checkout:$isNewCheckout'
      ];
      final marketingData =
          OrderFormMarketingData(marketingTags: marketingTags);

      await Modular.get<OrderCheckoutHandler>().addMarketingTags(
        marketingData: marketingData,
      );
      debugPrint('✅ UTMI de novo checkout definido com sucesso');
    } catch (e) {
      debugPrint('❌ Erro ao definir o UTMI de novo checkout: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocBuilder<HomeCubit, HomeState>(
        bloc: homeCubit,
        builder: (context, state) {
          return CustomScrollView(
            slivers: [
              // SliverAppBar(
              //   floating: true,
              //   surfaceTintColor: Tokens.colors.neutral.pure,
              //   elevation: 0,
              //   flexibleSpace: GestureDetector(
              //     onTap: () {
              //       Modular.to.pushNamed('/components');
              //     },
              //     child: const HomeHeader(
              //       imageHeight: 12,
              //       showRigthIcon: false,
              //     ),
              //   ),
              // ),
              SliverList(
                delegate: SliverChildBuilderDelegate(
                  (context, index) {
                    final component = state.components[index];
                    return buildComponentWidget(component, index);
                  },
                  childCount: state.components.length,
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  void _onSearchTap() async {
    MainPage.goMenu();
  }

  final region = "home";
  String creativeName(BannerNameGA4? value) => value?.creativeName ?? '';
  String promotionName(BannerNameGA4? value) => value?.promotionName ?? '';

  Widget buildComponentWidget(dynamic component, int index) {
    switch (component.runtimeType) {
      // ====================================================
      // Categories Mosaic Component
      // ====================================================
      case CategoriesMosaicCmsComponent:
        final categoriesMosaicComponent =
            component as CategoriesMosaicCmsComponent;
        return CategoriesMosaicCmsWidget.fromCategoriesMosaicCmsComponent(
          categoriesMosaicComponent,
          onItemSelected: (categoryItem, itemIndex) async {
            AzzasAnalyticsEvents.logSelectContent(
                contentType:
                    'home:categorias-em-destaque:${categoryItem.bannerNameGA4?.creativeName ?? ''}');
          },
        );

      // ====================================================
      // Spot Product Component
      // ====================================================
      case SpotProductCmsComponent:
        final spotComponent = component as SpotProductCmsComponent;

        return SpotProductCmsWidget.fromSpotProductCmsComponent(
          spotComponent,
        );

      // ====================================================
      // Content Area With Sign Component
      // ====================================================
      case ContentAreaWithSignCmsComponent:
        final contentAreaWithSignComponent =
            component as ContentAreaWithSignCmsComponent;
        return ContentAreaCompleteCmsWidget.fromComponent(
          contentAreaWithSignComponent,
          CheckoutNavigationManager.isNewCheckout,
        );

      // ====================================================
      // Product Mosaic Component
      // ====================================================
      case ProductMosaicCmsComponent:
        final productMosaicComponent = component as ProductMosaicCmsComponent;
        return ProductsMosaicCmsWidget.fromComponent(productMosaicComponent);

      // ====================================================
      // Media List Banner Component
      // ====================================================
      case MediaListBannerComponent:
        final mediaListBannerComponent = component as MediaListBannerComponent;
        const label = 'home-media-list-banner';

        return MediaListBannerCompleteCmsWidget.fromComponent(
          mediaListBannerComponent,
          onSearchTap: _onSearchTap,
          onVisible: (mediaItem, bannerIndex) async {
            _eventDispatcher.logViewPromotion(
              region: region,
              creativeName: creativeName(mediaItem.bannerNameGA4),
              creativeSlot:
                  '$label:${index.toString()}:${bannerIndex.toString()}',
              promotionName: promotionName(mediaItem.bannerNameGA4),
              items: null,
            );
          },
          onItemSelected: (mediaItem, bannerIndex) async {
            _eventDispatcher.logSelectPromotion(
              region: region,
              creativeSlot:
                  '$label:${index.toString()}:${bannerIndex.toString()}',
              creativeName: mediaItem.bannerNameGA4?.creativeName ?? '',
              promotionName: promotionName(mediaItem.bannerNameGA4),
              items: null,
            );
          },
        );

      // ====================================================
      // Personal Showcase Component
      // ====================================================
      case PersonalShowcaseCmsComponent:
        final personalShowcaseComponent =
            component as PersonalShowcaseCmsComponent;
        return PersonalShowcaseCompleteCmsWidget.fromComponent(
          personalShowcaseComponent,
        );

      // ====================================================
      // Showcase Banner Carrousel Component
      // ====================================================
      case ShowcaseBannerCarrouselCmsComponent:
        final showcaseBannerCarrouselComponent =
            component as ShowcaseBannerCarrouselCmsComponent;
        const label = 'home-showcase-banner-carrousel';

        return ShowcaseBannerListCmsWidget.fromComponent(
          showcaseBannerCarrouselComponent,
          onVisible: (bannerItem, itemIndex) async {
            
            _eventDispatcher.logViewPromotion(
              region: region,
              creativeSlot:
                  '$label:${index.toString()}:${itemIndex.toString()}',
              creativeName: bannerItem.bannerNameGA4?.creativeName ?? '',
              promotionName: promotionName(bannerItem.bannerNameGA4),
              items: null,
            );
          },
          onItemSelected: (bannerItem, itemIndex) async {
            _eventDispatcher.logSelectPromotion(
              region: region,
              creativeSlot:
                  '$label:${index.toString()}:${itemIndex.toString()}',
              creativeName: bannerItem.bannerNameGA4?.creativeName ?? '',
              promotionName: promotionName(bannerItem.bannerNameGA4),
              items: null,
            );

          },
        );

      // ====================================================
      // Video Banner Component
      // ====================================================
      case VideoBannerComponent:
        final videoBannerComponent = component as VideoBannerComponent;
        const label = 'home-video-banner';
        return VideoBannerCompleteCmsWidget.fromComponent(
          videoBannerComponent,
          onVisible: () async {
            _eventDispatcher.logSelectPromotion(
              region: region,
              creativeSlot: '$label:${index.toString()}:${1.toString()}',
              creativeName:
                  videoBannerComponent.bannerNameGA4?.creativeName ?? '',
              promotionName: promotionName(videoBannerComponent.bannerNameGA4),
              items: null,
            );
          },
          onCallToActionCallback: () async {
            _eventDispatcher.logSelectPromotion(
              region: region,
              creativeSlot: '$label:${index.toString()}:${1.toString()}',
              creativeName:
                  videoBannerComponent.bannerNameGA4?.creativeName ?? '',
              promotionName: promotionName(videoBannerComponent.bannerNameGA4),
              items: null,
            );
           
          },
        );

      default:
        return const SizedBox.shrink();
    }
  }

  String sanitize(String input) {
    final withoutDiacritics = removeDiacritics(input);
    return withoutDiacritics
        .toUpperCase()
        .replaceAll(RegExp(r'[^A-Z0-9\s]'), '')
        .trim()
        .toLowerCase();
  }
}
