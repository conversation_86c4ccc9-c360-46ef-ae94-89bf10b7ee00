import 'dart:math';
import 'package:azzas_analytics/events/category/pdc_events.dart';
import 'package:azzas_analytics/events/home/<USER>';
import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:azzas_app_commons/modules/pdc/pdc_state.dart';
import 'package:crisbarros_app_flutter/config/design_tokens/tokens.dart';
import 'package:crisbarros_app_flutter/config/utils/analytics_items.dart';
import 'package:crisbarros_app_flutter/modules/pdc/widgets/category_filter_bottom_sheet.dart';
import 'package:crisbarros_app_flutter/modules/pdc/widgets/color_filter_bottom_sheet.dart';
import 'package:crisbarros_app_flutter/modules/pdc/widgets/price_filter_bottom_sheet.dart';
import 'package:crisbarros_app_flutter/modules/pdc/widgets/size_filter_bottom_sheet.dart';
import 'package:crisbarros_app_flutter/modules/pdc/widgets/sort_items_bottom_sheet.dart';
import 'package:flutter/material.dart';

class PdcPage extends StatefulWidget {
  const PdcPage({super.key, required this.search});

  final Search search;

  @override
  State<PdcPage> createState() => _PdcPageState();
}

class _PdcPageState extends State<PdcPage> with TickerProviderStateMixin {
  List<String> categoriesSelected = [];
  List<String> sizeSelected = [];
  static const _minSliderPrice = 500.0;
  static const _maxSliderPrice = 1400.0;
  double currentMinSliderPrice = _minSliderPrice;
  double currentMaxSliderPrice = _maxSliderPrice;
  List<String> colorsSelected = [];
  SortOption? sortOption;

  final scrollController = ScrollController();
  final cubit = PdcCubit();
  final _eventDispatcher = Modular.get<EventDispatcher>();

  @override
  void initState() {
    super.initState();
    cubit.getProducts(search: widget.search);
    if (widget.search.query != null) {
      SearchEvents.logViewSearchResults(searchTerm: widget.search.query ?? '');
    }

    scrollController.addListener(() {
      if (scrollController.position.pixels >=
          scrollController.position.maxScrollExtent) {
        cubit.getMoreProducts();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final activeFilter =
        categoriesSelected.isNotEmpty || sizeSelected.isNotEmpty;
    final totalFilters = categoriesSelected.length + sizeSelected.length;
    return Scaffold(
      body: SafeArea(
        child: BlocBuilder<PdcCubit, PdcState>(
          bloc: cubit,
          builder: (context, state) {
            if (!state.isLoading &&
                state.products != null &&
                state.products!.isNotEmpty) {
              _eventDispatcher.logViewItemList(
                  itemListName: widget.search.title,
                  products: state.products ?? [],
                  region: 'pdc');
            }
            return CustomScrollView(
              controller: scrollController,
              slivers: [
                AzzasSmallAppBar(
                  title: widget.search.title ?? "",
                  pinned: true,
                  toolbarHeight: 83,
                  supportText: '${state.totalProducts} resultados',
                  onBackButton: () {
                    Navigator.of(context).pop();
                  },
                  actions: [
                    Padding(
                      padding: const EdgeInsets.only(right: 24),
                      child: GestureDetector(
                        onTap: () {
                          Navigator.of(context).pop();
                        },
                        child: Icon(
                          Tokens.icons.action.search,
                          size: 32,
                        ),
                      ),
                    ),
                  ],
                ),
                SliverToBoxAdapter(
                  child: SizedBox(
                    height: Tokens.spacing.spacingSmall,
                  ),
                ),
                SliverToBoxAdapter(
                  child: SizedBox(
                    height: 32,
                    child: ListView(
                      shrinkWrap: true,
                      scrollDirection: Axis.horizontal,
                      padding: EdgeInsets.symmetric(
                        horizontal: Tokens.spacing.spacingMedium,
                      ),
                      children: [
                        AzzasFilterButton(
                          onPressed: () async {
                            await PDCEvents.logSelectFilter(
                              local: 'pdc',
                              category: 'categoria',
                              action: 'filtros',
                            );
                            Modular.to.pushNamed(
                              '/pdc/filter-management',
                              arguments: cubit,
                            );
                          },
                          label: 'Filtros',
                          isActive: activeFilter,
                          iconType: activeFilter
                              ? AzzasFilterButtonIcon.badge
                              : AzzasFilterButtonIcon.rightIcon,
                          icon: Tokens.icons.filter.setting,
                          badgeText: totalFilters.toString(),
                        ),
                        SizedBox(width: Tokens.spacing.spacingXXSmall),
                        AzzasFilterButton(
                          onPressed: () async {
                            await PDCEvents.logSelectSort(
                              local: 'pdc',
                              action: state.sort?.name ?? 'sem_ordenacao',
                            );
                            _onSortTap(
                              state.currentSearch,
                              state.sort,
                            );
                          },
                          label: 'Ordenar',
                          isActive: state.sort != null,
                          iconType: AzzasFilterButtonIcon.rightIcon,
                        ),
                        SizedBox(width: Tokens.spacing.spacingXXSmall),
                        AzzasFilterButton(
                          onPressed: () {
                            const filter = FilterType.categories;
                            _onCategoryTap(
                              cubit.getFiltersValuesByType(filter),
                              cubit.getSelectedFiltersValuesByType(filter),
                            );
                          },
                          label: 'Categorias',
                          isActive: cubit.hasFilterSelected(
                            filterType: FilterType.categories,
                          ),
                          iconType: cubit.hasFilterSelected(
                            filterType: FilterType.categories,
                          )
                              ? AzzasFilterButtonIcon.badge
                              : AzzasFilterButtonIcon.rightIcon,
                          badgeText: cubit
                              .getFilterCount(filterType: FilterType.categories)
                              .toString(),
                        ),
                        SizedBox(width: Tokens.spacing.spacingXXSmall),
                        AzzasFilterButton(
                          onPressed: () {
                            const filter = FilterType.sizes;
                            _onSizeTap(
                              cubit.getFiltersValuesByType(filter),
                              cubit.getSelectedFiltersValuesByType(filter),
                            );
                          },
                          label: 'Tamanho',
                          isActive: cubit.hasFilterSelected(
                            filterType: FilterType.sizes,
                          ),
                          iconType: cubit.hasFilterSelected(
                            filterType: FilterType.sizes,
                          )
                              ? AzzasFilterButtonIcon.badge
                              : AzzasFilterButtonIcon.rightIcon,
                          badgeText: cubit
                              .getFilterCount(filterType: FilterType.sizes)
                              .toString(),
                        ),
                        SizedBox(width: Tokens.spacing.spacingXXSmall),
                        AzzasFilterButton(
                          onPressed: () {
                            const filter = FilterType.colors;
                            _onColorTap(
                              cubit.getFiltersValuesByType(filter),
                              cubit.getSelectedFiltersValuesByType(filter),
                            );
                          },
                          label: 'Cor',
                          isActive: cubit.hasFilterSelected(
                            filterType: FilterType.colors,
                          ),
                          iconType: cubit.hasFilterSelected(
                            filterType: FilterType.colors,
                          )
                              ? AzzasFilterButtonIcon.badge
                              : AzzasFilterButtonIcon.rightIcon,
                          badgeText: cubit
                              .getFilterCount(filterType: FilterType.colors)
                              .toString(),
                        ),
                        SizedBox(width: Tokens.spacing.spacingXXSmall),
                        AzzasFilterButton(
                          onPressed: () {
                            _onPriceTap(
                              state.currentSearch,
                              state.filterList[FilterType.price] ?? [],
                              state.filterPrice,
                              state.filterPriceSelected,
                            );
                          },
                          label: 'Preço',
                          isActive: cubit.hasPriceFilter(),
                          iconType: AzzasFilterButtonIcon.rightIcon,
                        ),
                      ],
                    ),
                  ),
                ),
                SliverToBoxAdapter(
                  child: SizedBox(height: Tokens.spacing.spacingXLarge),
                ),
                if (state.isLoading)
                  const SliverToBoxAdapter(child: Center(child: AzzasSpinner()))
                else
                  SliverGrid(
                    gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 2,
                      crossAxisSpacing: 4,
                      mainAxisSpacing: 8,
                      mainAxisExtent:
                          MediaQuery.textScalerOf(context).scale(1.0) < 1.2
                              ? 406
                              : 455,
                    ),
                    delegate: SliverChildBuilderDelegate(
                      (context, index) {
                        final Product product = state.products![index];

                        return AzzasCmsSpotProduct(
                          index: index,
                          heroTag: 'pdc_product_$index',
                          productImageUrl: product.coverImage,
                          productTitle: product.productName ?? "",
                          isOnSale: product.isOnSale,
                          productFullPrice: CurrencyHelper.format(
                            amount: product.getListPrice(),
                          ),
                          productCurrentPrice: CurrencyHelper.format(
                            amount: product.getPrice(),
                          ),
                          imageWidth: double.infinity,
                          imageHeight: 356,
                          product: product,
                          textLeftSpacing: 16,
                          currentPriceTextStyle: Tokens
                              .typography.body.extraSmall.extraSmallCaption
                              .copyWith(
                            color: !product.isOnSale
                                ? Tokens.colors.typography.medium
                                : Tokens.colors.typography.light,
                            fontSize: 12.0,
                            height: 1.25,
                          ),
                          onTap: () async {
                            final item = buildItemFromProduct(product);
                            await PDCEvents.logSelectProduct(
                              item: item,
                              itemListId: 'pdc_product_list',
                              itemListName: 'Lista de Produtos PDC',
                            );
                            Modular.to.pushNamed('/pdp', arguments: product);
                          },
                        );
                      },
                      childCount: state.products?.length,
                    ),
                  ),
                SliverToBoxAdapter(
                  child: SizedBox(height: Tokens.spacing.spacingXLarge),
                ),
                if (state.isLoadingMore)
                  const SliverToBoxAdapter(
                    child: Center(
                      child: AzzasSpinner(),
                    ),
                  ),
                SliverToBoxAdapter(
                  child: SizedBox(height: Tokens.spacing.spacingXLarge),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  void _onCategoryTap(
    List<String> filters,
    List<String> selectedFilters,
  ) {
    showAzzasBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return CategoryFilterBottomSheet(
          listOptions: filters,
          filterTypeSelected: selectedFilters,
          onTap: () {
            Navigator.pop(context);
          },
          onFilterChanged: (_, dynamic value) {
            final filter = cubit.getFilterByName(FilterType.categories, value);
            if (filter == null) return;
            cubit.toggleFilter(filter: filter, type: FilterType.categories);
          },
          onClear: () {
            cubit.cleanFilterByType(FilterType.categories);
          },
          results: 0,
          pdcCubit: cubit,
        );
      },
      vsync: this,
    );
  }

  void _onSizeTap(
    List<String> filters,
    List<String> selectedFilters,
  ) {
    showAzzasBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return SizeFilterBottomSheet(
          listOptions: filters,
          filterTypeSelected: selectedFilters,
          onTap: () {
            Navigator.pop(context);
          },
          onFilterChanged: (_, dynamic value) {
            final filter = cubit.getFilterByName(FilterType.sizes, value);
            if (filter == null) return;
            cubit.toggleFilter(filter: filter, type: FilterType.sizes);
          },
          onClear: () {
            cubit.cleanFilterByType(FilterType.sizes);
          },
          results: 0,
          pdcCubit: cubit,
        );
      },
      vsync: this,
    );
  }

  void _onColorTap(
    List<String> filters,
    List<String> selectedFilters,
  ) {
    showAzzasBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return ColorFilterBottomSheet(
          listOptions: filters,
          filterTypeSelected: selectedFilters,
          onTap: () {
            Navigator.pop(context);
          },
          onFilterChanged: (_, dynamic value) {
            final filter = cubit.getFilterByName(FilterType.colors, value);
            if (filter == null) return;
            cubit.toggleFilter(filter: filter, type: FilterType.colors);
          },
          onClear: () {
            cubit.cleanFilterByType(FilterType.colors);
          },
          pdcCubit: cubit,
          results: 0,
        );
      },
      vsync: this,
    );
  }

  void _onPriceTap(
    Search search,
    List<Filter> filters,
    FilterPrice filterPrice,
    FilterPrice filterPriceSelected,
  ) {
    showAzzasBottomSheet(
      context: context,
      builder: (BuildContext context) {
        double selectedMinValue = filterPriceSelected.minValue == 0
            ? filterPrice.minValue
            : filterPriceSelected.minValue;

        double selectedMaxValue = filterPriceSelected.maxValue == 0
            ? filterPrice.maxValue
            : filterPriceSelected.maxValue;

        final filterMaxValue = filterPrice.maxValue;
        final filterMinValue = filterPrice.minValue;

        final currentMinValue =
            max(min(selectedMinValue, selectedMaxValue), filterMinValue);
        final currentMaxValue =
            min(max(selectedMinValue, selectedMaxValue), filterMaxValue);

        return PriceFilterBottomSheet(
          onTap: () {
            Navigator.pop(context);
          },
          onFilterChanged: (_, dynamic value) {
            value as Map<String, dynamic>;
            final filterPrice = FilterPrice(
              minValue: value['min'],
              maxValue: value['max'],
            );
            cubit.changePrice(filterPrice);
          },
          onFilterChangeEnd: (_, dynamic value) {
            value as Map<String, dynamic>;
            final filterPrice = FilterPrice(
              minValue: value['min'],
              maxValue: value['max'],
            );
            cubit.changePrice(filterPrice);
            cubit.newIntelligentSearch();
          },
          onClear: () {
            cubit.cleanFilterByType(FilterType.price);
          },
          minSliderValue: min(filterMaxValue, filterMinValue),
          maxSliderValue: max(filterMaxValue, filterMinValue),
          currentMinValue: currentMinValue,
          currentMaxValue: currentMaxValue,
          results: 0,
          pdcCubit: cubit,
        );
      },
      vsync: this,
    );
  }

  void _onSortTap(Search search, OrderBy? selectedSortOption) {
    showAzzasBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return SortItemsBottomSheet(
          selectedFilter: SortItemsBottomSheet.sortOptions
              .where((it) => it.orderByName == selectedSortOption?.name)
              .firstOrNull,
          onFilterChanged: (_, SortOption value) {
            Navigator.pop(context);
            final orderBy = OrderBy.fromString(value.orderByName);
            if (orderBy == null) return;
            cubit.changeSort(search, orderBy);
          },
        );
      },
      vsync: this,
    );
  }

  bool _isBelt(Product product) {
    return //
        (product.productClusters?.values.contains("sale-acessorios-cintos") ??
                false) ||
            (product.productClusters?.values.contains("acessorios-cintos") ??
                false);
  }
}
