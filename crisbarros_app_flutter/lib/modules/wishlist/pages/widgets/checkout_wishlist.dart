import 'package:azzas_analytics/events/checkout/bag_events.dart';
import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:crisbarros_app_flutter/config/design_tokens/tokens.dart';
import 'package:flutter/material.dart';
import 'package:visibility_detector/visibility_detector.dart';

class CheckoutWishlist extends StatefulWidget {
  final BagPageCubit bagPageCubit;
  const CheckoutWishlist({super.key, required this.bagPageCubit});

  @override
  State<CheckoutWishlist> createState() => _CheckoutWishlistState();
}

class _CheckoutWishlistState extends State<CheckoutWishlist> {
  @override
  void initState() {
    super.initState();
    widget.bagPageCubit.getWishlistProducts();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<BagPageCubit, BagPageState>(
      bloc: widget.bagPageCubit,
      builder: (context, state) {
        final hasWishlistProducts = state.wishlistProducts.isNotEmpty;

        if (state.isLoadingWishlist) {
          return const _LoadingSkeletonWidget();
        }
        if (hasWishlistProducts && !state.isLoadingWishlist) {
          return _ProductsWidget(
            products: state.wishlistProducts,
            totalProducts: state.totalWishlistProducts,
          );
        }
        return const SizedBox.shrink();
      },
    );
  }
}

class _ProductsWidget extends StatelessWidget {
  final List<Product> products;
  final num totalProducts;
  const _ProductsWidget({
    Key? key,
    required this.products,
    required this.totalProducts,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Padding(
          padding: EdgeInsets.only(
            left: Tokens.spacing.spacingMedium,
            right: Tokens.spacing.spacingMedium,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Seus desejos salvos ($totalProducts)',
                style: Tokens.typography.headings.small.smallRegular.copyWith(
                  color: Tokens.colors.brand.pure,
                ),
              ),
              SizedBox(
                height: Tokens.spacing.spacingXSmall,
              ),
              Text(
                'Leve agora o que você deixou pra depois',
                style: Tokens.typography.body.medium.mediumRegular.copyWith(
                  color: Tokens.colors.typography.medium,
                ),
              ),
              SizedBox(
                height: Tokens.spacing.spacingXSmall,
              ),
              InkWell(
                onTap: () async {
                  await BagEvents.logDiscoverProducts(
                    listName: 'Seus desejos salvos',
                    local: 'sacola',
                  );
                  NavigatorDynamic.call(
                      'plp/productclusterids/1073?titulo=shop roupas');
                },
                child: Row(
                  children: [
                    Text(
                      'Descubra produtos',
                      style:
                          Tokens.typography.body.extraSmall.extraSmallRegular,
                    ),
                    Icon(
                      Tokens.icons.navigation.right,
                      size: 16.0,
                      color: Tokens.colors.typography.medium,
                    ),
                  ],
                ),
              ),
              SizedBox(height: Tokens.spacing.spacingLarge),
            ],
          ),
        ),
        WishlistSpotProducts(
          products: products,
        ),
      ],
    );
  }
}

class WishlistSpotProducts extends StatefulWidget {
  final List<Product> products;

  const WishlistSpotProducts({
    Key? key,
    required this.products,
  }) : super(key: key);

  @override
  State<WishlistSpotProducts> createState() => _WishlistSpotProductsState();
}

class _WishlistSpotProductsState extends State<WishlistSpotProducts> {
  final _eventDispatcher = Modular.get<EventDispatcher>();
  bool _hasDispatchedEvent = false;

  @override
  Widget build(BuildContext context) {
    return VisibilityDetector(
      key: Key('spot_product_carousel_${widget.hashCode}'),
      onVisibilityChanged: (visibilityInfo) {
        if (!mounted || _hasDispatchedEvent) return;
        if (visibilityInfo.visibleFraction > 0.9) {
          if (widget.products.isNotEmpty) {
            _eventDispatcher.logViewItemList(
              itemListName: 'wishlist',
              products: widget.products,
            );
            _hasDispatchedEvent = true;
          }
        }
      },
      child: AzzasSpotProductCarrousel(
        controllerSize: AzzasControllerCarrouselSize.small,
        spotDimension: SpotDimension.small,
        products: widget.products.map((product) => product.toSpotParams()).toList(),
        onTap: (params) {
          //TODO: Repensar essa lógica
          final product = widget.products.firstWhere(
              (element) => element.productName == params.productTitle);

          _eventDispatcher.logSelectItem(
              itemListName: 'wishlist',
              product: product,
              index: widget.products.indexOf(product));

          Modular.to.pushNamed(
            '/pdp',
            arguments: product,
          );
        },
      ),
    );
  }
}

class _LoadingSkeletonWidget extends StatelessWidget {
  const _LoadingSkeletonWidget({Key? key}) : super(key: key);

  static const kCardsLoadingQuantity = 5;
  static const kCardHeight = 360.0;
  static const kListViewHeight = 460.0;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        bottom: Tokens.spacing.spacingMedium,
        left: Tokens.spacing.spacingMedium,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.symmetric(
              vertical: Tokens.spacing.spacingLarge,
            ),
            child: Text(
              'Wishlist',
              style: Tokens.typography.headings.small.smallRegular,
            ),
          ),
          SizedBox(
            height: kListViewHeight,
            child: ListView.separated(
              scrollDirection: Axis.horizontal,
              shrinkWrap: true,
              itemBuilder: (_, index) => const AzzasSpotProductLoadingSkeleton(
                height: kCardHeight,
              ),
              separatorBuilder: (_, __) =>
                  SizedBox(width: Tokens.spacing.spacingXXSmall),
              itemCount: kCardsLoadingQuantity,
            ),
          )
        ],
      ),
    );
  }
}
