import 'package:azzas_analytics/services/analytics_service.dart';
import 'package:azzas_analytics/events/navigation/navigation_events.dart';
import 'package:azzas_app_commons/azzas_app_commons.dart';
import 'package:crisbarros_app_flutter/config/design_tokens/tokens.dart';
import 'package:flutter/material.dart';

class DeliveryAddressFilledPage extends StatefulWidget {
  const DeliveryAddressFilledPage({
    super.key,
    this.overrideFinishFlow,
  });

  final Function()? overrideFinishFlow;

  @override
  State<DeliveryAddressFilledPage> createState() =>
      _DeliveryAddressFilledPageState();
}

class _DeliveryAddressFilledPageState extends State<DeliveryAddressFilledPage> {
  final _formKey = GlobalKey<FormState>();
  final _cepInputController = TextEditingController(text: '');
  final _streetInputController = TextEditingController(text: '');
  final _numberInputController = TextEditingController(text: '');
  final _complementInputController = TextEditingController();
  final _neighborhoodInputController = TextEditingController();
  final TextEditingController _receiverNameInputController =
      TextEditingController(text: '');
  final _orderFormCubit = Modular.get<OrderFormCubit>();
  final _eventDispatcher = Modular.get<EventDispatcher>();

  showTestToast(BuildContext context, {String? message}) {
    final scaffold = ScaffoldMessenger.of(context);
    scaffold.showSnackBar(
      SnackBar(
        content: Text(message ?? ""),
        action: SnackBarAction(
          label: 'fechar',
          onPressed: scaffold.hideCurrentSnackBar,
        ),
      ),
    );
  }

  @override
  void initState() {
    NavigationEvents.logPageView(local: 'delivery_address_filled');
    _cepInputController.text =
        _orderFormCubit.state.orderForm?.shippingData?.address?.formattedCep ??
            '';
    _complementInputController.text =
        _orderFormCubit.state.orderForm?.shippingData?.address?.complement ??
            '';
    _numberInputController.text =
        _orderFormCubit.state.orderForm?.shippingData?.address?.number ?? '';
    _streetInputController.text =
        _orderFormCubit.state.orderForm?.shippingData?.address?.street ?? '';
    _neighborhoodInputController.text =
        _orderFormCubit.state.orderForm?.shippingData?.address?.neighborhood ??
            '';

    final orderFormCubit = Modular.get<OrderFormCubit>();
    final clientProfileData = orderFormCubit.orderForm.clientProfileData;

    _receiverNameInputController.text =
        _orderFormCubit.state.orderForm?.shippingData?.address?.receiverName ??
            clientProfileData?.completeName ??
            '';
    super.initState();
  }

  Future<void> _updateAddress() async {
    final address = _orderFormCubit.orderForm.shippingData?.address;
    try {
      await _orderFormCubit.updateAddressAndDelivery(
          address: address?.copyWith(
        street: _streetInputController.text,
        receiverName: _receiverNameInputController.text,
        complement: _complementInputController.text,
        number: _numberInputController.text,
        neighborhood: _neighborhoodInputController.text,
      ));
    } catch (e) {
      debugPrint('error in updateAddress');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: CustomScrollView(
          slivers: [
            AzzasSmallAppBar(
              title: 'Complete seu endereço',
              pinned: true,
              toolbarHeight: Tokens.spacing.spacingXUltraLarge,
              onBackButton: () {
                Modular.to.pop();
              },
            ),
            SliverFillRemaining(
              hasScrollBody: false,
              child: Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: Tokens.spacing.spacingMedium,
                ),
                child: BlocBuilder<OrderFormCubit, OrderFormState>(
                  bloc: _orderFormCubit,
                  builder: (context, state) {
                    final address = state.orderForm?.shippingData?.address;
                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(
                          height: Tokens.spacing.spacingLarge,
                        ),
                        AzzasCheckoutStepAddressOpen(
                          params: AzzasCheckoutStepAddressOpenParams(
                            formKey: _formKey,
                            cepInputParams: StepAddressInputParams(
                              controller: _cepInputController,
                              enabledField: false,
                              placeholder: 'Cep',
                            ),
                            responsibleNameInputParams: StepAddressInputParams(
                              controller: _receiverNameInputController,
                            ),
                            streetNameInputParams: StepAddressInputParams(
                              controller: _streetInputController,
                            ),
                            numberInputParams: StepAddressInputParams(
                              controller: _numberInputController,
                            ),
                            complementInputParams: StepAddressInputParams(
                              controller: _complementInputController,
                            ),
                            neighborhoodInputParams: StepAddressInputParams(
                              controller: _neighborhoodInputController,
                            ),
                            onTapUseLocation: () {},
                            loadingFinishButton: state.isLoadingDeliveryOptions,
                            onTapFinishButton: () async {
                              if (_formKey.currentState?.validate() == true) {
                                await AnalyticsService.trackEvent(
                                    'select_content', {
                                  'content_type':
                                      'checkout:como-receber-compra:confirmar',
                                });
                                await _updateAddress();

                                _eventDispatcher.logAddShippingInfo();

                                if (widget.overrideFinishFlow != null) {
                                  widget.overrideFinishFlow!();
                                } else {
                                  Modular.to.pushNamed(
                                    '/checkout/order_review',
                                  );
                                }
                                return;
                              }
                              showTestToast(
                                context,
                                message: "Endereço inválido!",
                              );
                            },
                            showLocationButton: false,
                            showSearchButton: false,
                            completeAddressTitle:
                                '${address?.city} - ${address?.state}',
                            finishButtonLabel: 'Confirmar',
                          ),
                        ),
                      ],
                    );
                  },
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
